// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRechargeCredit = "recharge_credits"

// RechargeCredit 用户充值 credit 表
type RechargeCredit struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdateAt     time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	UserID       int64     `gorm:"column:user_id;type:bigint unsigned;not null" json:"user_id"`
	BusinessType int32     `gorm:"column:business_type;type:int unsigned;not null;comment:业务类型" json:"business_type"` // 业务类型
	Balance      int64     `gorm:"column:balance;type:bigint unsigned;not null;comment:余额" json:"balance"`            // 余额
}

// TableName RechargeCredit's table name
func (*RechargeCredit) TableName() string {
	return TableNameRechargeCredit
}
