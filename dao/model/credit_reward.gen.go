// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCreditReward = "credit_reward"

// CreditReward 维护 credit 余额的相关记录
type CreditReward struct {
	ID              int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreateAt        time.Time `gorm:"column:create_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_at"`
	UpdateAt        time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	UserID          int64     `gorm:"column:user_id;type:bigint unsigned;not null" json:"user_id"`
	BusinessType    int32     `gorm:"column:business_type;type:int unsigned;not null" json:"business_type"`
	RechargeBalance int64     `gorm:"column:recharge_balance;type:bigint unsigned;not null" json:"recharge_balance"`
	LastReward      int64     `gorm:"column:last_reward;type:bigint unsigned;not null" json:"last_reward"`
	Remark          string    `gorm:"column:remark;type:text" json:"remark"`
}

// TableName CreditReward's table name
func (*CreditReward) TableName() string {
	return TableNameCreditReward
}
