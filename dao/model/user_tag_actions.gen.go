// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserTagAction = "user_tag_actions"

// UserTagAction 用户货架/分类收藏
type UserTagAction struct {
	ID         int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	UserID     int64     `gorm:"column:user_id;type:bigint;not null;comment:用户ID" json:"user_id"`              // 用户ID
	TagKey     string    `gorm:"column:tag_key;type:varchar(255);not null;comment:tag key" json:"tag_key"`     // tag key
	ActionType string    `gorm:"column:action_type;type:varchar(64);not null;comment:like" json:"action_type"` // like
	CreatedAt  time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName UserTagAction's table name
func (*UserTagAction) TableName() string {
	return TableNameUserTagAction
}
