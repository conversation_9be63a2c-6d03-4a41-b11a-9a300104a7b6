// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStripeLog = "stripe_logs"

// StripeLog stripe回调日志表
type StripeLog struct {
	ID         int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                 // 主键ID
	EventID    string    `gorm:"column:event_id;type:varchar(64);not null;comment:event ID" json:"event_id"`                          // event ID
	EventType  string    `gorm:"column:event_type;type:varchar(64);not null;comment:event type" json:"event_type"`                    // event type
	Info       string    `gorm:"column:info;type:text;comment:JSON" json:"info"`                                                      // JSON
	CreateTime time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName StripeLog's table name
func (*StripeLog) TableName() string {
	return TableNameStripeLog
}
