// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameFeedTag = "feed_tag"

// FeedTag feed标签配置表
type FeedTag struct {
	ID         int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	TagKey     string    `gorm:"column:tag_key;type:varchar(255);default:tag_" json:"tag_key"`
	TagName    string    `gorm:"column:tag_name;type:varchar(255)" json:"tag_name"`
	TagType    string    `gorm:"column:tag_type;type:varchar(50);not null;comment:tag类型" json:"tag_type"`                              // tag类型
	Status     int32     `gorm:"column:status;type:tinyint;not null;default:1;comment:状态: -1-失效, 1-正常" json:"status"`                  // 状态: -1-失效, 1-正常
	CreateTime time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	Top        int32     `gorm:"column:top;type:int;not null;comment:置顶索引: 0-未置顶, 正整数表示置顶顺序" json:"top"`                               // 置顶索引: 0-未置顶, 正整数表示置顶顺序
	TagLevel   int32     `gorm:"column:tag_level;type:tinyint;not null;default:1;comment:标签级别" json:"tag_level"`                       // 标签级别
}

// TableName FeedTag's table name
func (*FeedTag) TableName() string {
	return TableNameFeedTag
}
