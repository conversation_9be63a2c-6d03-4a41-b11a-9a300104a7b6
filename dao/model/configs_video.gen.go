// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameConfigsVideo = "configs_video"

// ConfigsVideo 视频模版表
type ConfigsVideo struct {
	ID                int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	GenerateType      string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频模版类型" json:"generate_type"`                                   // 视频模版类型
	Thirdparty        string    `gorm:"column:thirdparty;type:varchar(255);not null;comment:调用的第三方平台" json:"thirdparty"`                                       // 调用的第三方平台
	InferParams       string    `gorm:"column:infer_params;type:mediumtext;comment:生成相关推理参数" json:"infer_params"`                                              // 生成相关推理参数
	Prompt            string    `gorm:"column:prompt;type:text;comment:视频生成提示词" json:"prompt"`                                                                 // 视频生成提示词
	NegativePrompt    string    `gorm:"column:negative_prompt;type:text;comment:视频生成反向提示词" json:"negative_prompt"`                                             // 视频生成反向提示词
	CfgScale          float32   `gorm:"column:cfg_scale;type:float;not null;default:0.5;comment:视频生成自由度，取值范围闭区间[0,1]" json:"cfg_scale"`                        // 视频生成自由度，取值范围闭区间[0,1]
	Status            int32     `gorm:"column:status;type:tinyint;not null;comment:-1:隐藏 1:展示" json:"status"`                                                  // -1:隐藏 1:展示
	EditStatus        int32     `gorm:"column:edit_status;type:tinyint;not null;comment:1:已同步 2:新增 3:有修改" json:"edit_status"`                                  // 1:已同步 2:新增 3:有修改
	Title             string    `gorm:"column:title;type:varchar(255);not null;comment:视频模版标题，也是多语言key" json:"title"`                                          // 视频模版标题，也是多语言key
	Image             string    `gorm:"column:image;type:varchar(255);not null;comment:视频模板封面图URL" json:"image"`                                               // 视频模板封面图URL
	ImageMedium       string    `gorm:"column:image_medium;type:varchar(255);not null;comment:视频模板封面图URL，中等分辨率" json:"image_medium"`                           // 视频模板封面图URL，中等分辨率
	ImageLow          string    `gorm:"column:image_low;type:varchar(255);not null;comment:视频模板封面图URL，低分辨率" json:"image_low"`                                  // 视频模板封面图URL，低分辨率
	Video             string    `gorm:"column:video;type:varchar(255);not null;comment:视频模板封面视频URL" json:"video"`                                              // 视频模板封面视频URL
	VideoMedium       string    `gorm:"column:video_medium;type:varchar(255);not null;comment:视频模板封面视频URL，中等分辨率" json:"video_medium"`                          // 视频模板封面视频URL，中等分辨率
	VideoLow          string    `gorm:"column:video_low;type:varchar(255);not null;comment:视频模板封面视频URL，低分辨率" json:"video_low"`                                 // 视频模板封面视频URL，低分辨率
	VideoWidth        int32     `gorm:"column:video_width;type:int;not null;comment:视频宽度" json:"video_width"`                                                  // 视频宽度
	VideoHeight       int32     `gorm:"column:video_height;type:int;not null;comment:视频高度" json:"video_height"`                                                // 视频高度
	Blurhash          string    `gorm:"column:blurhash;type:varchar(255);not null;comment:视频加载占位图" json:"blurhash"`                                            // 视频加载占位图
	Autoplay          int32     `gorm:"column:autoplay;type:tinyint;not null;comment:是否自动播放 -1:否 1:是" json:"autoplay"`                                         // 是否自动播放 -1:否 1:是
	HideInReviewing   int32     `gorm:"column:hide_in_reviewing;type:tinyint;not null;comment:是否在审核时隐藏 -1:否 1:是" json:"hide_in_reviewing"`                     // 是否在审核时隐藏 -1:否 1:是
	FreeTrial         int32     `gorm:"column:free_trial;type:tinyint;not null;comment:是否允许免费试用 -1:否 1:是" json:"free_trial"`                                   // 是否允许免费试用 -1:否 1:是
	DevPublishTime    time.Time `gorm:"column:dev_publish_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:模板dev环境发布时间" json:"dev_publish_time"` // 模板dev环境发布时间
	PublishTime       time.Time `gorm:"column:publish_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:模板发布时间" json:"publish_time"`              // 模板发布时间
	CreateTime        time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime        time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`
	VideoType         string    `gorm:"column:video_type;type:varchar(255);not null;comment:视频类型" json:"video_type"`                     // 视频类型
	LikeCount         int64     `gorm:"column:like_count;type:bigint;not null;comment:点赞数量" json:"like_count"`                           // 点赞数量
	BaseLikeCount     int64     `gorm:"column:base_like_count;type:bigint;not null;comment:基础点赞数量" json:"base_like_count"`               // 基础点赞数量
	ShareCount        int64     `gorm:"column:share_count;type:bigint;not null;comment:分享数量" json:"share_count"`                         // 分享数量
	AspectRatio       string    `gorm:"column:aspect_ratio;type:varchar(32);not null;comment:长宽比" json:"aspect_ratio"`                   // 长宽比
	Id2videoImageList string    `gorm:"column:id2video_image_list;type:text;comment:id生视频的默认图片" json:"id2video_image_list"`              // id生视频的默认图片
	PretreatType      string    `gorm:"column:pretreat_type;type:varchar(120);comment:预处理类型" json:"pretreat_type"`                       // 预处理类型
	PretreatParams    string    `gorm:"column:pretreat_params;type:mediumtext;comment:预处理参数" json:"pretreat_params"`                     // 预处理参数
	ClipType          string    `gorm:"column:clip_type;type:varchar(120);not null;comment:裁剪类型" json:"clip_type"`                       // 裁剪类型
	MasterTemplate    string    `gorm:"column:master_template;type:varchar(255);not null;comment:用于变种模板的源头" json:"master_template"`      // 用于变种模板的源头
	AbParams          string    `gorm:"column:ab_params;type:text;comment:AB测试参数" json:"ab_params"`                                      // AB测试参数
	TemplateLevel     int32     `gorm:"column:template_level;type:tinyint;not null;default:1;comment:模板等级" json:"template_level"`        // 模板等级
	ManuallyPushToTop int32     `gorm:"column:manually_push_to_top;type:tinyint;not null;comment:1:手动推送到顶部" json:"manually_push_to_top"` // 1:手动推送到顶部
}

// TableName ConfigsVideo's table name
func (*ConfigsVideo) TableName() string {
	return TableNameConfigsVideo
}
