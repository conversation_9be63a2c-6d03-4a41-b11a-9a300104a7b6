// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSubscribeLog = "subscribe_logs"

// SubscribeLog 订阅回调日志表
type SubscribeLog struct {
	ID                    int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                     // 主键ID
	TraceID               string    `gorm:"column:trace_id;type:varchar(64);not null;comment:追踪ID" json:"trace_id"`                                  // 追踪ID
	NotificationType      string    `gorm:"column:notification_type;type:varchar(64);not null;comment:通知类型" json:"notification_type"`                // 通知类型
	ProductID             string    `gorm:"column:product_id;type:varchar(128);not null;comment:产品ID" json:"product_id"`                             // 产品ID
	TransactionID         string    `gorm:"column:transaction_id;type:varchar(128);not null;comment:交易ID" json:"transaction_id"`                     // 交易ID
	OriginalTransactionID string    `gorm:"column:original_transaction_id;type:varchar(128);not null;comment:原始交易ID" json:"original_transaction_id"` // 原始交易ID
	PurchaseDate          time.Time `gorm:"column:purchase_date;type:datetime;comment:购买时间戳" json:"purchase_date"`                                   // 购买时间戳
	ExpiresDate           time.Time `gorm:"column:expires_date;type:datetime;comment:过期时间戳" json:"expires_date"`                                     // 过期时间戳
	PayloadInfo           string    `gorm:"column:payload_info;type:text;comment:载荷信息JSON" json:"payload_info"`                                      // 载荷信息JSON
	TransactionInfo       string    `gorm:"column:transaction_info;type:text;comment:交易信息JSON" json:"transaction_info"`                              // 交易信息JSON
	RenewalInfo           string    `gorm:"column:renewal_info;type:text;comment:续订信息JSON" json:"renewal_info"`                                      // 续订信息JSON
	CreateTime            time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`     // 创建时间
	UpdateTime            time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`     // 更新时间
}

// TableName SubscribeLog's table name
func (*SubscribeLog) TableName() string {
	return TableNameSubscribeLog
}
