// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTemplateImage = "template_image"

// TemplateImage 模版封面图表
type TemplateImage struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	GenerateType string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频模版类型" json:"generate_type"`                  // 视频模版类型
	Image        string    `gorm:"column:image;type:varchar(255);not null;comment:视频模板封面图URL" json:"image"`                              // 视频模板封面图URL
	ImageMedium  string    `gorm:"column:image_medium;type:varchar(255);not null;comment:视频模板封面图URL，中等分辨率" json:"image_medium"`          // 视频模板封面图URL，中等分辨率
	ImageLow     string    `gorm:"column:image_low;type:varchar(255);not null;comment:视频模板封面图URL，低分辨率" json:"image_low"`                 // 视频模板封面图URL，低分辨率
	CreateTime   time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName TemplateImage's table name
func (*TemplateImage) TableName() string {
	return TableNameTemplateImage
}
