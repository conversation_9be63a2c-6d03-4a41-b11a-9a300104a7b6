// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTasksVideo = "tasks_video"

// TasksVideo mapped from table <tasks_video>
type TasksVideo struct {
	ID                  int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ThirdID             string    `gorm:"column:third_id;type:varchar(255);not null;comment:第三方平台任务ID" json:"third_id"`                        // 第三方平台任务ID
	Thirdparty          string    `gorm:"column:thirdparty;type:varchar(255);not null;comment:第三方平台名称" json:"thirdparty"`                      // 第三方平台名称
	APIType             string    `gorm:"column:api_type;type:varchar(255);not null;comment:API调用类型" json:"api_type"`                          // API调用类型
	VideoType           string    `gorm:"column:video_type;type:varchar(32);not null;comment:视频类型" json:"video_type"`                          // 视频类型
	UserID              int64     `gorm:"column:user_id;type:bigint;not null;comment:用户ID" json:"user_id"`                                     // 用户ID
	Priority            int32     `gorm:"column:priority;type:int;not null;comment:优先级，数值越小优先级越高" json:"priority"`                             // 优先级，数值越小优先级越高
	GenerateType        string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频生成类型" json:"generate_type"`                 // 视频生成类型
	Faceid              string    `gorm:"column:faceid;type:varchar(30);not null;comment:用户形象ID" json:"faceid"`                                // 用户形象ID
	Status              int32     `gorm:"column:status;type:smallint;not null;comment:-1:排队 0:默认 1:创建 2:成功 3:失败 4:转存结果进行中 5:取消" json:"status"` // -1:排队 0:默认 1:创建 2:成功 3:失败 4:转存结果进行中 5:取消
	Reason              string    `gorm:"column:reason;type:text;comment:失败原因" json:"reason"`                                                  // 失败原因
	Prompt              string    `gorm:"column:prompt;type:text;comment:提示词" json:"prompt"`                                                   // 提示词
	Image               string    `gorm:"column:image;type:varchar(500);not null;comment:输入图或视频" json:"image"`                                 // 输入图或视频
	OriginResourceID    string    `gorm:"column:origin_resource_id;type:varchar(120);not null;comment:资源id" json:"origin_resource_id"`         // 资源id
	Video               string    `gorm:"column:video;type:varchar(255);not null;comment:生成视频" json:"video"`                                   // 生成视频
	CoinInfo            string    `gorm:"column:coin_info;type:varchar(255);not null;default:{};comment:金币信息" json:"coin_info"`                // 金币信息
	ExtInfo             string    `gorm:"column:ext_info;type:varchar(255);not null;default:{};comment:扩展信息" json:"ext_info"`                  // 扩展信息
	CreateTime          time.Time `gorm:"column:create_time;type:timestamp(6);not null;default:CURRENT_TIMESTAMP(6)" json:"create_time"`
	UpdateTime          time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`
	Duration            float64   `gorm:"column:duration;type:double;not null" json:"duration"`
	QueueDuration       float64   `gorm:"column:queue_duration;type:double;not null;comment:优先级队列排队耗时" json:"queue_duration"`                                      // 优先级队列排队耗时
	CreateDuration      float64   `gorm:"column:create_duration;type:double;not null;comment:gpu创建任务耗时" json:"create_duration"`                                    // gpu创建任务耗时
	InferDuration       float64   `gorm:"column:infer_duration;type:double;not null;comment:gpu任务执行耗时" json:"infer_duration"`                                      // gpu任务执行耗时
	ResaveDuration      float64   `gorm:"column:resave_duration;type:double;not null;comment:转存视频耗时" json:"resave_duration"`                                       // 转存视频耗时
	RuntimeAbParams     string    `gorm:"column:runtime_ab_params;type:text;comment:运行AB参数" json:"runtime_ab_params"`                                              // 运行AB参数
	GpuBaseURL          string    `gorm:"column:gpu_base_url;type:varchar(255);not null;comment:GPU服务基础URL" json:"gpu_base_url"`                                   // GPU服务基础URL
	FixedCreateTime     time.Time `gorm:"column:fixed_create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:任务创建时间(不会变)" json:"fixed_create_time"` // 任务创建时间(不会变)
	WaitQueueDuration   float64   `gorm:"column:wait_queue_duration;type:double;not null;comment:购物车时间耗时" json:"wait_queue_duration"`                              // 购物车时间耗时
	VideoExpectDuration string    `gorm:"column:video_expect_duration;type:varchar(50);not null;comment:视频预期生成时长" json:"video_expect_duration"`                    // 视频预期生成时长
	Body2video          string    `gorm:"column:body2video;type:text;comment:json信息" json:"body2video"`                                                            // json信息
	ThirdAssetID        string    `gorm:"column:third_asset_id;type:varchar(255);not null;comment:三方生成的视频id" json:"third_asset_id"`                                // 三方生成的视频id
	FromTaskID          string    `gorm:"column:from_task_id;type:varchar(255);not null;comment:依赖的任务id" json:"from_task_id"`                                      // 依赖的任务id
	FirstFrame          string    `gorm:"column:first_frame;type:varchar(255);not null;comment:首帧图" json:"first_frame"`                                            // 首帧图
}

// TableName TasksVideo's table name
func (*TasksVideo) TableName() string {
	return TableNameTasksVideo
}
