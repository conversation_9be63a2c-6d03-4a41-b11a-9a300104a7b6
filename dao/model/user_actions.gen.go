// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserAction = "user_actions"

// UserAction mapped from table <user_actions>
type UserAction struct {
	UID        int64     `gorm:"column:uid;type:bigint;primaryKey;autoIncrement:true" json:"uid"`
	CreateTime time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	UseHotmove bool      `gorm:"column:use_hotmove;type:tinyint(1);not null;comment:1:使用过Hot Moves模版" json:"use_hotmove"`              // 1:使用过Hot Moves模版
}

// TableName UserAction's table name
func (*UserAction) TableName() string {
	return TableNameUserAction
}
