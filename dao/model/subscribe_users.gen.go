// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSubscribeUser = "subscribe_users"

// SubscribeUser mapped from table <subscribe_users>
type SubscribeUser struct {
	ID                    int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	SubscribeID           int64     `gorm:"column:subscribe_id;type:bigint;not null" json:"subscribe_id"`
	UserID                int64     `gorm:"column:user_id;type:bigint;not null" json:"user_id"`
	ProductID             string    `gorm:"column:product_id;type:varchar(255);not null" json:"product_id"`
	OriginalTransactionID string    `gorm:"column:original_transaction_id;type:varchar(128);not null" json:"original_transaction_id"`
	TransactionID         string    `gorm:"column:transaction_id;type:varchar(128);not null" json:"transaction_id"`
	ExpireAt              time.Time `gorm:"column:expire_at;type:timestamp;not null;comment:过期时间" json:"expire_at"` // 过期时间
	CreateTime            time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime            time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`
	Vendor                string    `gorm:"column:vendor;type:varchar(32);not null;comment:平台信息" json:"vendor"` // 平台信息
	Remark                string    `gorm:"column:remark;type:text;comment:备注信息" json:"remark"`                 // 备注信息
}

// TableName SubscribeUser's table name
func (*SubscribeUser) TableName() string {
	return TableNameSubscribeUser
}
