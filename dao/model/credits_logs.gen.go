// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCreditsLog = "credits_logs"

// CreditsLog credits 流水表
type CreditsLog struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdateAt     time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	UserID       int64     `gorm:"column:user_id;type:bigint unsigned;not null;comment:用户id" json:"user_id"`               // 用户id
	Action       int32     `gorm:"column:action;type:tinyint unsigned;not null;comment:行为" json:"action"`                  // 行为
	Scene        string    `gorm:"column:scene;type:varchar(128);not null;comment:场景" json:"scene"`                        // 场景
	BusinessType int32     `gorm:"column:business_type;type:int unsigned;not null;comment:credit 类型" json:"business_type"` // credit 类型
	Old          int64     `gorm:"column:old;type:bigint unsigned;not null;comment:旧余额" json:"old"`                        // 旧余额
	New          int64     `gorm:"column:new;type:bigint unsigned;not null;comment:新余额" json:"new"`                        // 新余额
	Count        int64     `gorm:"column:count;type:bigint unsigned;not null;comment:数量" json:"count"`                     // 数量
	Total        int64     `gorm:"column:total;type:bigint unsigned;not null;comment:总额" json:"total"`                     // 总额
}

// TableName CreditsLog's table name
func (*CreditsLog) TableName() string {
	return TableNameCreditsLog
}
