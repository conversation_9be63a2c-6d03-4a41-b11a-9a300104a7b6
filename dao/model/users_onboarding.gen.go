// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUsersOnboarding = "users_onboarding"

// UsersOnboarding 用户选择onboarding结果
type UsersOnboarding struct {
	ID           int32     `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdateAt     time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	UserID       int64     `gorm:"column:user_id;type:bigint unsigned;not null" json:"user_id"`
	OnboardingID int64     `gorm:"column:onboarding_id;type:bigint unsigned;not null" json:"onboarding_id"`
}

// TableName UsersOnboarding's table name
func (*UsersOnboarding) TableName() string {
	return TableNameUsersOnboarding
}
