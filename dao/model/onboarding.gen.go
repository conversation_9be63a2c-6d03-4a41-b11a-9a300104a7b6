// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOnboarding = "onboarding"

// Onboarding onboarding 页面
type Onboarding struct {
	ID         int64  `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	Title      string `gorm:"column:title;type:varchar(32);not null" json:"title"`
	Img        string `gorm:"column:img;type:varchar(255);not null" json:"img"`
	TemplateID int64  `gorm:"column:template_id;type:bigint unsigned;not null;comment:模版id" json:"template_id"` // 模版id
	Entry      string `gorm:"column:entry;type:varchar(32);not null;comment:类型" json:"entry"`                   // 类型
}

// TableName Onboarding's table name
func (*Onboarding) TableName() string {
	return TableNameOnboarding
}
