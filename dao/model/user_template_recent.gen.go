// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserTemplateRecent = "user_template_recent"

// UserTemplateRecent 用户最近访问表
type UserTemplateRecent struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	UserID       int64     `gorm:"column:user_id;type:bigint;not null;comment:用户ID" json:"user_id"`                              // 用户ID
	GenerateType string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频generate_type" json:"generate_type"` // 视频generate_type
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName UserTemplateRecent's table name
func (*UserTemplateRecent) TableName() string {
	return TableNameUserTemplateRecent
}
