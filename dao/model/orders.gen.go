// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrder = "orders"

// Order 订单表
type Order struct {
	ID                    int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                     // 主键ID
	UserID                int64     `gorm:"column:user_id;type:bigint unsigned;not null;comment:用户ID" json:"user_id"`                                // 用户ID
	ProductID             string    `gorm:"column:product_id;type:varchar(128);not null;comment:产品ID" json:"product_id"`                             // 产品ID
	OriginalTransactionID string    `gorm:"column:original_transaction_id;type:varchar(128);not null;comment:原始交易ID" json:"original_transaction_id"` // 原始交易ID
	TransactionID         string    `gorm:"column:transaction_id;type:varchar(128);not null;comment:当前交易ID" json:"transaction_id"`                   // 当前交易ID
	Vendor                string    `gorm:"column:vendor;type:varchar(32);not null;comment:平台" json:"vendor"`                                        // 平台
	ExpireAt              time.Time `gorm:"column:expire_at;type:timestamp;comment:过期时间" json:"expire_at"`                                           // 过期时间
	OrderType             string    `gorm:"column:order_type;type:varchar(32);not null;comment:订单类型" json:"order_type"`                              // 订单类型
	Currency              string    `gorm:"column:currency;type:varchar(32);not null;comment:货币类型" json:"currency"`                                  // 货币类型
	Price                 string    `gorm:"column:price;type:varchar(32);not null;comment:价格" json:"price"`                                          // 价格
	TeValue               string    `gorm:"column:te_value;type:text;comment:用户header信息" json:"te_value"`                                            // 用户header信息
	Remark                string    `gorm:"column:remark;type:text;comment:备注信息" json:"remark"`                                                      // 备注信息
	CreateTime            time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`     // 创建时间
	UpdateTime            time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`     // 更新时间
}

// TableName Order's table name
func (*Order) TableName() string {
	return TableNameOrder
}
