// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameResource = "resources"

// Resource 用户上传资源表
type Resource struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdateAt     time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	UserID       int64     `gorm:"column:user_id;type:bigint unsigned;not null" json:"user_id"`
	ResourceID   int64     `gorm:"column:resource_id;type:bigint unsigned;not null;comment:资源id" json:"resource_id"` // 资源id
	ResourceType string    `gorm:"column:resource_type;type:varchar(32);not null;comment:资源类型" json:"resource_type"` // 资源类型
	Biz          string    `gorm:"column:biz;type:varchar(32);not null;comment:业务类型" json:"biz"`                     // 业务类型
	URL          string    `gorm:"column:url;type:varchar(255);not null;comment:资源地址" json:"url"`                    // 资源地址
	Meta         string    `gorm:"column:meta;type:text;not null;comment:属性" json:"meta"`                            // 属性
	Status       int32     `gorm:"column:status;type:tinyint unsigned;not null;comment:状态" json:"status"`            // 状态
	Nsfw         int32     `gorm:"column:nsfw;type:tinyint unsigned;not null;comment:是否 nsfw； 0 不是 1 是" json:"nsfw"` // 是否 nsfw； 0 不是 1 是
}

// TableName Resource's table name
func (*Resource) TableName() string {
	return TableNameResource
}
