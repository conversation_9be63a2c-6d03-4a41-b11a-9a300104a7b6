// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTemplateTag = "template_tags"

// TemplateTag 模版 tag 表(feed tag)
type TemplateTag struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	GenerateType string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频模版类型" json:"generate_type"` // 视频模版类型
	TagKey       string    `gorm:"column:tag_key;type:varchar(255)" json:"tag_key"`
	CreateTime   time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	Top          int32     `gorm:"column:top;type:tinyint;not null;comment:置顶索引: 0-未置顶, 正整数表示置顶顺序" json:"top"`                           // 置顶索引: 0-未置顶, 正整数表示置顶顺序
}

// TableName TemplateTag's table name
func (*TemplateTag) TableName() string {
	return TableNameTemplateTag
}
