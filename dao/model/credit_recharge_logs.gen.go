// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCreditRechargeLog = "credit_recharge_logs"

// CreditRechargeLog credit 充值记录
type CreditRechargeLog struct {
	ID        int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreateAt  time.Time `gorm:"column:create_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_at"`
	UpdateAt  time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_at"`
	ExpireAt  int64     `gorm:"column:expire_at;type:bigint unsigned;not null" json:"expire_at"`
	UserID    int64     `gorm:"column:user_id;type:bigint unsigned;not null" json:"user_id"`
	ProductID string    `gorm:"column:product_id;type:varchar(255);not null" json:"product_id"`
	Amount    int64     `gorm:"column:amount;type:bigint unsigned;not null" json:"amount"`
	Entry     string    `gorm:"column:entry;type:varchar(255);not null" json:"entry"`
}

// TableName CreditRechargeLog's table name
func (*CreditRechargeLog) TableName() string {
	return TableNameCreditRechargeLog
}
