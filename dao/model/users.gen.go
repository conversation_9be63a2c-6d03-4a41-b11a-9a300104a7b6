// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUser = "users"

// User mapped from table <users>
type User struct {
	UID                int64     `gorm:"column:uid;type:bigint;primaryKey;autoIncrement:true" json:"uid"`
	CreateTime         time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime         time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	LoginMethodID      string    `gorm:"column:login_method_id;type:varchar(255);not null" json:"login_method_id"`
	LoginMethod        string    `gorm:"column:login_method;type:varchar(255);not null" json:"login_method"`
	RegisterChannel    string    `gorm:"column:register_channel;type:varchar(255);not null" json:"register_channel"`
	RegisterCountry    string    `gorm:"column:register_country;type:varchar(50);not null;comment:注册国家" json:"register_country"` // 注册国家
	RealUser           bool      `gorm:"column:real_user;type:tinyint(1);not null;default:1" json:"real_user"`
	Gender             string    `gorm:"column:gender;type:varchar(255);not null" json:"gender"`
	Did                string    `gorm:"column:did;type:varchar(255);not null" json:"did"`
	Idfv               string    `gorm:"column:idfv;type:varchar(255);not null" json:"idfv"`
	SubscribeProductID string    `gorm:"column:subscribe_product_id;type:varchar(120);not null;comment:订阅商品id" json:"subscribe_product_id"` // 订阅商品id
	RenewalStatus      string    `gorm:"column:renewal_status;type:varchar(50);not null;comment:会员续订开关" json:"renewal_status"`              // 会员续订开关
	UserLevel          int32     `gorm:"column:user_level;type:tinyint;not null;default:1" json:"user_level"`
	Status             int32     `gorm:"column:status;type:tinyint;not null;comment:状态: 0正常, 1注销" json:"status"`                            // 状态: 0正常, 1注销
	StripeCustomerID   string    `gorm:"column:stripe_customer_id;type:varchar(255);not null;comment:Stripe客户ID" json:"stripe_customer_id"` // Stripe客户ID
}

// TableName User's table name
func (*User) TableName() string {
	return TableNameUser
}
