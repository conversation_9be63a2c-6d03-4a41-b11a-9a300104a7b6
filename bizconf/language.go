package bizconf

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"io"
	"strings"
	"sync/atomic"
	"time"
	"unicode"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"go.uber.org/zap"
)

var (
	LangConf *LocalizationConf
)

type LocalizationConf struct {
	textMap  atomic.Pointer[map[string]string]
	lastTIme time.Time
}

func Init(conf config.LanguageConf) error {
	LangConf = &LocalizationConf{}
	err := LangConf.Load(conf)
	if err != nil {
		return err
	}
	go LangConf.Watch(conf)
	return nil
}

func (c *LocalizationConf) Load(conf config.LanguageConf) error {
	object, err := s3.Cli.GetObject(conf.Buck<PERSON>, conf.Key)
	if err != nil {
		return err
	}
	data, err := io.ReadAll(object.Body)
	if err != nil {
		return err
	}
	if object.LastModified == nil {
		return fmt.Errorf("LastModified is nil")
	}
	if object.LastModified.After(c.lastTIme) {
		err = c.UnmarshalLocConf(data)
		if err != nil {
			return err
		}
		c.lastTIme = *object.LastModified
	}
	return nil
}

func (c *LocalizationConf) Watch(conf config.LanguageConf) {
	for {
		time.Sleep(3 * time.Minute)
		err := c.Load(conf)
		if err != nil {
			logger.Error("Watch LanguageConf failed", zap.Error(err))
		}
	}
}

func (c *LocalizationConf) TextWithEmoji(langCode, textKey string) string {
	emojiPart, textPart := splitEmojiAndText(textKey)
	return emojiPart + c.Text(langCode, textPart)
}

func (c *LocalizationConf) TextWithDefault(langCode, textKey, dftText string) string {
	text := c.Text(langCode, textKey)
	if text == "" && dftText != "" {
		return dftText
	}
	return text
}

func (c *LocalizationConf) Text(langCode, textKey string) string {
	key := GenLocTxtMapKey(langCode, textKey)
	texts := *c.textMap.Load()
	text, has := texts[key]
	if has && text != "" {
		return text
	}
	text, has = texts[GenLocTxtMapKey("default", textKey)]
	if has && text != "" {
		return text
	}
	text, has = texts[GenLocTxtMapKey("en", textKey)]
	if has && text != "" {
		return text
	}
	return ""
}

func (c *LocalizationConf) TextAll(textKey string) map[string]string {
	langs := []string{"en", "zh_Hant", "de", "es", "pt", "ja", "tr", "ko", "it", "fr"}
	allTexts := make(map[string]string)
	for _, lang := range langs {
		allTexts[lang] = c.Text(lang, textKey)
	}
	return allTexts
}

func (c *LocalizationConf) UnmarshalLocConf(raw []byte) error {
	textMap := make(map[string]string)
	var maxLoop int = 1e6
	head := make(map[int]string)
	csvReader := csv.NewReader(bytes.NewReader(raw))
	csvReader.LazyQuotes = true
	for rowIdx := 0; rowIdx < maxLoop; rowIdx++ {
		line, err := csvReader.Read()
		if err == io.EOF {
			break
		} else if err != nil {
			return err
		}
		var firstCol string
		for colIdx, field := range line {
			if rowIdx == 0 {
				head[colIdx] = field
				continue
			}
			if colIdx == 0 {
				firstCol = field
				continue
			}
			if field == "" {
				continue
			}
			textMap[GenLocTxtMapKey(head[colIdx], firstCol)] = field
		}
	}
	c.textMap.Store(&textMap)
	return nil
}

func GenLocTxtMapKey(langCode, textKey string) string {
	return fmt.Sprintf("%s##%s", langCode, textKey)
}

func splitEmojiAndText(s string) (emojiPart, textPart string) {
	var emojiBuf strings.Builder
	var textBuf strings.Builder
	for _, r := range s {
		if unicode.IsLetter(r) || r == '_' {
			textBuf.WriteRune(r)
		} else {
			emojiBuf.WriteRune(r)
		}
	}
	return emojiBuf.String(), textBuf.String()
}
