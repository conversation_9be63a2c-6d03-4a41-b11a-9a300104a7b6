package bizconf

import (
	"fmt"
	"os"
	"testing"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
)

func TestText(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../etc/app/dev.json")
	s3.Init()
	fmt.Println(config.Configuration.LanguageConf)
	err := Init(config.Configuration.LanguageConf)
	if err != nil {
		t.Fatal(err)
		t.FailNow()
	}
	data := LangConf.TextAll("homepage_video_gen_shake_booty")
	t.Log(data)
}
