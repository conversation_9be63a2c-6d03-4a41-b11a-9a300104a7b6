package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/internal/feeds"
	"git.hoxigames.xyz/movely/movely-server/internal/scheduler"
	"git.hoxigames.xyz/movely/movely-server/pkg/bot"
	"git.hoxigames.xyz/movely/movely-server/pkg/email"
	meilisearch "git.hoxigames.xyz/movely/movely-server/pkg/search"
	"git.hoxigames.xyz/movely/movely-server/pkg/te"

	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/global"
	"git.hoxigames.xyz/movely/movely-server/internal"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"go.uber.org/zap"
)

func main() {
	err := config.Init("")
	if err != nil {
		return
	}
	err = logger.Init()
	if err != nil {
		return
	}
	defer global.Cleanup()
	err = rdb.Init()
	if err != nil {
		return
	}
	err = s3.Init()
	if err != nil {
		return
	}

	meilisearch.Init()

	email.SetUp(config.Configuration.Email)
	sf.Init(config.IsDev())

	bot.InitProductAlarmBot()
	te.Init(config.Configuration.TE)

	err = bizconf.Init(config.Configuration.LanguageConf)
	if err != nil {
		fmt.Printf("bizconf.Init err: %v\n", err)
		return
	}

	go func() {
		if config.Configuration.Env != "local" {
			scheduler.NewScheduler().Start()
		}
	}()

	engine := app.NewEngine()
	internal.Init(engine)
	srv := &http.Server{
		Addr:    config.Configuration.Srv.Address,
		Handler: engine,
	}
	go feeds.InitGenerate()
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			logger.Error("listen err", zap.Error(err))
		}
	}()
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err = srv.Shutdown(ctx); err != nil {
		logger.Error("shutdown err", zap.Error(err))
		return
	}

	logger.Info("shutdown success")
}
