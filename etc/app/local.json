{
  "srv": {
    "address": "0.0.0.0:6767"
  },
  "log": {
    "level": "info"
  },
  "mysql": {
    "dsn": "root:root@tcp(************:3306)/movely_dev?charset=utf8mb4&parseTime=True&loc=Local&interpolateParams=True",
    "max_life_time": 300,
    "max_idle_conn": 10,
    "max_open_conn": 10
  },
  "redis": {
    "addr": "************:6379",
    "password": "",
    "pool_size": 10,
    "cluster_mode": false,
    "tls": false
  },
  "apple": {
    "key_id": "5N8XYHZT2G",
********************************************************************************************************************************************************************************************************************************************************************************************
    "bundle_id": "ai.aftertake.dev",
    "issuer": "34d42dfa-dfde-40fe-9552-179688176332",
    "sandbox": true
  },
  "email": {
    "email_from": "Pic+ <<EMAIL>>",
    "smtp_pass": "BJC4NkOMxjUkTxseXNsLaXmONG6ggEIHpk+DvkZyoIVb",
    "smtp_user": "********************",
    "smtp_host": "email-smtp.us-west-2.amazonaws.com",
    "smtp_port": 587
  },
  "aws": {
    "s3": {
      "srv": {
        "domain": "https://movelyai.s3.us-west-2.amazonaws.com",
        "region": "us-west-2",
        "accessKey": "********************",
        "secretAccessKey": "SRcPvK89BPLzGtiqmLE5A2oXwYC1dPefi90XGfj6"
      }
    }
    "cdn": {
      "srv": {
        "domain": "https://static.aimovely.com"
      }
    }
  },
  "language": {
    "bucket": "movelyai",
    "key": "config/localization.csv"
  },
  "meilisearch": {
    "host": "http://************:7700",
    "master_key": "YjM0NTc4NDVmODNjMTFhY2MzZDU0OTIy",
    "index": "movely_template"
  },
  "stripe": {
    "secret_key": "sk_test_51PbGlTL39OuSalusgMUZhYK0WglwVtwOp4khtzMpTtww407lTvAZVRdPC9vHpFUYzRIpb8ZCfL9E9GQYnT46SsDF00zuqioqaR",
    "webhook_secret": "whsec_194b7a2042c3f836a16d5875d65d95b7f093762b8ab59671b2e7e0bf78ab410b",
    "success_url": "https://picplus.fly.dev/callback/stripe-succeed"
  },
  "te": {
    "directory": "/var/te",
    "prefix": "ifonly",
    "use_log_bus": false,
    "use_batch": true,
    "token": "dss"
  },
  "post_proc_serv_host": "http://************:6004",
  "resource": {
    "max_size": 20971520
  }
}
