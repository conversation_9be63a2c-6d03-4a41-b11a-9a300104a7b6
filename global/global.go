package global

import (
	"log"
	"sync"
)

const (
	ZapLoggerCleanup          = "zapLogger"
	CronCleanup               = "cron"
	RecommendRedisLockCleanup = "RecommendRedisLock"
)

var (
	Cleanups   = make(map[string]func() error)
	cleanupsMu = sync.Mutex{}
)

func Cleanup() {
	for name, cleanup := range Cleanups {
		if err := cleanup(); err != nil {
			log.Printf("%s cleanup err:%v\n", name, err)
		}
	}
}

func SetCleanup(name string, fn func() error) {
	cleanupsMu.Lock()
	Cleanups[name] = fn
	cleanupsMu.Unlock()
}
