package scheduler

import (
	"git.hoxigames.xyz/movely/movely-server/global"
	"git.hoxigames.xyz/movely/movely-server/internal/feeds"
	"git.hoxigames.xyz/movely/movely-server/internal/payment"
	"git.hoxigames.xyz/movely/movely-server/internal/video"
	"github.com/robfig/cron/v3"
)

type Scheduler struct {
	c *cron.Cron
}

// Start implements service.Service.
func (s *Scheduler) Start() {
	s.c.Run()
}

// Stop implements service.Service.
func (s *Scheduler) Stop() {
	<-s.c.Stop().Done()
}

type HandleCronFunc func()

func (h HandleCronFunc) Run() {
	h()
}

func NewScheduler() *Scheduler {
	s := &Scheduler{
		c: cron.New(cron.WithSeconds()),
	}

	// 视频任务定时创建任务
	s.mustAddJob("@every 6s", HandleCronFunc(func() {
		video.NewVideoTaskCreateScheduler().ScheduleCreateAll()
	}))
	// 视频任务定时查询任务
	s.mustAddJob("@every 10s", HandleCronFunc(func() {
		video.NewVideoTaskQueryScheduler().ScheduleQueryAll()
	}))
	// 搜索
	s.mustAddJob("@every 2h", HandleCronFunc(func() {
		feeds.UpdateMeiliSearch()
	}))
	// 订阅定时任务
	s.mustAddJob("@every 5m", HandleCronFunc(func() {
		payment.NewSubscriptionScheduler().Start()
	}))

	global.SetCleanup(global.CronCleanup, func() error {
		s.Stop()
		return nil
	})

	return s
}

func (s *Scheduler) mustAddJob(spec string, cmd cron.Job) {
	_, err := s.c.AddJob(spec, cmd)
	if err != nil {
		panic(err)
	}
}
