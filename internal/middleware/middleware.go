package middleware

import (
	"context"
	"net/http"

	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/jwtx"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
)

const (
	UserID = "user_id"
)

func Auth(ctx *app.Context) {
	auth := ctx.GetGinContext().Request.Header.Get(app.Authorization)
	claim, err := jwtx.Parse(auth, jwtx.Access)
	if err != nil {
		output.Error(ctx, 10001, err)
		ctx.GetGinContext().Abort()
		return
	}
	ctx.GetGinContext().Set(UserID, claim.AccountId)
	ctx.GetGinContext().Next()
}

func GetAccountId(ctx *app.Context) int64 {
	userId, ok := ctx.Get(UserID)
	if !ok {
		return 0
	}
	return userId.(int64)
}

func TraceMiddleware(ctx *app.Context) {
	traceId := ctx.TraceID
	if traceId == "" {
		traceId = app.NewTraceID()
	}
	ctx.GetGinContext().Request = ctx.GetGinContext().Request.WithContext(context.WithValue(ctx.GetGinContext().Request.Context(), logger.TraceIDKey, traceId))
	ctx.GetGinContext().Next()
}

func Cors(ctx *app.Context) {

	method := ctx.GetGinContext().Request.Method

	requestOrigin := ctx.GetGinContext().Request.Header.Get("Origin")
	ctx.GetGinContext().Header("Access-Control-Allow-Origin", requestOrigin)
	ctx.GetGinContext().Header("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRF-Token, Authorization, Token, Analytics")
	ctx.GetGinContext().Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
	ctx.GetGinContext().Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
	ctx.GetGinContext().Header("Access-Control-Allow-Credentials", "true")

	//放行所有OPTIONS方法
	if method == "OPTIONS" {
		ctx.GetGinContext().AbortWithStatus(http.StatusNoContent)
	}
	// 处理请求
	ctx.GetGinContext().Next()
}
