package resource

import (
	"bytes"
	"encoding/json"
	"fmt"
	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/chai2010/webp"
	"github.com/go-resty/resty/v2"
	"github.com/h2non/filetype"
	"github.com/h2non/filetype/types"
	"go.uber.org/zap"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"mime/multipart"
	"strconv"
	"strings"
	"time"
)

var (
	imageTypes = map[string]string{
		"png":  "image/png",
		"gif":  "image/gif",
		"jpeg": "image/jpeg",
		"webp": "image/webp"}
	allBiz = map[string]bool{
		"img2video": true,
	}
)

type Meta struct {
	W int `json:"w"`
	H int `json:"h"`
}

type Resource struct{}

var (
	nsfwClient *resty.Client
)

func init() {
	nsfwClient = resty.New().SetTimeout(2 * time.Minute)
}

type (
	uploadReq struct {
		Biz  string                `form:"biz"  binding:"required"`
		File *multipart.FileHeader `form:"file" binding:"required"`
	}
	uploadResp struct {
		Biz        string `json:"biz"`
		Url        string `json:"url"`
		ResourceID string `json:"resource_id"`
	}
)

func (resource *Resource) Upload(ctx *app.Context) {
	req := &uploadReq{}

	err := ctx.ShouldBind(req)
	if err != nil {
		output.Error(ctx, output.ParamErrorCode, err)
		return
	}

	biz := req.Biz
	if !allBiz[biz] {
		output.Error(ctx, output.ParamErrorCode, fmt.Errorf("biz %s not found", biz))
		return
	}
	userId := middleware.GetAccountId(ctx)

	file, err := req.File.Open()
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}
	defer func() {
		_ = file.Close()
	}()

	if req.File.Size > config.Configuration.Resource.MaxSize {
		output.Error(ctx, output.FileTooLargeErrorCode, fmt.Errorf("file is too large, %d", req.File.Size))
		return
	}

	fileBytes, err := io.ReadAll(file)
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	contentType, err := filetype.Match(fileBytes)
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}
	if !checkImageType(contentType) {
		output.Error(ctx, output.UnsupportedFileErrorCode, err)
		return
	}
	var img image.Config
	if contentType.MIME.Subtype == "webp" {
		img, err = webp.DecodeConfig(bytes.NewReader(fileBytes))
	} else {
		img, _, err = image.DecodeConfig(bytes.NewReader(fileBytes))
	}
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	meta := &Meta{
		W: img.Width,
		H: img.Height,
	}
	metaBytes, err := json.Marshal(meta)
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	key := fmt.Sprintf("%v/%s",
		s3.GetImageDir(config.Configuration.Env, userId),
		s3.GetImageKey(userId))
	ossResp, err := s3.Cli.Upload(s3.Bucket, key, bytes.NewReader(fileBytes), contentType.MIME.Value)
	if err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	resourceId := sf.Node.Generate().Int64()
	if err := rdb.Rdb.Create(&model.Resource{
		UserID:       userId,
		ResourceID:   resourceId,
		ResourceType: contentType.MIME.Value,
		Biz:          biz,
		URL:          s3.FormatURL(s3.Bucket, key),
		Meta:         string(metaBytes),
		Status:       enums.ResourceCreate,
		Nsfw:         enums.NotNsfw,
	}).Error; err != nil {
		logger.ErrorWithAppCtx(ctx, "resource upload failed", zap.Error(err))
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	resp := &uploadResp{}

	url := strings.ReplaceAll(ossResp.Location, config.Configuration.Aws.S3.Srv.Domain, config.Configuration.Aws.Cdn.Srv.Domain)

	go func() {
		isNsfw := resource.nsfwDetect(url, ctx.GetTraceID())
		if isNsfw {
			err = rdb.Rdb.Model(&model.Resource{}).Where("user_id = ? AND resource_id = ?", userId, resourceId).
				Updates(map[string]interface{}{"nsfw": enums.Nsfw}).Error
			if err != nil {
				logger.ErrorWithTraceId(ctx.GetTraceID(), "Resource Upload Updates failed", zap.Error(err))
			}
		}
	}()

	resp.Url = url
	resp.Biz = biz
	resp.ResourceID = strconv.FormatInt(resourceId, 10)
	output.Success(ctx, resp)
}

func checkImageType(contentType types.Type) bool {
	return imageTypes[contentType.MIME.Subtype] != ""
}

func (resource *Resource) nsfwDetect(image, traceId string) bool {
	startTime := time.Now().UTC()

	req := struct {
		Image string `json:"image"`
	}{Image: image}

	resp := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Nsfw bool `json:"nsfw"`
		} `json:"data"`
	}{}

	if _, err := nsfwClient.R().SetHeaders(map[string]string{
		"Content-Type": "application/json",
		"X-Trace-Id":   traceId,
	}).SetBody(req).SetResult(&resp).Post(
		fmt.Sprintf("%s/nsfw_detect", config.Configuration.PostProcServHost),
	); err != nil {
		logger.ErrorWithTraceId(traceId, "NsfwDetect: invoke nsfw_detect failed", zap.Error(err))
		return false
	} else if resp.Code != 0 {
		logger.ErrorWithTraceId(traceId, fmt.Sprintf("NsfwDetect: invoke nsfw_detect got unexpected code: %d, msg: %s", resp.Code, resp.Msg))
		return false
	}

	costTime := time.Now().UTC().Sub(startTime).Milliseconds()
	logger.InfoWithTraceId(traceId, fmt.Sprintf("NsfwDetect: invoke nsfw_detect cost time: %d ms, result: %v", costTime, resp.Data.Nsfw))

	return resp.Data.Nsfw
}

type (
	historyReq struct {
		Index uint64 `form:"index"`
		Size  uint64 `form:"size"`
	}
	historyResp struct {
		NextIndex int64              `json:"next_index"`
		Entries   []*historyRespData `json:"entries"`
	}
	historyRespData struct {
		ResourceID string `json:"resource_id"`
		URL        string `json:"url"`
		Meta       Meta   `json:"meta"`
	}
)

func (resource *Resource) History(ctx *app.Context) {
	req := &historyReq{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		output.Error(ctx, 20001, err)
		return
	}

	if req.Size > 20 {
		req.Size = 20
	}
	userId := middleware.GetAccountId(ctx)

	var resources []model.Resource
	db := rdb.Rdb.Model(&model.Resource{})
	if req.Index > 0 {
		db = db.Where("id < ?", req.Index)
	}
	db = db.Where("user_id = ? and status = ? and nsfw = ?", userId, enums.ResourceCreate, enums.NotNsfw)
	err = db.Order("id desc").Limit(int(req.Size)).Find(&resources).Error
	if err != nil {
		output.Error(ctx, 10003, err)
		return
	}

	var lastId int64
	resp := &historyResp{}
	data := make([]*historyRespData, 0, len(resources))

	for _, resource := range resources {

		meta := Meta{}
		_ = json.Unmarshal([]byte(resource.Meta), &meta)

		path, err := s3.ParseURL(resource.URL)
		if err != nil {
			continue
		}
		data = append(data, &historyRespData{
			ResourceID: strconv.FormatInt(resource.ResourceID, 10),
			URL:        fmt.Sprintf("%s/%s", config.Configuration.Aws.Cdn.Srv.Domain, path.Key),
			Meta:       meta,
		})
		lastId = resource.ID
	}
	resp.Entries = data
	resp.NextIndex = lastId

	output.Success(ctx, resp)
}

type (
	removeReq struct {
		ResourceIds []string `json:"resource_ids"`
	}
)

func (resource *Resource) Remove(ctx *app.Context) {
	req := &removeReq{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		output.Error(ctx, 20001, err)
		return
	}

	resourceIds := req.ResourceIds
	if len(resourceIds) > 100 {
		output.Error(ctx, 20001, err)
		return
	}

	userId := middleware.GetAccountId(ctx)
	err = rdb.Rdb.Model(&model.Resource{}).Where("user_id = ? AND resource_id IN (?)", userId, resourceIds).Updates(map[string]interface{}{"status": enums.ResourceDelete}).Error
	if err != nil {
		logger.ErrorWithAppCtx(ctx, "resource Delete Updates", zap.Error(err), zap.Strings("resource_ids", resourceIds))
	}

	output.SuccessNil(ctx)
	return
}
