package onboarding

import (
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"go.uber.org/zap"
)

type Onboarding struct{}

type (
	itemsResp struct {
		ID       int64              `json:"id"`
		Title    string             `json:"title"`
		Img      string             `json:"img"`
		Template *models.VideoEntry `json:"template,omitempty"`
	}
)

func (ob *Onboarding) Items(ctx *app.Context) {

	resp := make(map[string][]*itemsResp)

	var onboardings []model.Onboarding
	if err := rdb.Rdb.Model(&model.Onboarding{}).Find(&onboardings).Error; err != nil {
		output.Error(ctx, output.InternalErrorCode, err)
		return
	}

	for _, item := range onboardings {

		var tmpl *models.VideoEntry
		if item.TemplateID != 0 {
			var template model.ConfigsVideo
			rdb.Rdb.Model(&model.ConfigsVideo{}).Where("id = ?", item.TemplateID).First(&template)
			tmpl = &models.VideoEntry{
				VideoType:      template.VideoType,
				GenerateType:   template.GenerateType,
				MasterTemplate: template.MasterTemplate,
				Title:          template.Title,
				ImageUrl:       template.Image,
				VideoUrl:       template.VideoLow,
				VideoMediumUrl: template.VideoMedium,
				VideoLowUrl:    template.VideoLow,
				Blurhash:       template.Blurhash,
				VideoWidth:     int(template.VideoWidth),
				VideoHeight:    int(template.VideoHeight),
				TemplateLevel:  template.TemplateLevel,
			}
		}

		data := &itemsResp{
			ID:       item.ID,
			Title:    item.Title,
			Img:      item.Img,
			Template: tmpl,
		}
		if len(resp[item.Entry]) > 0 {
			resp[item.Entry] = append(resp[item.Entry], data)
		} else {
			resp[item.Entry] = []*itemsResp{data}
		}
	}

	output.Success(ctx, resp)
	return
}

type (
	selectReq struct {
		Id uint64 `json:"id" binding:"required"`
	}
)

func (ob *Onboarding) Select(ctx *app.Context) {
	req := &selectReq{}

	err := ctx.ShouldBind(req)
	if err != nil {
		output.Error(ctx, 20001, err)
		return
	}

	id := req.Id
	userId := middleware.GetAccountId(ctx)

	var onboarding model.Onboarding
	if err := rdb.Rdb.Model(&model.Onboarding{}).Where("id = ?", id).First(&onboarding).Error; err != nil {
		logger.ErrorWithAppCtx(ctx, "usersOnboarding Select", zap.Error(err))
		output.SuccessNil(ctx)
		return
	}

	usersOnboarding := &model.UsersOnboarding{
		UserID:       userId,
		OnboardingID: onboarding.ID,
	}

	if err := rdb.Rdb.Create(usersOnboarding).Error; err != nil {
		logger.ErrorWithAppCtx(ctx, "usersOnboarding Create", zap.Error(err))
	}
	output.SuccessNil(ctx)
	return
}
