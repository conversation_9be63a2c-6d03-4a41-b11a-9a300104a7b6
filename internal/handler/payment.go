package handler

import (
	"errors"
	"strings"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/internal/payment"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"github.com/spf13/cast"
)

type PaymentHandler struct {
	applePayment  *payment.ApplePayment
	stripePayment *payment.StripePayment
}

func NewPaymentHandler() *PaymentHandler {
	return &PaymentHandler{
		applePayment:  payment.NewApplePayment(),
		stripePayment: payment.NewStripePayment(),
	}
}

type (
	// 商品文档：https://hoxigames.feishu.cn/wiki/MErgw9yfKi3oq9k8KefcxXl9nEe
	ProductListResp struct {
		SubscribeProducts []*Product `json:"subscribe_products"`
		OnetimeProducts   []*Product `json:"onetime_products"`
	}
	Product struct {
		Id            string        `json:"id"`
		Price         float64       `json:"price"`
		Membership    string        `json:"membership,omitempty"`
		Credit        ProductCredit `json:"credits"`
		StripePriceID string        `json:"stripe_price_id"`
	}
	ProductCredit struct {
		BaseCount              int64 `json:"base_count"`
		RewardIntervalSeconds  int64 `json:"reward_interval_seconds,omitempty"`
		RewardIntervalCount    int64 `json:"reward_interval_count,omitempty"`
		RewardMaxLimit         int64 `json:"reward_max_limit,omitempty"`
		OnetimeAdditionalRatio int64 `json:"onetime_additional_ratio,omitempty"`
	}
)

func (h *PaymentHandler) ProductList(c *app.Context) {
	resp := &ProductListResp{SubscribeProducts: make([]*Product, 0), OnetimeProducts: make([]*Product, 0)}

	products := enums.GetMonthlySubscribeProducts(config.Configuration.Env)
	for _, product := range products {
		resp.SubscribeProducts = append(resp.SubscribeProducts, convertProduct(product))
	}
	// TODO: 做abtest
	// products = enums.GetWeeklySubscribeProducts(config.Configuration.Env)
	// for _, product := range products {
	// 	resp.SubscribeProducts = append(resp.SubscribeProducts, ConvertProduct(product))
	// }
	products = enums.GetOnetimeProducts(config.Configuration.Env)
	for _, product := range products {
		resp.OnetimeProducts = append(resp.OnetimeProducts, convertProduct(product))
	}

	output.Success(c, resp)
}

func convertProduct(product *enums.Product) *Product {
	return &Product{
		Id:         strings.ReplaceAll(product.Id, "movely_", "aftertake_"), // TODO: 临时更改，需要换回去
		Price:      cast.ToFloat64(product.Price),
		Membership: utils.ExtractMemberShipFromProduct(product.Id),
		Credit: ProductCredit{
			BaseCount:              product.Credits.BaseCount,
			RewardIntervalSeconds:  product.Credits.RewardIntervalSeconds,
			RewardIntervalCount:    product.Credits.RewardIntervalCount,
			RewardMaxLimit:         product.Credits.RewardMaxLimit,
			OnetimeAdditionalRatio: product.Credits.OnetimeAdditionalRatio,
		},
		StripePriceID: enums.GetStripePriceIDByProductID(product.Id),
	}
}

// 客户端支付调用接口
func (h *PaymentHandler) ApplePurchase(c *app.Context) {
	req := payment.ApplePurchaseReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	resp, err := h.applePayment.ApplePurchase(c, req)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, resp)
}

// 苹果恢复购买
func (h *PaymentHandler) AppleRestore(c *app.Context) {
	req := payment.AppleRestoreReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	resp, err := h.applePayment.AppleRestore(c, req)
	if err != nil {
		if errors.Is(err, enums.ErrTransactionAlreadyExist) {
			output.Error(c, output.ErrCodeTransactionAlreadyExist, err)
			return
		}
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, resp)
}

// 苹果回调接口
func (h *PaymentHandler) AppleWebhook(c *app.Context) {
	req := payment.AppleWebhookReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	resp, err := h.applePayment.ProcessAppleWebhook(c, req)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, resp)
}

// 创建stripe订单
func (h *PaymentHandler) StripeOrderCreate(c *app.Context) {
	req := payment.StripeOrderCreateReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	resp, err := h.stripePayment.StripeOrderCreate(c, req)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, resp)
}

// 获取stripe的billing portal url
func (h *PaymentHandler) GetBillingPortalURL(c *app.Context) {
	resp, err := h.stripePayment.GetBillingPortalURL(c)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, resp)
}

// stripe webhook 回调
func (h *PaymentHandler) StripeWebhook(c *app.Context) {
	err := h.stripePayment.StripeWebhook(c)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, nil)
}
