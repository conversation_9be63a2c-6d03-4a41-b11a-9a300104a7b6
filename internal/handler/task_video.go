package handler

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/credit"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/internal/video"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type VideoTaskHandler struct {
}

func NewVideoTaskHandler() *VideoTaskHandler {
	return &VideoTaskHandler{}
}

type (
	VideoTrack struct {
		GenerateApi                 string `json:"generate_api"`
		InputProcessMethod          string `json:"input_process_method"`
		TemplateSexyTag             string `json:"template_sexy_tag"`
		VideoOriPhotoIdAfterprocess string `json:"video_ori_photo_id_afterprocess"`
		AbtestMachine               int    `json:"abtest_machine"`
		AbtestLoraName              string `json:"abtest_lora_name"`
		VideoDuration               string `json:"video_duration"`
		TokenType                   string `json:"token_type"`
		TokenNum                    int64  `json:"token_num"`
		CreditNum                   int64  `json:"credit_num"`
	}
	VideoTask struct {
		Id                  string `json:"id"`
		Status              int32  `json:"status"` // -1:排队 0:默认 1:创建 2:成功 3:失败 4:转存 5:取消
		CanBeCancelled      bool   `json:"can_be_cancelled"`
		Progress            int32  `json:"progress"`
		Msg                 string `json:"msg"`
		IsExtend            bool   `json:"is_extend"`
		ExpectedCostSeconds int32  `json:"expected_cost_seconds"`
	}
	VideoAsset struct {
		Id           string `json:"id"`
		GenerateType string `json:"generate_type"`
		ImageUrl     string `json:"image_url"`
		VideoUrl     string `json:"video_url"`
		ResourceId   string `json:"resource_id"`
		ResourceUrl  string `json:"resource_url"`
	}

	VideoHistory struct {
		TaskId       string `json:"task_id"`
		GenerateType string `json:"generate_type"`
		ImageUrl     string `json:"image_url"`
		VideoUrl     string `json:"video_url"`
		ResourceId   string `json:"resource_id"`
		ResourceUrl  string `json:"resource_url"`
		Progress     int32  `json:"progress"`
		Status       int32  `json:"status"`
		CreateTime   int64  `json:"create_time"`
	}
)

type VideoCreateReq struct {
	GenerateType        string `json:"generate_type" binding:"required"`
	Duration            string `json:"duration"`        // 5 | 10
	PhotoWithEdit       bool   `json:"photo_with_edit"` // inpaint video
	IsReGenerate        bool   `json:"is_re_generate"`
	IsFree              bool   `json:"is_free"`
	Faceid              string `json:"faceid"`
	OriginResourceId    string `json:"origin_resource_id" binding:"required"`
	CustomPrompt        string `json:"custom_prompt"`
	TaskId              string `json:"task_id"`
	OriginVideoUrl      string `json:"origin_video_url"`       // 原视频
	BodyFirstFrameImage string `json:"body_first_frame_image"` // 首帧图片
	BodyFirstFrameMask  string `json:"body_first_frame_mask"`  // 首帧mask 图片
	TagKey              string `json:"tag_key"`
}

type VideoCreateResp struct {
	Task *VideoTask `json:"task"`
}

type VideoQueryReq struct {
	TaskId string `json:"task_id"`
}

type VideoQueryResp struct {
	Task    *VideoTask    `json:"task,omitempty"`
	Results []*VideoAsset `json:"results,omitempty"`
	Track   *VideoTrack   `json:"track,omitempty"`
}

func (h *VideoTaskHandler) VideoCreate(c *app.Context) {
	req := &VideoCreateReq{}
	resp := &VideoCreateResp{}
	err := c.ShouldBind(req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	userId := middleware.GetAccountId(c)
	configVideo := &model.ConfigsVideo{}
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type = ?", req.GenerateType).First(configVideo).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	if req.GenerateType == enums.GenerateCustomize {
		configVideo.VideoType = enums.VideoTypeImage2Video
	}
	if req.GenerateType == enums.GenerateExtend {
		configVideo.VideoType = enums.VideoTypeVideo2Video
	}
	if strings.HasPrefix(req.GenerateType, "breast_video") {
		configVideo.VideoType = enums.VideoTypeBody2Video
	}

	resource := &model.Resource{}
	err = rdb.Rdb.Model(&model.Resource{}).Where("user_id = ? AND resource_id = ?", userId, cast.ToInt64(req.OriginResourceId)).First(resource).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}

	prompt := configVideo.Prompt
	req.CustomPrompt = h.preProcessCustomPrompt(req.GenerateType, req.CustomPrompt)
	if req.CustomPrompt != "" {
		prompt += req.CustomPrompt
	}
	// freeVideoType := enums.VIDEO_TYPE_FREE
	// if configVideo.VideoType == enums.VideoTypeBody2Video {
	// 	freeVideoType = enums.VIDEO_TYPE_BODY_FREE
	// }
	// originUrl := req.OriginPicUrl
	// if configVideo.VideoType == enums.VideoTypeBody2Video && req.OriginVideoUrl != "" {
	// 	originUrl = req.OriginVideoUrl
	// }
	// costCoinCount := int64(1)
	// if req.Duration == "10" {
	// 	costCoinCount = int64(2)
	// }
	// req.IsFree = user.NewGetUserResourcesLogic(l.ctx, l.svcCtx).CanUseFreeVideo(freeVideoType, req.Duration, userDao)

	// 加入队列的逻辑
	newUserLimit := 10
	var tasksVideos []model.TasksVideo
	if err := rdb.Rdb.Model(&model.TasksVideo{}).Where("user_id = ?", userId).Limit(newUserLimit).Find(&tasksVideos).Error; err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	var execTaskCnt int64 // 当前用户正在执行的任务数量
	if err := rdb.Rdb.Model(&model.TasksVideo{}).
		Where("user_id = ? AND status IN ?", userId, []int32{
			int32(enums.TaskStatus_queuing), int32(enums.TaskStatus_created), int32(enums.TaskStatus_resaving),
		}).Count(&execTaskCnt).Error; err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	var priority int32 // 优先级，数值越小优先级越高
	if execTaskCnt > 0 {
		if len(tasksVideos) < newUserLimit { // 认为是新用户
			priority = 100
		} else {
			priority = 200
		}
	}

	creditLogic := credit.Credit{}
	costCoins, err := creditLogic.DeductCredit(c, userId, 50, enums.Image2VideoScene)
	if err != nil {
		output.Error(c, output.NotEnoughCreditErrorCode, err)
		return
	}
	coinInfo := enums.VideoCoinInfo{
		Status:    "deducted",
		CostCoins: costCoins,
	}
	coinInfoBs, _ := json.Marshal(coinInfo)

	nowt := time.Now().UTC()
	thirdName := configVideo.Thirdparty
	task := model.TasksVideo{
		ID:                  sf.Node.Generate().Int64(),
		Thirdparty:          thirdName,
		VideoType:           configVideo.VideoType,
		UserID:              userId,
		Priority:            priority,
		GenerateType:        req.GenerateType,
		Faceid:              req.Faceid,
		Status:              int32(enums.TaskStatus_queuing),
		Reason:              video.NewVideoReason("", 0, "queuing").JsonStr(),
		Prompt:              prompt,
		OriginResourceID:    req.OriginResourceId,
		Image:               utils.ConvertS3ToCDN(resource.URL),
		CoinInfo:            string(coinInfoBs),
		CreateTime:          nowt,
		UpdateTime:          nowt,
		FixedCreateTime:     nowt,
		VideoExpectDuration: req.Duration,
		// FromTaskID:          fromTaskId, // extend 任务依赖的任务id
	}
	// if strings.HasPrefix(req.GenerateType, "breast_video") {
	// 	task.Body2video = utils.ToJsonString(videosdk.Body2VideoParams{
	// 		UserId:              userID,
	// 		OriginVideoUrl:      req.OriginVideoUrl,
	// 		BodyFirstFrameImage: req.BodyFirstFrameImage,
	// 		BodyFirstFrameMask:  req.BodyFirstFrameMask,
	// 	})
	// }

	if err := rdb.Rdb.Create(&task).Error; err != nil {
		// 退币逻辑
		// if err := l.taskFailed(task, coinInfo, userDao, "not initialed", err); err != nil {
		// 	l.Errorf("VideoTaskCreate mark failed status failed: %v", err)
		// 	return nil, err
		// }
		// l.Errorf("VideoTaskCreate DB.Transaction error: %v", err)
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	// 更新用户相关信息
	ctx := context.WithValue(context.Background(), logger.TraceIDKey, c.GetTraceID())
	common.UpdateInfoByTask(ctx, userId, configVideo, req.TagKey)

	resp = &VideoCreateResp{
		Task: &VideoTask{
			Id:                  strconv.FormatInt(task.ID, 10),
			Status:              int32(enums.TaskStatus_created),
			CanBeCancelled:      task.Priority != 0,
			ExpectedCostSeconds: enums.GetVideoExpectedCostSeconds(thirdName),
		},
	}
	output.Success(c, resp)
}

func (h *VideoTaskHandler) VideoQuery(c *app.Context) {
	req := &VideoQueryReq{}
	resp := &VideoQueryResp{}
	err := c.ShouldBind(req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}

	var task model.TasksVideo
	if err := rdb.Rdb.Where(&model.TasksVideo{ID: cast.ToInt64(req.TaskId)}).First(&task).Error; err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}

	configVideo := &model.ConfigsVideo{}
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type = ?", task.GenerateType).First(configVideo).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}

	// var tag model.VideoTag
	// if err := l.svcCtx.DB.Model(&tag).Where(&model.VideoTag{
	// 	GenerateType: task.GenerateType,
	// 	TagType:      "sex",
	// 	Status:       1,
	// }).First(&tag).Error; err != nil {
	// 	l.Errorf("taskFailed: get video tag failed: %v", err)
	// 	// just for track, do not return
	// }

	abtestMachine := GetAbTestMachine(task.RuntimeAbParams)
	var tokenType string
	var tokenNum, creditNum int64
	var isExtend bool
	if task.Status == int32(enums.TaskStatus_finished) {
		// var videoInfo videosdk.VideoInfo
		// if err := json.Unmarshal([]byte(task.ExtInfo), &videoInfo); err != nil {
		// 	l.Errorf("VideoTaskQueryLogic: unmarshal ExtInfo failed: %v", err)
		// 	// fallthrough, do not return
		// }
		// var coinInfo enums.VideoCoinInfo
		// json.Unmarshal([]byte(task.CoinInfo), &coinInfo)
		// if len(coinInfo.CostCoins) > 0 {
		// 	tokenType = enums.VideoTypeToTrack(coinInfo.CostCoins[0].CoinType)
		// 	tokenNum = coinInfo.CostCoins[0].Count
		// 	creditNum = tokenNum
		// 	if coinInfo.CostCoins[0].Version == enums.VideoCoinCostVersion1 {
		// 		creditNum = enums.GetCoin2VideoCoinCount(creditNum)
		// 	}
		// }

		firstFrameImage := task.Image
		// if videoConf.VideoType == enums.VideoTypeID2Video || task.VideoType == enums.VideoTypeBody2Video { // 抽首帧
		// 	if task.FirstFrame != "" {
		// 		firstFrameImage = task.FirstFrame
		// 	} else {
		// 		firstFrameImage = ""
		// 		fname := strings.TrimSuffix(filepath.Base(task.Video), filepath.Ext(task.Video)) + ".jpg"
		// 		fname = filepath.Join(os.TempDir(), fname)
		// 		_ = os.MkdirAll(filepath.Dir(fname), os.ModePerm)
		// 		defer os.Remove(fname)
		// 		var stderr bytes.Buffer
		// 		cmd := exec.Command("ffmpeg", "-i", task.Video, "-ss", "00:00:00.125", "-vframes", "1", fname, "-y")
		// 		cmd.Stderr = &stderr
		// 		if err := cmd.Run(); err != nil {
		// 			l.Errorf("VideoTaskQueryLogic: extract first frame image failed: %v, stderr: %s", err, stderr.String())
		// 			// fallthrough, do not return
		// 		} else {
		// 			bs, err := os.ReadFile(fname)
		// 			if err != nil {
		// 				l.Errorf("VideoTaskQueryLogic: read first frame image failed: %v", err)
		// 				// fallthrough, do not return
		// 			} else {
		// 				rsp, err := user.NewGetImgUrlsLogic(l.ctx, l.svcCtx).GetImgUrlsV2(&types.GetImgUrlsReq{})
		// 				if err != nil {
		// 					l.Errorf("VideoTaskQueryLogic: get upload url failed: %v", err)
		// 					// fallthrough, do not return
		// 				}
		// 				if rsp != nil {
		// 					if _, err := httpx.Put(context.WithoutCancel(l.ctx), rsp.UploadUrl, bs,
		// 						httpx.WithHeader("Content-Type", "image/jpeg"),
		// 						httpx.WithTTL(2*time.Minute)); err != nil {
		// 						l.Errorf("VideoTaskQueryLogic: upload first frame image failed: %v", err)
		// 						// fallthrough, do not return
		// 					} else {
		// 						firstFrameImage = rsp.DownloadUrl
		// 						l.svcCtx.DB.Model(&model.TasksVideo{}).Where("task_id = ?", task.TaskID).Updates(map[string]any{
		// 							"first_frame": firstFrameImage,
		// 						})
		// 					}
		// 				}
		// 			}
		// 		}
		// 	}
		// }

		// if task.Status == int32(enums.TaskStatus_finished) &&
		// 	task.ThirdAssetID != "" &&
		// 	(task.VideoExpectDuration == enums.FiveDuration) &&
		// 	time.Since(task.FixedCreateTime) < enums.VideoExtendExpireTime &&
		// 	(strings.Contains(task.Thirdparty, videosdk.KLing) || task.Thirdparty == "custom") {
		// 	isExtend = true
		// }

		// inferParams := make(map[string]any)
		// _ = json.Unmarshal([]byte(videoConf.InferParams), &inferParams)
		// modelName, _ := inferParams["model_name"].(string)
		// if modelName == "kling-v2-1" {
		// 	isExtend = false
		// }

		resp = &VideoQueryResp{
			Task: &VideoTask{
				Id:                  cast.ToString(task.ID),
				Status:              task.Status,
				Progress:            100,
				IsExtend:            isExtend,
				ExpectedCostSeconds: enums.GetVideoExpectedCostSeconds(task.Thirdparty),
			},
			Results: []*VideoAsset{{
				Id:           cast.ToString(task.ID),
				GenerateType: task.GenerateType,
				ImageUrl:     firstFrameImage,
				VideoUrl:     utils.ConvertS3ToCDN(task.Video),
				ResourceId:   task.OriginResourceID,
				ResourceUrl:  utils.ConvertS3ToCDN(task.Image),
			}},
			Track: &VideoTrack{
				GenerateApi: task.APIType,
				// InputProcessMethod:          videoConf.ClipType,
				// TemplateSexyTag:             tag.TagName,
				VideoOriPhotoIdAfterprocess: task.Image,
				AbtestMachine:               abtestMachine,
				VideoDuration:               task.VideoExpectDuration,
				TokenType:                   tokenType,
				TokenNum:                    tokenNum,
				CreditNum:                   creditNum,
			},
		}
		output.Success(c, resp)
		return
	} else if task.Status == int32(enums.TaskStatus_failed) {
		var reason video.VideoReason
		_ = json.Unmarshal([]byte(task.Reason), &reason)
		// minimax 1026, 1027, kling 1301, pixverse 500054, 7
		if reason.Code == 1026 || reason.Code == 1027 || reason.Code == 1301 || reason.Code == 500054 || reason.Code == 7 {
			output.Error(c, output.InternalErrorCode, errors.New("gen video input sensitively"))
			return
		}
		// minimax 2013, kling 1201, pixverse 500033
		if reason.Code == 2013 || reason.Code == 1201 || reason.Code == 500033 {
			output.Error(c, output.InternalErrorCode, errors.New("gen video resolution invalid"))
			return
		}
		resp = &VideoQueryResp{
			Task: &VideoTask{
				Id:       strconv.FormatInt(task.ID, 10),
				Status:   task.Status,
				Progress: 0,
				Msg:      task.Reason,
			},
			Track: &VideoTrack{
				GenerateApi: task.APIType,
				// InputProcessMethod:          videoConf.ClipType,
				// TemplateSexyTag:             tag.TagName,
				VideoOriPhotoIdAfterprocess: task.Image,
				AbtestMachine:               abtestMachine,
			},
		}
		output.Success(c, resp)
		return
	} else if task.Status == int32(enums.TaskStatus_cancelled) {
		resp = &VideoQueryResp{
			Task: &VideoTask{
				Id:       strconv.FormatInt(task.ID, 10),
				Status:   task.Status,
				Progress: 0,
				Msg:      "task cancelled",
			},
			Track: &VideoTrack{
				GenerateApi: task.APIType,
				// InputProcessMethod:          videoConf.ClipType,
				// TemplateSexyTag:             tag.TagName,
				VideoOriPhotoIdAfterprocess: task.Image,
				AbtestMachine:               abtestMachine,
			},
		}
		output.Success(c, resp)
		return
	}

	progress := float32(time.Since(task.CreateTime)) / float32(6*time.Minute) * 100
	if progress >= 100 {
		progress = 99
	}
	var canBeCancelled bool
	if task.Status == int32(enums.TaskStatus_queuing) {
		progress = 0
		if task.Priority != 0 {
			canBeCancelled = true
		}
	}
	resp = &VideoQueryResp{
		Task: &VideoTask{
			Id:                  strconv.FormatInt(task.ID, 10),
			Status:              int32(enums.TaskStatus_created),
			CanBeCancelled:      canBeCancelled,
			Progress:            int32(progress),
			ExpectedCostSeconds: enums.GetVideoExpectedCostSeconds(task.Thirdparty),
		},
		Track: &VideoTrack{
			GenerateApi: task.APIType,
			// InputProcessMethod:          videoConf.ClipType,
			// TemplateSexyTag:             tag.TagName,
			VideoOriPhotoIdAfterprocess: task.Image,
			AbtestMachine:               abtestMachine,
		},
	}

	output.Success(c, resp)
}

func (h *VideoTaskHandler) preProcessCustomPrompt(generateType string, customPrompt string) string {
	if enums.ClearPromptVideoMap[generateType] { // 清除用户的 prompt
		return ""
	}
	customPrompt = enums.FilterSensitiveWords(customPrompt)
	return customPrompt
}

func GetAbTestMachine(abParams string) (abtestMachine int) {
	abtestMachine = 0 //0：二卡机器 1：八卡机器 2：八卡+加速lora

	var runtimeAbParams map[string]any
	err := json.Unmarshal([]byte(abParams), &runtimeAbParams)
	if err != nil {
		logger.Error("GetAbTestMachine unmarshal abParams failed", zap.Error(err))
		return
	}
	if runtimeAbParams["machine_group"] == "wan_remote" {
		abtestMachine = 1
	} else if runtimeAbParams["machine_group"] == "wan_remote_accelerate_lora" {
		abtestMachine = 2
	}
	return
}

type VideoCancelReq struct {
	TaskId string `json:"task_id"`
}

type VideoCancelResp struct {
}

func (h *VideoTaskHandler) VideoCancel(c *app.Context) {
	req := &VideoCancelReq{}
	resp := &VideoCancelResp{}
	err := c.ShouldBind(req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	userId := middleware.GetAccountId(c)
	var task model.TasksVideo
	if err := rdb.Rdb.Model(&model.TasksVideo{}).Where(&model.TasksVideo{ID: cast.ToInt64(req.TaskId)}).First(&task).Error; err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	if task.Status != int32(enums.TaskStatus_queuing) || task.Priority == 0 || task.UserID != userId {
		output.Error(c, output.ErrCodeTaskCanNotCancel, err)
		return
	}
	var coinInfo enums.VideoCoinInfo
	if err := json.Unmarshal([]byte(task.CoinInfo), &coinInfo); err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	coinInfo.Status = enums.VideoCoinStatusRefunded
	coinInfoBs, _ := json.Marshal(coinInfo)
	if res := rdb.Rdb.Model(&model.TasksVideo{}).
		Where(&model.TasksVideo{ID: task.ID}).
		Updates(&model.TasksVideo{
			Status:     int32(enums.TaskStatus_cancelled),
			CoinInfo:   string(coinInfoBs),
			UpdateTime: time.Now().UTC(),
		}); res.Error != nil {
		output.Error(c, output.InternalErrorCode, res.Error)
		return
	} else if res.RowsAffected == 0 {
		output.Error(c, output.InternalErrorCode, errors.New("task no rows affected"))
		return
	}
	logger.Infof("VideoTaskCancel: refund video coin, user_id: %d, task_id: %s", task.UserID, task.ID)

	err = (&credit.Credit{}).RefundCredits(c, userId, coinInfo.CostCoins)
	if err != nil {
		logger.Errorf("VideoTaskCancel: refund credit failed: %v, user_id: %d, task_id: %d", err, task.UserID, task.ID)
	}

	output.Success(c, resp)
}

type VideoHistoryReq struct {
	Size  int    `json:"size" form:"size"`   // 分页数量
	Index int64  `json:"index" form:"index"` // 游标
	Query string `json:"query" form:"query"` // 查询条件   finished, all
}

type VideoHistoryResp struct {
	NextIndex int64           `json:"next_index"`
	Tasks     []*VideoHistory `json:"tasks"`
}

func (h *VideoTaskHandler) VideoHistory(c *app.Context) {
	req := &VideoHistoryReq{}
	resp := &VideoHistoryResp{
		Tasks: []*VideoHistory{},
	}
	err := c.ShouldBind(req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	userId := middleware.GetAccountId(c)
	sql := rdb.Rdb.Model(&model.TasksVideo{}).Where("user_id = ?", userId)
	if req.Query == "finished" {
		sql = sql.Where("status = ?", int32(enums.TaskStatus_finished))
	}
	if req.Index > 0 {
		sql = sql.Where("id < ?", req.Index)
	}

	var tasks []model.TasksVideo
	if err := sql.Order("id desc").Limit(req.Size).Find(&tasks).Error; err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}

	generateTypeList := []string{}
	for _, task := range tasks {
		generateTypeList = append(generateTypeList, task.GenerateType)
	}
	var configsVideo []model.ConfigsVideo
	rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type in (?)", generateTypeList).Find(&configsVideo)
	generateTypeMap := make(map[string]model.ConfigsVideo)
	for _, config := range configsVideo {
		generateTypeMap[config.GenerateType] = config
	}

	for _, task := range tasks {
		asset := &VideoHistory{
			TaskId:       cast.ToString(task.ID),
			GenerateType: task.GenerateType,
			ImageUrl:     generateTypeMap[task.GenerateType].Image,
			VideoUrl:     utils.ConvertS3ToCDN(task.Video),
			ResourceId:   task.OriginResourceID,
			ResourceUrl:  task.Image,
			Status:       task.Status,
			CreateTime:   task.CreateTime.Unix(),
		}
		if task.Status == int32(enums.TaskStatus_finished) {
			asset.Progress = 100
		}
		resp.Tasks = append(resp.Tasks, asset)
	}
	if len(tasks) > 0 {
		resp.NextIndex = tasks[len(tasks)-1].ID
	}
	output.Success(c, resp)
}
