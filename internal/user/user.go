package user

import (
	"context"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/big"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/sourcegraph/conc/pool"

	"git.hoxigames.xyz/movely/movely-server/internal/enums"

	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/credit"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"

	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"golang.org/x/oauth2"
	googleauth "google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/email"
	"git.hoxigames.xyz/movely/movely-server/pkg/jwtx"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"git.hoxigames.xyz/movely/movely-server/pkg/te"
	"github.com/golang-jwt/jwt/v5"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	Email_Login  string = "email"
	Apple_Login  string = "apple"
	Google_Login string = "google"
	VNodePre     string = "movely:"
)

const (
	PUBLIC_KEY_REQ_URL = "https://appleid.apple.com/auth/keys"
	APPLE_URL          = "https://appleid.apple.com"
)

type JwtClaims struct {
	CHash          string `json:"c_hash"`
	Email          string `json:"email"`
	EmailVerified  bool   `json:"email_verified"`
	AuthTime       int    `json:"auth_time"`
	NonceSupported bool   `json:"nonce_supported"`
	jwt.RegisteredClaims
}

type User struct {
}

func NewUser() *User {
	return &User{}
}

type (
	SendvcodetoemailReq struct {
		Email string `json:"email" binding:"required"`
	}
	SendvcodetoemailResp struct {
		Success bool   `json:"success"`
		Code    string `json:"vcode"`
	}

	VerifyReq struct {
		Token       string `json:"token" binding:"required"`
		DistinctId  string `json:"distinct_id"`
		RegisterApp string `json:"register_app"`
	}
	VerifyRsp struct {
		AccountId    string `json:"account_id"`
		Token        string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		IsFirstLogin bool   `json:"is_first_login"`
	}

	VerifyVCodeReq struct {
		Email       string `json:"email" binding:"required"`
		Vcode       string `json:"vcode" binding:"required"`
		DistinctId  string `json:"distinct_id"`
		RegisterApp string `json:"register_app"`
	}

	RefreshTokenReq struct {
		AccessToken  string `json:"access_token"  binding:"required"`
		RefreshToken string `json:"refresh_token"  binding:"required"`
	}
	GetUserInfoReq struct {
	}
	GetUserInfoRsp struct {
		UID             string            `json:"uid"`
		Email           string            `json:"email"`
		Membership      string            `json:"membership"`
		UserLevel       int32             `json:"user_level"`
		Credits         map[int32]*Credit `json:"credits"`
		CreditRatio     int64             `json:"credit_ratio"`
		RegisterTime    int64             `json:"register_time"`
		RegisterChannel string            `json:"register_channel"`
	}
	Credit struct {
		Balance    int64 `json:"balance"`
		Factor     int64 `json:"factor,omitempty"`
		LastSupply int64 `json:"last_supply,omitempty"`
		SupplyRate int64 `json:"supply_rate,omitempty"`
		Max        int64 `json:"max,omitempty"`
	}

	UserDeleteRsp struct {
		Toast string `json:"toast"`
	}
)

func (u *User) VerifyApple(c *app.Context) {
	var req VerifyReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, 20001, err)
		return
	}
	// Apple jwt 校验
	claims := JwtClaims{}
	token, err := jwt.ParseWithClaims(req.Token, &claims, func(token *jwt.Token) (interface{}, error) {
		kidValue := token.Header["kid"]
		kid, ok := kidValue.(string)
		if !ok {
			return nil, errors.New("invalid kid")
		}
		pk := GetRSAPublicKey(kid)
		return pk, nil
	})
	if err != nil || !token.Valid {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	IosClientId := config.Configuration.Apple.BundleID
	ApplicationClientId := fmt.Sprintf("%v_services", IosClientId)
	if claims.Issuer != APPLE_URL || len(claims.Audience) == 0 || (claims.Audience[0] != ApplicationClientId && claims.Audience[0] != IosClientId) {
		output.Error(c, output.ParamErrorCode, errors.New("verify token info fail, info is not match"))
		return
	}

	rsp, err := u.userLogin(c, req.Token, Apple_Login, req.DistinctId, req.RegisterApp)
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "user login failed", zap.Error(err))
		output.Error(c, 10003, err)
		return
	}
	output.Success(c, rsp)
}

func (u *User) VerifyGoogle(c *app.Context) {
	var req VerifyReq
	err := c.ShouldBind(&req)
	if err != nil {
		body, _ := c.GetGinContext().GetRawData()
		logger.Error("verify google req bind failed", zap.Error(err), zap.Any("body", string(body)))
		output.Error(c, 20001, err)
		return
	}
	// 检查 google
	s, err := googleauth.NewService(context.Background(), option.WithTokenSource(oauth2.StaticTokenSource(&oauth2.Token{
		AccessToken: req.Token,
	})))
	if err != nil {
		logger.Error("googleauth NewService failed", zap.Error(err))
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	userinfo, err := s.Userinfo.Get().Do()
	if err != nil {
		logger.Error("get google user info failed", zap.Error(err), zap.Any("userinfo", userinfo))
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	// Todo did如果空，可以从数数获取
	rsp, err := u.userLogin(c, userinfo.Id, Google_Login, req.DistinctId, req.RegisterApp)
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "user login failed", zap.Error(err))
		output.Error(c, 10003, err)
		return
	}
	output.Success(c, rsp)
}

func IsTestEmail(email string) bool {
	return strings.HasSuffix(email, "@hoxigames.com")
}

func getRegisterApp(c *app.Context, registerApp string) string {
	// Todo web谷歌登录判断不了手机H5还是PC端 需要后端通过其他方式判断
	ua := c.GetGinContext().Request.Header.Get(app.UserAgent)
	uaMobile := c.GetGinContext().Request.Header.Get(app.SecChUaMobile)
	logger.InfoWithAppCtx(c, "GetRegisterApp", zap.Int64("uid", middleware.GetAccountId(c)),
		zap.String("ua", ua), zap.String("uaMobile", uaMobile), zap.String("registerApp", registerApp))
	return registerApp
}

func (u *User) VerifyVCode(c *app.Context) {
	var req VerifyVCodeReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, 20001, err)
		return
	}

	key := VNodePre + req.Email
	ctx := context.Background()

	if !IsTestEmail(req.Email) {
		codestr, err := rdb.Redis.Get(ctx, key).Result()
		if err != nil && err != redis.Nil {
			logger.ErrorWithTraceId(c.GetTraceID(), "redis get failed", zap.Error(err), zap.String("key", key))
			output.Error(c, 10003, err)
			return
		}
		if req.Vcode != codestr {
			output.Error(c, output.ErrCodeVCodeError, fmt.Errorf("vcode not match"))
			return
		}
	}
	rsp, err := u.userLogin(c, req.Email, Email_Login, req.DistinctId, req.RegisterApp)
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "user login failed", zap.Error(err))
		output.Error(c, 10003, err)
		return
	}
	rdb.Redis.Del(ctx, key).Result()
	output.Success(c, rsp)
}

func (u *User) SendVCodeToEmail(c *app.Context) {
	var req SendvcodetoemailReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, 20001, err)
		return
	}
	ctx := context.Background()
	code := generateCode()
	key := VNodePre + req.Email
	_, err = rdb.Redis.Set(ctx, key, code, 10*time.Minute).Result()
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "redis set failed", zap.Error(err), zap.String("key", key))
		output.Error(c, 10003, err)
		return
	}
	err = email.SendVerifyEmail(req.Email, &email.MailData{
		Code:     code,
		UserName: req.Email,
		Subject:  "verify email",
	}, "UserActive")
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "send verify email failed", zap.Error(err))
		output.Error(c, 10003, err)
		return
	}
	resp := &SendvcodetoemailResp{
		Success: true,
	}
	if config.IsDev() {
		resp.Code = code
	}
	output.Success(c, resp)
}

func (u *User) GetUserInfo(c *app.Context) {
	var req GetUserInfoReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, 20001, err)
		return
	}
	uid := middleware.GetAccountId(c)
	var user model.User
	err = rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).First(&user).Error
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "db query failed", zap.Error(err), zap.Int64("uid", uid))
		output.Error(c, output.ParamErrorCode, err)
		return
	}

	p := pool.New().WithErrors().WithMaxGoroutines(10).WithContext(context.Background())
	// 增加币
	p.Go(func(ctx context.Context) error {
		return (&credit.Credit{}).RechargeReward(c, user.UID)
	})
	_ = p.Wait()

	factor, maxCredit, supplyRate, creditRatio := u.getUserCredits(user)
	creditResp := make(map[int32]*Credit)
	freeCredits, _ := (&credit.Credit{}).GetFreeCredits(c, uid)
	for _, c := range freeCredits {
		creditResp[c.BusinessType] = &Credit{
			Balance:    c.Balance,
			LastSupply: c.LastSupply,
			SupplyRate: supplyRate,
			Factor:     factor,
			Max:        maxCredit,
		}
	}
	rechargeCredits, _ := (&credit.Credit{}).GetRechargeCredits(c, uid)
	for _, c := range rechargeCredits {
		creditResp[c.BusinessType] = &Credit{
			Balance: c.Balance,
		}
	}

	// Todo 字段
	resp := &GetUserInfoRsp{
		UID:             strconv.FormatInt(user.UID, 10),
		Membership:      utils.ExtractMemberShipFromProduct(user.SubscribeProductID),
		UserLevel:       user.UserLevel,
		Credits:         creditResp,
		CreditRatio:     creditRatio,
		RegisterTime:    user.CreateTime.Unix(),
		RegisterChannel: user.RegisterChannel,
	}
	if user.LoginMethod == Email_Login {
		resp.Email = user.LoginMethodID
	}
	output.Success(c, resp)
}

func (u *User) getUserCredits(user model.User) (factor, maxCredit, supplyRate, creditRatio int64) {
	factor = enums.FactorDefault
	maxCredit = enums.MaxDefault
	supplyRate = enums.SupplyRateDefault

	product := enums.GetProduct(user.SubscribeProductID, config.Configuration.Env)
	if product != nil {
		factor = product.Credits.RewardIntervalCount
		maxCredit = product.Credits.RewardMaxLimit
		supplyRate = product.Credits.RewardIntervalSeconds
		creditRatio = product.Credits.OnetimeAdditionalRatio
	}
	return factor, maxCredit, supplyRate, creditRatio
}

func (u *User) RefreshToken(c *app.Context) {
	var req RefreshTokenReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, 20001, err)
		return
	}
	refresh, err := jwtx.Parse(req.RefreshToken, jwtx.Refresh)
	if err != nil {
		output.Error(c, 10001, err)
		return
	}
	accessToken, refreshToken, err := u.getToken(c, refresh.AccountId)
	if err != nil {
		output.Error(c, 10001, err)
		return
	}
	resp := &VerifyRsp{
		Token:        accessToken,
		RefreshToken: refreshToken,
		AccountId:    strconv.FormatInt(refresh.AccountId, 10),
	}
	output.Success(c, resp)
}

func (u *User) userLogin(c *app.Context, loginMethodID, loginMethod, did string, registerApp string) (*VerifyRsp, error) {
	var userDao *model.User
	err := rdb.Rdb.Model(&model.User{}).Where("login_method_id = ? AND login_method = ?", loginMethodID, loginMethod).
		First(&userDao).Error
	var uid int64
	rsp := &VerifyRsp{}
	registerApp = getRegisterApp(c, registerApp)
	switch err {
	case nil:
		uid = userDao.UID
	case gorm.ErrRecordNotFound:
		rsp.IsFirstLogin = true
		uid, err = u.createUser(c, loginMethodID, loginMethod, did, registerApp)
		logger.InfoWithAppCtx(c, "userLogin create user", zap.Int64("uid", uid),
			zap.String("loginMethodID", loginMethodID), zap.String("loginMethod", loginMethod), zap.String("did", did), zap.String("registerApp", registerApp))
		if err != nil {
			common.TrackWithBaseAttributes(c, uid, "login_fail", map[string]any{
				"login_fail_reason": "create_user_failed",
			})
			logger.ErrorWithTraceId(c.GetTraceID(), "db query failed", zap.Error(err), zap.String("loginid", loginMethodID))
			return nil, err
		}
	default:
		common.TrackWithBaseAttributes(c, uid, "login_fail", map[string]any{
			"login_fail_reason": "db_query_failed",
		})
		logger.ErrorWithTraceId(c.GetTraceID(), "db query failed", zap.Error(err), zap.String("loginid", loginMethodID))
		return nil, err
	}
	accessToken, refreshToken, err := u.getToken(c, uid)
	if err != nil {
		common.TrackWithBaseAttributes(c, uid, "login_fail", map[string]any{
			"login_fail_reason": "get_token_failed",
		})
		logger.ErrorWithTraceId(c.GetTraceID(), "user login failed", zap.Error(err))
		return nil, err
	}
	common.TrackWithBaseAttributes(c, uid, "login_success", map[string]any{
		"login_method": loginMethod,
	})
	rsp.AccountId = strconv.FormatInt(uid, 10)
	rsp.Token = accessToken
	rsp.RefreshToken = refreshToken
	return rsp, nil
}

func (u *User) getToken(c *app.Context, uid int64) (accessToken string, refreshToken string, err error) {
	now := time.Now().UTC()
	accessToken, err = jwtx.Generate(uid, jwtx.Access, jwtx.WithTTL(now.Add(time.Hour*24*3)))
	if err != nil {
		return "", "", err
	}
	refreshToken, err = jwtx.Generate(uid, jwtx.Refresh, jwtx.WithTTL(now.Add(time.Hour*24*30)))
	if err != nil {
		return "", "", err
	}
	return
}

func (u *User) createUser(c *app.Context, loginMethodID, loginMethod, did string, registerApp string) (int64, error) {
	uid := sf.Node.Generate().Int64()
	now := time.Now().UTC()
	err := rdb.Rdb.Model(&model.User{}).Create(&model.User{
		UID:                uid,
		CreateTime:         now,
		UpdateTime:         now,
		LoginMethodID:      loginMethodID,
		LoginMethod:        loginMethod,
		RegisterChannel:    registerApp,
		RegisterCountry:    c.Country,
		Gender:             "",
		Did:                did,
		Idfv:               c.IDFA,
		SubscribeProductID: "",
		RenewalStatus:      "",
	}).Error
	if err != nil {
		return 0, err
	}

	err = (&credit.Credit{}).FreeCreditAdd(c, credit.FreeCreditAddParam{
		UserId:       uid,
		BusinessType: enums.FreeGiveawayBusinessType,
		Balance:      130,
		Scene:        enums.FreeGiveawayScene,
	})
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "FreeCreditBalanceAdd failed", zap.Error(err))
		common.SendServerAlarmMsg(context.Background(), uid, c.Country, "New User FreeCreditBalanceAdd failed")
	}
	distinctId := c.DistinctId
	if distinctId == "" {
		distinctId = did
	}

	// Todo 数数科技 用户来源渠道 adjust
	err = te.TeClient.UserSetOnce(strconv.Itoa(int(uid)), distinctId, map[string]interface{}{
		"register_app":     registerApp,
		"register_country": c.Country,
		"register_time":    now.Add(-7 * time.Hour).Unix(), // 使用UTC-7
	})
	if err != nil {
		logger.ErrorWithTraceId(c.GetTraceID(), "te user set once failed", zap.Error(err))
		common.SendServerAlarmMsg(context.Background(), uid, c.Country, "te user set once failed")
	}
	return uid, nil
}

func generateCode() string {
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	vcode := fmt.Sprintf("%06v", rnd.Int31n(1000000))
	return vcode
}

type JwtKeys struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

func GetRSAPublicKey(kid string) *rsa.PublicKey {
	var body []byte
	resp, err := http.Get(PUBLIC_KEY_REQ_URL)
	if err != nil {
		return nil
	} else {
		defer resp.Body.Close()
		_body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil
		}
		body = _body
	}

	var jKeys map[string][]JwtKeys
	err = json.Unmarshal(body, &jKeys)
	if err != nil {
		return nil
	}

	var pubKey rsa.PublicKey
	for _, data := range jKeys {
		for _, val := range data {
			if val.Kid == kid {
				n_bin, _ := base64.RawURLEncoding.DecodeString(val.N)
				n_data := new(big.Int).SetBytes(n_bin)

				e_bin, _ := base64.RawURLEncoding.DecodeString(val.E)
				e_data := new(big.Int).SetBytes(e_bin)

				pubKey.N = n_data
				pubKey.E = int(e_data.Uint64())
				break
			}
		}
	}

	if pubKey.E <= 0 {
		return nil
	}

	return &pubKey
}

type (
	updateReq struct {
		Gender string `json:"gender" binding:"required"`
	}
)

func (u *User) Update(ctx *app.Context) {
	req := updateReq{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		output.Error(ctx, 20001, err)
		return
	}

	gender := req.Gender
	userId := middleware.GetAccountId(ctx)

	if !checkGender(gender) {
		output.Error(ctx, 20001, err)
		return
	}

	if err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", userId).Updates(&model.User{Gender: gender}).Error; err != nil {
		output.Error(ctx, 10003, err)
		return
	}
	output.SuccessNil(ctx)
}

func (u *User) Delete(ctx *app.Context) {
	userId := middleware.GetAccountId(ctx)
	if err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", userId).UpdateColumn("status", 1).Error; err != nil {
		output.Error(ctx, 10003, err)
		return
	}
	var rsp UserDeleteRsp
	rsp.Toast = "The account will be successfully deleted after 30 days."
	output.Success(ctx, rsp)
}

var genders = map[string]string{
	"female": "female",
	"male":   "male",
}

func checkGender(gender string) bool {
	return genders[gender] != ""
}
