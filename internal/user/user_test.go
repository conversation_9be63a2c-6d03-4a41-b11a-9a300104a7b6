package user

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/pkg/email"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/oauth2"
	googleauth "google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

func TestJwt(t *testing.T) {
	tokenString := "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	claim := &JwtClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claim, func(token *jwt.Token) (interface{}, error) {
		kidValue := token.Header["kid"]
		kid, ok := kidValue.(string)
		if !ok {
			return nil, errors.New("invalid kid")
		}
		pk := GetRSAPublicKey(kid)
		return pk, nil
	})
	fmt.Println(token, err)
	fmt.Println(claim.Audience)
}

var token string = "******************************************************************************************************************************************************************************************************************************"

func TestGoogleApi(t *testing.T) {
	s, err := googleauth.NewService(context.Background(), option.WithTokenSource(oauth2.StaticTokenSource(&oauth2.Token{
		AccessToken: token,
	})))
	if err != nil {
		t.Fatal(err)
	}
	user, err := s.Userinfo.Get().Do()
	fmt.Println(user, err)
}

func TestSendVcode(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	email.SetUp(config.Configuration.Email)
	err := email.SendVerifyEmail("<EMAIL>", &email.MailData{
		Code:     "123245",
		UserName: "<EMAIL>",
		Subject:  "verify email",
	}, "UserActive")
	if err != nil {
		t.Fatal(err)
	}
}
