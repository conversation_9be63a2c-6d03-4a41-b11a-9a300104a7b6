package payment

import (
	"testing"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/global"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/bot"
	"git.hoxigames.xyz/movely/movely-server/pkg/email"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
)

func TestMain(m *testing.M) {
	err := config.Init("../../etc/app/local.json")
	if err != nil {
		return
	}
	err = logger.Init()
	if err != nil {
		return
	}
	defer global.Cleanup()
	err = rdb.Init()
	if err != nil {
		return
	}
	err = s3.Init()
	if err != nil {
		return
	}
	bot.InitProductAlarmBot()
	email.SetUp(config.Configuration.Email)
	sf.Init(config.IsDev())
	m.Run()
}

func TestProcessAppleTransaction(t *testing.T) {
	payment := NewApplePayment()
	{
		// A 买
		// param := AppleTransactionAdd{
		// 	BundleID:              "ai.aftertake.dev",
		// 	TransactionID:         "100",
		// 	OriginalTransactionID: "100",
		// 	ProductID:             "movely_basic_weekly_7",
		// 	ExpiresDate:           time.Now().Add(time.Hour * 24 * 30),
		// 	Price:                 100,
		// 	Currency:              "USD",
		// 	FromScene:             enums.PaymentFromScenePurchase,
		// }
		// err := payment.ProcessAppleTransaction(&app.Context{}, 10001, param)
		// t.Log(err)

		// B 升级买
		param := AppleTransactionAdd{
			BundleID:              "ai.aftertake.dev",
			OriginalTransactionID: "100",
			TransactionID:         "104",
			ProductID:             "movely_pro_weekly_14",
			ExpiresDate:           time.Now().Add(time.Hour * 24 * 30),
			Price:                 100,
			Currency:              "USD",
			FromScene:             enums.PaymentFromScenePurchase,
		}
		err := payment.ProcessAppleTransaction(&app.Context{}, 10004, param)
		t.Log(err)
	}
}

func TestSubscriptionScheduler_FixAppleSub(t *testing.T) {
	// var sub model.Subscribe
	// rdb.Rdb.Model(&model.Subscribe{}).Where("id = ?", 1947630901019430912).First(&sub)
	// err := NewSubscriptionScheduler().FixAppleSub(sub)
	// t.Log(err)

	NewSubscriptionScheduler().Start()
}
