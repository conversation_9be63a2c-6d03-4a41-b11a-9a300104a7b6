package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/webhook"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*

后台：https://dashboard.stripe.com/dashboard


调试 stripe 回调: https://docs.stripe.com/stripe-cli

本地测试 stripe 回调
stripe login
stripe listen --forward-to http://127.0.0.1:6767/v1/payment/stripe/webhook

## 如果失败，前往 https://dashboard.stripe.com/test/events 找到 evt_* 然后执行
stripe events resend evt_1RnxJvL39OuSalusS5N0tm0b


测试模式：https://dashboard.stripe.com/test/products
测试时钟：https://dashboard.stripe.com/test/billing/subscriptions/test-clocks
*/

type StripePayment struct {
	stripeClient *stripe.Client
}

func NewStripePayment() *StripePayment {
	return &StripePayment{
		stripeClient: stripe.NewClient(config.Configuration.Stripe.SecretKey),
	}
}

type (
	StripeOrderCreateReq struct {
		PriceId    string         `json:"price_id"`
		ProductId  string         `json:"product_id"`
		SuccessURL string         `json:"success_url"`
		Context    map[string]any `json:"context"`
	}
	StripeOrderCreateResp struct {
		RedirectUrl string `json:"redirect_url"`
		SessionId   string `json:"session_id"`
	}

	GetBillingPortalURLResponse struct {
		URL string `json:"url"` // 例如："https://billing.stripe.com/p/session/test_"
	}
)

func (s *StripePayment) CreateCustomer(email string, uid int64) (*stripe.Customer, error) {
	customer, err := s.stripeClient.V1Customers.Create(context.Background(), &stripe.CustomerCreateParams{
		Email: stripe.String(email),
		Metadata: map[string]string{
			"uid": fmt.Sprintf("%d", uid),
		},
	})
	return customer, err
}

// 创建 stripe 订单，用于给用户提供一个支付链接
func (s *StripePayment) StripeOrderCreate(c *app.Context, req StripeOrderCreateReq) (resp StripeOrderCreateResp, err error) {
	uid := middleware.GetAccountId(c)
	product := enums.GetProductByStripePriceID(req.PriceId, config.Configuration.Env)
	if product == nil {
		return resp, fmt.Errorf("product not found")
	}
	var userDao *model.User
	err = rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).First(&userDao).Error
	if err != nil {
		return resp, err
	}
	// if userDao.SubscribeProductID != "" { // 不能这么判断
	// 	return resp, fmt.Errorf("user already has a subscription")
	// }

	// 创建stripe customer
	if userDao.StripeCustomerID == "" {
		var email string
		if userDao.LoginMethod == "email" && userDao.LoginMethodID != "" {
			email = userDao.LoginMethodID
		}
		stripeCustomer, err := s.CreateCustomer(email, uid)
		if err != nil {
			return resp, err
		}
		userDao.StripeCustomerID = stripeCustomer.ID
		err = rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).Updates(map[string]any{
			"stripe_customer_id": stripeCustomer.ID,
		}).Error
		if err != nil {
			return resp, err
		}
	}

	isDiscount := strings.Contains(product.Id, "_annual_")
	checkoutSession, err := s.CreateCheckoutSession(userDao.StripeCustomerID, req.PriceId, req.SuccessURL, isDiscount)
	if err != nil {
		return resp, err
	}
	resp.RedirectUrl = checkoutSession.URL
	resp.SessionId = checkoutSession.ID
	return
}

// stripe checkout 用于给用户提供一个支付链接
// https://docs.stripe.com/api/checkout/sessions/create
func (s *StripePayment) CreateCheckoutSession(customerID string, priceID string, successURL string, isDiscount bool) (*stripe.CheckoutSession, error) {
	ctx := context.Background()
	// price, err := s.stripeClient.V1Prices.Retrieve(ctx, priceID, nil)
	// if err != nil {
	// 	return nil, err
	// }
	params := &stripe.CheckoutSessionCreateParams{
		ExpiresAt: stripe.Int64(time.Now().Add(120 * time.Minute).Unix()), // 2 hours 失效
		Customer:  stripe.String(customerID),
		LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
			{
				Price:    stripe.String(priceID), // 商品的 price id
				Quantity: stripe.Int64(1),        // 购买的数量
			},
		},
		AllowPromotionCodes: stripe.Bool(true),                                             // 优惠码 和 Discounts 冲突
		Mode:                stripe.String(string(stripe.CheckoutSessionModeSubscription)), // 订阅
		SuccessURL:          stripe.String(successURL),
	}
	// if isDiscount {
	// 	params.Discounts = []*stripe.CheckoutSessionCreateDiscountParams{
	// 		{
	// 			Coupon: stripe.String("0PMHDdfj"),
	// 		},
	// 	}
	// }
	checkoutSession, err := s.stripeClient.V1CheckoutSessions.Create(ctx, params)
	return checkoutSession, err
}

// GetBillingPortalURL 目前仅支持 1 个 subscription，没有输入参数，调用这个接口即可取消当前的订阅。取消订阅只是取消续费，不影响用户继续享受当前周期的服务。
//
// Stripe 竟然不支持用户在 Stripe 平台自行取消订阅。用户想取消订阅必须通过商户才能取消。如果我们跑路了对方就完全没法取消订阅。
// 用户如果想自行取消订阅，必须打电话给信用卡机构告诉他们不要给这个商户付款，举报这个商户的欺诈行为。
// https://www.reddit.com/r/stripe/comments/p53gn8/cancel_subscription/
// https://www.reddit.com/r/stripe/comments/15vrmlw/psa_when_using_stripe_to_pay_for_a_subscription/
// 无服务器版的可以在 https://dashboard.stripe.com/test/settings/billing/portal 设置
// 我们这里使用有服务器版的，生成一个临时的 portal 链接 https://docs.stripe.com/customer-management/integrate-customer-portal#customize
func (s *StripePayment) GetBillingPortalURL(c *app.Context) (resp GetBillingPortalURLResponse, err error) {
	uid := middleware.GetAccountId(c)
	userDao := &model.User{}
	err = rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).First(&userDao).Error
	if err != nil {
		return resp, err
	}
	billingPortalSession, err := s.stripeClient.V1BillingPortalSessions.Create(context.Background(), &stripe.BillingPortalSessionCreateParams{
		Customer: stripe.String(userDao.StripeCustomerID),
	})
	if err != nil {
		return resp, err
	}
	resp.URL = billingPortalSession.URL
	return
}

func (s *StripePayment) StripeWebhook(c *app.Context) (err error) {
	payload, _ := c.GetGinContext().GetRawData()
	signature := c.GetGinContext().GetHeader("Stripe-Signature")
	logger.InfoWithAppCtx(c, "StripeWebhook event start", zap.Int("signature", len(signature)), zap.Int("payload", len(payload)))
	event, err := webhook.ConstructEventWithOptions(payload, signature, config.Configuration.Stripe.WebhookSecret, webhook.ConstructEventOptions{IgnoreAPIVersionMismatch: true})
	if err != nil {
		logger.ErrorWithAppCtx(c, "StripeWebhook ConstructEventWithOptions", zap.Error(err))
		return err
	}

	// TODO: 暂时所有的都记录一下
	rdb.Rdb.Model(&model.StripeLog{}).Create(&model.StripeLog{
		EventID:    event.ID,
		EventType:  string(event.Type),
		Info:       utils.ToJsonString(event),
		CreateTime: time.Now().UTC(),
		UpdateTime: time.Now().UTC(),
	})

	switch event.Type {
	case stripe.EventTypeInvoicePaymentSucceeded: // invoice.payment_succeeded 事件每次续费都会发
		var invoice stripe.Invoice
		err := json.Unmarshal(event.Data.Raw, &invoice)
		if err != nil {
			return fmt.Errorf("unmarshal invoice error: %w", err)
		}
		customerID := ""
		priceID := ""
		subID := ""
		periodEnd := int64(0)
		if invoice.Customer != nil {
			customerID = invoice.Customer.ID
		}
		if invoice.Lines != nil && len(invoice.Lines.Data) > 0 {
			priceID = invoice.Lines.Data[0].Pricing.PriceDetails.Price
			periodEnd = invoice.Lines.Data[0].Period.End
			subID = invoice.Lines.Data[0].Subscription.ID
		}
		if subID == "" || priceID == "" || customerID == "" {
			common.SendServerAlarmMsg(context.Background(), 0, "", fmt.Sprintf("StripeWebhook invoice.payment_succeeded event error, subID: %s, priceID: %s, customerID: %s", subID, priceID, customerID))
			return fmt.Errorf("subID or priceID or customerID is empty")
		}

		orderType := ""
		switch stripe.InvoiceBillingReason(invoice.BillingReason) {
		case stripe.InvoiceBillingReasonSubscriptionCreate:
			orderType = "新订单"
		case stripe.InvoiceBillingReasonSubscriptionUpdate:
			orderType = "套餐升级"
		case stripe.InvoiceBillingReasonSubscriptionCycle:
			orderType = "到期续费"
		default:
			// ignore error
		}

		product := enums.GetProductByStripePriceID(priceID, config.Configuration.Env)
		if product == nil {
			return fmt.Errorf("product not found")
		}

		transaction := StripeTransactionAdd{
			TransactionID:         event.ID,
			OriginalTransactionID: subID,
			CustomerID:            customerID,
			PriceID:               priceID,
			ExpiresDate:           periodEnd,
			Price:                 fmt.Sprintf("%d", invoice.AmountPaid),
			Currency:              string(invoice.Currency),
			OrderType:             orderType,
			Product:               product,
		}
		err = s.AddSubscribeOrder(c, transaction)
		if err != nil {
			common.SendServerAlarmMsg(context.Background(), 0, "", fmt.Sprintf("StripeWebhook AddSubscribeOrder error, event: %s", event.ID))
			return err
		}

	case stripe.EventTypeCustomerSubscriptionUpdated:
		var subscription stripe.Subscription
		err := json.Unmarshal(event.Data.Raw, &subscription)
		if err != nil {
			return fmt.Errorf("unmarshal subscription error: %w", err)
		}

		customerID := ""
		if subscription.Customer != nil {
			customerID = subscription.Customer.ID
		}

		userDao := &model.User{}
		err = rdb.Rdb.Model(&model.User{}).Where("stripe_customer_id = ?", customerID).First(&userDao).Error
		if err != nil {
			return fmt.Errorf("get user error: %w", err)
		}

		if cancelAt, ok := event.Data.PreviousAttributes["cancel_at_period_end"]; ok && cancelAt == false { // 只处理第一次取消事件
			if subscription.CanceledAt != 0 { // 取消订阅
				common.SendServerAlarmMsg(context.Background(), userDao.UID, "", fmt.Sprintf("StripeWebhook canceled subscription, user_id: %d, subscription_id: %s", userDao.UID, subscription.ID))
				logger.InfoWithAppCtx(c, "StripeWebhook canceled subscription", zap.Any("user_id", userDao.UID), zap.Any("subscription_id", subscription.ID))
			}
		}
	}
	return
}

func (s *StripePayment) AddSubscribeOrder(c *app.Context, transaction StripeTransactionAdd) (err error) {
	userDao := &model.User{}
	err = rdb.Rdb.Model(&model.User{}).Where("stripe_customer_id = ?", transaction.CustomerID).First(&userDao).Error
	if err != nil {
		return fmt.Errorf("get user error: %w", err)
	}
	logger.InfoWithAppCtx(c, "StripeWebhook AddSubscribeOrder", zap.Any("user_id", userDao.UID), zap.Any("transaction", transaction))

	// 新增订单
	order := &model.Order{
		UserID:                userDao.UID,
		ProductID:             transaction.Product.Id,
		TransactionID:         transaction.TransactionID,
		OriginalTransactionID: transaction.OriginalTransactionID,
		ExpireAt:              time.Unix(transaction.ExpiresDate, 0),
		Vendor:                enums.Stripe,
		OrderType:             transaction.OrderType,
		Currency:              transaction.Currency,
		Price:                 transaction.Price,
	}
	err = rdb.Rdb.Model(&model.Order{}).Create(order).Error
	if err != nil {
		return fmt.Errorf("create subscribe order error: %w", err)
	}

	// 创建或更新订阅用户记录
	err = s.ensureSubscription(c, rdb.Rdb, userDao.UID, transaction)
	if err != nil {
		return fmt.Errorf("ensure subscription error: %w", err)
	}

	// 增加视频币积分
	// todo 增加credit
	//creditLogic := &credit.Credit{}
	//if err := creditLogic.RechargeCreditAdd(c, userDao.UID, enums.RechargeSubsBusinessType, int64(transaction.Product.Credits.BaseCount)); err != nil {
	//	return err
	//}
	common.SendServerAlarmMsg(context.Background(), userDao.UID, "", fmt.Sprintf("StripeWebhook AddSubscribeOrder, user_id: %d, product_id: %s", userDao.UID, transaction.Product.Id))
	// 修改订阅信息
	err = rdb.Rdb.Model(&model.User{}).Where("uid = ?", userDao.UID).Updates(map[string]any{
		"subscribe_product_id": transaction.Product.Id,
	}).Error
	if err != nil {
		return fmt.Errorf("update user error: %w", err)
	}
	return nil
}

type StripeTransactionAdd struct {
	TransactionID         string // event.ID
	OriginalTransactionID string // subscription.ID
	CustomerID            string
	PriceID               string
	ExpiresDate           int64
	Price                 string
	Currency              string
	OrderType             string
	Product               *enums.Product
}

func (h *StripePayment) ensureSubscription(c *app.Context, tx *gorm.DB, uid int64, transaction StripeTransactionAdd) error {
	productId := transaction.Product.Id
	expireAt := time.Unix(transaction.ExpiresDate, 0)
	remark := utils.ToJsonString(transaction)

	var subscribe model.Subscribe
	err := tx.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).First(&subscribe).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新订阅
		subscribe = model.Subscribe{
			ID:                    sf.Node.Generate().Int64(),
			UserID:                uid,
			ProductID:             productId,
			TransactionID:         transaction.TransactionID,
			OriginalTransactionID: transaction.OriginalTransactionID,
			ExpireAt:              expireAt,
			Status:                enums.SubActive,
			Vendor:                enums.Stripe,
			Remark:                remark,
			TeValue:               "",
			CreateTime:            time.Now().UTC(),
			UpdateTime:            time.Now().UTC(),
		}
		if err := tx.Create(&subscribe).Error; err != nil {
			return err
		}
	} else if err != nil {
		return err
	} else {
		// 更新现有订阅
		if err := tx.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Updates(map[string]any{
			"user_id":        uid,
			"product_id":     productId,
			"transaction_id": transaction.TransactionID,
			"expire_at":      expireAt,
			"status":         enums.SubActive,
			"remark":         remark,
			"update_time":    time.Now().UTC(),
		}).Error; err != nil {
			return err
		}
	}

	// 创建或更新订阅用户记录
	subscribeUser := model.SubscribeUser{
		ID:                    sf.Node.Generate().Int64(),
		SubscribeID:           subscribe.ID,
		UserID:                uid,
		ProductID:             productId,
		OriginalTransactionID: transaction.OriginalTransactionID,
		TransactionID:         transaction.TransactionID,
		ExpireAt:              expireAt,
		CreateTime:            time.Now().UTC(),
		UpdateTime:            time.Now().UTC(),
	}
	return tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "subscribe_id"}, {Name: "user_id"}}, // 需要在 subscribe_id 和 user_id 上有唯一索引
		DoUpdates: clause.AssignmentColumns([]string{
			"product_id",
			"original_transaction_id",
			"transaction_id",
			"expire_at",
			"update_time",
		}),
	}).Create(&subscribeUser).Error
}
