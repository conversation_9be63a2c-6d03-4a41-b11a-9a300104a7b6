package payment

import (
	"context"
	"fmt"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"go.uber.org/zap"
)

type SubscriptionScheduler struct {
	applePayment *ApplePayment
}

func NewSubscriptionScheduler() *SubscriptionScheduler {
	return &SubscriptionScheduler{
		applePayment: NewApplePayment(),
	}
}

func (s *SubscriptionScheduler) Start() {
	ctx := context.Background()
	lock, err := rdb.RedisLock.Obtain(ctx, "movely:subscription_scheduler", time.Second*180, nil)
	if err != nil {
		logger.Error("SubscriptionScheduler Obtain lock err", zap.Error(err))
		return
	}
	defer lock.Release(ctx)

	var subList []model.Subscribe
	rdb.Rdb.Model(&model.Subscribe{}).Where("status = ? AND expire_at < ?", enums.SubActive, time.Now().UTC()).Find(&subList).Limit(200)
	logger.Info("SubscriptionScheduler Start", zap.Any("subList", len(subList)))
	for _, sub := range subList {
		if sub.Vendor == enums.AppleStore {
			err := s.FixAppleSub(sub)
			if err != nil {
				common.SendServerAlarmMsg(context.Background(), 0, "", fmt.Sprintf("SubscriptionScheduler fixAppleSub err, sub_id: %d, err: %v", sub.ID, err))
				logger.Error("SubscriptionScheduler fixAppleSub err", zap.Error(err))
			}
		}
	}

}

func (s *SubscriptionScheduler) FixAppleSub(sub model.Subscribe) error {
	return s.applePayment.FixAppleSubscription(sub)
}
