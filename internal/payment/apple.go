package payment

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/credit"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/awa/go-iap/appstore"
	"github.com/awa/go-iap/appstore/api"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ApplePayment struct {
	appstoreClient           *appstore.Client
	appstoreApiClient        *api.StoreClient
	appstoreApiClientSandbox *api.StoreClient
}

func NewApplePayment() *ApplePayment {
	cfg := config.Configuration.Apple
	return &ApplePayment{
		appstoreApiClient: api.NewStoreClient(&api.StoreConfig{
			KeyContent: []byte(cfg.KeyContent),
			KeyID:      cfg.KeyID,
			BundleID:   cfg.BundleID,
			Issuer:     cfg.Issuer,
			Sandbox:    true,
		}),
		appstoreApiClientSandbox: api.NewStoreClient(&api.StoreConfig{
			KeyContent: []byte(cfg.KeyContent),
			KeyID:      cfg.KeyID,
			BundleID:   cfg.BundleID,
			Issuer:     cfg.Issuer,
			Sandbox:    false,
		}),
		appstoreClient: appstore.New(),
	}
}

type (
	ApplePurchaseReq struct {
		TransactionId string         `json:"transaction_id"`
		ProductId     string         `json:"product_id"`
		Context       map[string]any `json:"context"`
	}
	ApplePurchaseResp struct {
	}

	AppleRestoreReq struct {
		TransactionId string `json:"transaction_id"`
		ProductId     string `json:"product_id"`
	}
	AppleRestoreResp struct {
	}

	AppleWebhookReq struct {
		SignedPayload string `json:"signedPayload"`
	}
	AppleWebhookResp struct {
	}
)

type (
	// 创建订单信息
	AppleTransactionAdd struct {
		BundleID              string                 `json:"bundle_id"`
		ProductID             string                 `json:"product_id"`
		TransactionID         string                 `json:"transaction_id"`
		OriginalTransactionID string                 `json:"original_transaction_id"`
		ExpiresDate           time.Time              `json:"expires_date"`
		Price                 int64                  `json:"price"`
		Currency              string                 `json:"currency"`
		FromScene             enums.PaymentFromScene `json:"from_scene"`
	}
	// 删除订单信息
	AppleTransactionDel struct {
		OriginalTransactionID string                `json:"original_transaction_id"`
		ExpiresDate           time.Time             `json:"expires_date"`
		Status                enums.SubscribeStatus `json:"status"`
	}
)

// 苹果购买
func (h *ApplePayment) ApplePurchase(c *app.Context, req ApplePurchaseReq) (resp ApplePurchaseResp, err error) {
	uid := middleware.GetAccountId(c)
	jwsTransaction, err := h.GetJWSTransactionByTransactionId(c, req.TransactionId)
	if err != nil {
		logger.ErrorWithAppCtx(c, "AppleRestore getJWSTransaction", zap.Error(err), zap.Any("uid", uid), zap.Any("req", req))
		return resp, err
	}
	if req.ProductId != jwsTransaction.ProductID {
		return resp, errors.New("product_id_not_match")
	}

	transaction := AppleTransactionAdd{
		BundleID:              jwsTransaction.BundleID,
		ProductID:             jwsTransaction.ProductID,
		TransactionID:         jwsTransaction.TransactionID,
		OriginalTransactionID: jwsTransaction.OriginalTransactionId,
		ExpiresDate:           time.UnixMilli(jwsTransaction.ExpiresDate),
		Price:                 jwsTransaction.Price,
		Currency:              jwsTransaction.Currency,
		FromScene:             enums.PaymentFromScenePurchase,
	}
	if err = h.ProcessAppleTransaction(c, uid, transaction); err != nil {
		logger.ErrorWithAppCtx(c, "ApplePurchase ProcessAppleTransaction", zap.Error(err), zap.Any("uid", uid), zap.Any("transaction", transaction))
		return resp, err
	}
	return
}

// 苹果恢复购买
func (h *ApplePayment) AppleRestore(c *app.Context, req AppleRestoreReq) (resp AppleRestoreResp, err error) {
	uid := middleware.GetAccountId(c)
	jwsTransaction, err := h.GetJWSTransactionByTransactionId(c, req.TransactionId)
	if err != nil {
		logger.ErrorWithAppCtx(c, "AppleRestore getJWSTransaction", zap.Error(err), zap.Any("uid", uid), zap.Any("req", req))
		return resp, err
	}
	if req.ProductId != jwsTransaction.ProductID {
		return resp, errors.New("product_id_not_match")
	}

	transaction := AppleTransactionAdd{
		BundleID:              jwsTransaction.BundleID,
		ExpiresDate:           time.UnixMilli(jwsTransaction.ExpiresDate),
		ProductID:             jwsTransaction.ProductID,
		TransactionID:         jwsTransaction.TransactionID,
		OriginalTransactionID: jwsTransaction.OriginalTransactionId,
		Price:                 jwsTransaction.Price,
		Currency:              jwsTransaction.Currency,
		FromScene:             enums.PaymentFromSceneRestore,
	}
	if err = h.ProcessAppleTransaction(c, uid, transaction); err != nil {
		logger.ErrorWithAppCtx(c, "ApplePurchase ProcessAppleTransaction", zap.Error(err), zap.Any("uid", uid), zap.Any("transaction", transaction))
		return resp, err
	}
	return
}

// 处理苹果订单统一入口
func (h *ApplePayment) ProcessAppleTransaction(c *app.Context, uid int64, transaction AppleTransactionAdd) error {
	if transaction.BundleID != config.Configuration.Apple.BundleID {
		return errors.New("bundle_id_not_match")
	}
	logger.InfoWithAppCtx(c, "ProcessAppleTransaction", zap.Any("uid", uid), zap.Any("transaction", utils.ToJsonString(transaction)))
	productId := transaction.ProductID
	remark := utils.ToJsonString(transaction)
	productId = strings.ReplaceAll(productId, "aftertake_", "movely_") // TODO: 临时更改，需要换回去
	product := enums.GetProduct(productId, config.Configuration.Env)
	if product == nil {
		return errors.New("product not found")
	}

	// 订单重复处理判断
	var existOrder model.Order
	_err := rdb.Rdb.Model(&model.Order{}).Where("transaction_id = ?", transaction.TransactionID).First(&existOrder).Error
	if _err == nil && existOrder.ID > 0 {
		logger.InfoWithAppCtx(c, "ProcessAppleTransaction order already exists", zap.Any("order", existOrder), zap.String("transaction_id", transaction.TransactionID))
		return nil
	}

	if product.Type == enums.ProductTypeSubscribe {
		if transaction.ExpiresDate.Before(time.Now().UTC()) { // 订阅是否过期
			return errors.New("transaction_expired")
		}
		if transaction.FromScene == enums.PaymentFromSceneRestore { // restore 强限制
			var existSubscribe model.Subscribe
			_err := rdb.Rdb.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).First(&existSubscribe).Error
			if _err == nil && existSubscribe.ExpireAt.After(time.Now().UTC()) {
				return enums.ErrTransactionAlreadyExist
			}
		}
	}

	vendor := enums.AppleStore
	creditLogic := credit.Credit{}
	// 分布式lock
	ctx := context.Background()
	lock, err := rdb.RedisLock.Obtain(ctx, fmt.Sprintf("movely:apple_transaction_lock:%d", uid), time.Second*180, nil)
	if err != nil {
		logger.Warn("ProcessAppleTransaction Obtain lock err", zap.Error(err))
		return err
	}
	defer lock.Release(ctx)

	err = rdb.Rdb.Transaction(func(tx *gorm.DB) error {
		// 再次检查订单是否存在
		var order model.Order
		_err := tx.Model(&model.Order{}).Where("transaction_id = ?", transaction.TransactionID).First(&order).Error
		if _err == nil && order.ID > 0 {
			logger.InfoWithAppCtx(c, "order already exists", zap.Any("order", order), zap.String("transaction_id", transaction.TransactionID))
			return nil
		}
		// 上来先创建订单
		order = model.Order{
			ID:                    sf.Node.Generate().Int64(),
			UserID:                uid,
			ProductID:             productId,
			TransactionID:         transaction.TransactionID,
			OriginalTransactionID: transaction.OriginalTransactionID,
			ExpireAt:              transaction.ExpiresDate,
			Vendor:                vendor,
			Currency:              transaction.Currency,
			Price:                 strconv.FormatInt(transaction.Price, 10),
			OrderType:             string(product.Type),
			Remark:                remark,
			TeValue:               "",
			CreateTime:            time.Now().UTC(),
			UpdateTime:            time.Now().UTC(),
		}
		if err := tx.Create(&order).Error; err != nil {
			return err
		}

		switch product.Type {
		case enums.ProductTypeSubscribe:
			if transaction.FromScene == enums.PaymentFromScenePurchase || transaction.FromScene == enums.PaymentFromSceneSubscribe {
				// 购买和首次订阅，不处理降级问题
				var existSubscribe model.Subscribe
				rdb.Rdb.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).First(&existSubscribe)
				if existSubscribe.ID > 0 {
					existProduct := enums.GetProduct(existSubscribe.ProductID, config.Configuration.Env)
					if existProduct != nil && product != nil && product.Level > existProduct.Level {
						logger.InfoWithAppCtx(c, "processSubscription level down miss", zap.Any("existProduct", existProduct), zap.Any("newProduct", product))
						return nil
					}
				}
			}
			// 处理订阅逻辑
			if err := h.processSubscriptionDB(tx, uid, transaction); err != nil {
				return err
			}
			// 更新订阅信息
			if err := tx.Model(&model.User{}).Where("uid = ?", uid).Updates(map[string]any{
				"subscribe_product_id": productId,
			}).Error; err != nil {
				return err
			}
			// 增加视频币积分
			if err := creditLogic.RechargeCreditAdd(c, credit.RechargeCreditAddParam{
				UserId:       uid,
				BusinessType: enums.RechargeSubsBusinessType,
				Balance:      int64(product.Credits.BaseCount),
				ProductId:    productId,
				ExpireAt:     transaction.ExpiresDate.Unix(),
				Scene:        enums.SubScene,
			}); err != nil {
				return err
			}
			// 把其他用户清理掉，执行后 subscribes 表和 subscribe_users 表都只有 uid 的记录
			var subscribeUsers []model.SubscribeUser
			tx.Model(&model.SubscribeUser{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Find(&subscribeUsers)
			for _, subscribeUser := range subscribeUsers {
				if subscribeUser.UserID != uid {
					tx.Model(&model.SubscribeUser{}).Where("id = ?", subscribeUser.ID).Delete(&model.SubscribeUser{})
					tx.Model(&model.User{}).Where("uid = ?", subscribeUser.UserID).Update("subscribe_product_id", "")
					creditLogic.ClearCredits(c, subscribeUser.UserID)
					common.SendServerAlarmText(context.Background(), fmt.Sprintf("苹果订阅踢掉其他用户 err:%v, originalTransactionId: %s, userId: %v, productId: %v", err, transaction.OriginalTransactionID, subscribeUser.UserID, productId))
				}
			}
		case enums.ProductTypeOneTime:
			// 一次性购买，增加视频币积分
			count := product.Credits.BaseCount
			var user model.User
			_ = rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).First(&user)
			subscribeProduct := enums.GetProduct(user.SubscribeProductID, config.Configuration.Env)
			if subscribeProduct != nil {
				count = product.Credits.BaseCount * (100 + subscribeProduct.Credits.OnetimeAdditionalRatio) / 100
			}
			if err := creditLogic.RechargeCreditAdd(c, credit.RechargeCreditAddParam{
				UserId:       uid,
				BusinessType: enums.RechargePurBusinessType,
				Balance:      int64(count),
				ProductId:    productId,
				ExpireAt:     transaction.ExpiresDate.Unix(),
				Scene:        enums.PurScene,
			}); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		logger.ErrorWithAppCtx(c, "ProcessAppleTransaction", zap.Error(err), zap.Any("uid", uid), zap.Any("product_id", productId), zap.Any("transaction_id", transaction.TransactionID))
		return err
	}

	// 事务成功后，异步处理订单完成逻辑
	go func() {
		_err := h.finishOrder()
		if _err != nil {
			logger.ErrorWithAppCtx(c, "ProcessAppleTransaction FinishOrder", zap.Error(_err))
		}
	}()
	return nil
}

// 清理订阅统一入口
func (h *ApplePayment) ClearSubscribe(c *app.Context, transaction AppleTransactionDel) error {
	ctx := context.Background()
	lock, err := rdb.RedisLock.Obtain(ctx, fmt.Sprintf("movely:apple_transaction_lock:%s", transaction.OriginalTransactionID), time.Second*180, nil)
	if err != nil {
		logger.Warn("ClearSubscribe Obtain lock err", zap.Error(err))
		return err
	}
	defer lock.Release(ctx)

	var subscribeUsers []model.SubscribeUser
	err = rdb.Rdb.Model(&model.SubscribeUser{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Find(&subscribeUsers).Error
	if err != nil {
		logger.Error("ClearSubscribe Find subscribeUsers err", zap.Error(err), zap.Any("originalTransactionId", transaction.OriginalTransactionID))
		return err
	}

	creditLogic := credit.Credit{}

	err = rdb.Rdb.Transaction(func(tx *gorm.DB) error {

		// 更新 订阅表
		if err := tx.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Updates(map[string]any{
			"status":      transaction.Status,
			"expire_at":   transaction.ExpiresDate,
			"update_time": time.Now().UTC(),
		}).Error; err != nil {
			return err
		}

		// 把用户订阅表相关的人，全部更新
		if err := tx.Model(&model.SubscribeUser{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Updates(map[string]any{
			"expire_at":   transaction.ExpiresDate,
			"update_time": time.Now().UTC(),
		}).Error; err != nil {
			return err
		}

		// 更新用户表
		for _, subscribeUser := range subscribeUsers {
			if err := tx.Model(&model.User{}).Where("uid = ?", subscribeUser.UserID).Updates(map[string]any{
				"subscribe_product_id": "",
			}).Error; err != nil {
				return err
			}
			creditLogic.ClearCredits(c, subscribeUser.UserID)
		}

		// TODO: 是否清除币，和踢人保持统一？
		return nil
	})
	if err != nil {
		logger.ErrorWithAppCtx(c, "ClearSubscribe", zap.Error(err))
		return err
	}

	return nil
}

// 苹果回调处理统一入口
func (h *ApplePayment) ProcessAppleWebhook(c *app.Context, req AppleWebhookReq) (resp AppleWebhookResp, err error) {
	signedPayload := req.SignedPayload
	var payload appstore.SubscriptionNotificationV2DecodedPayload
	err = h.appstoreClient.ParseNotificationV2WithClaim(signedPayload, &payload)
	if err != nil {
		return resp, fmt.Errorf("parse signed payload error: %w", err)
	}
	var transactionInfo appstore.JWSTransactionDecodedPayload
	err = h.appstoreClient.ParseNotificationV2WithClaim(string(payload.Data.SignedTransactionInfo), &transactionInfo)
	if err != nil {
		return resp, fmt.Errorf("parse signed transaction info error: %w", err)
	}
	var renewalInfo appstore.JWSRenewalInfoDecodedPayload
	err = h.appstoreClient.ParseNotificationV2WithClaim(string(payload.Data.SignedRenewalInfo), &renewalInfo)
	if err != nil {
		return resp, fmt.Errorf("parse signed renewal info error: %w", err)
	}
	// 记录日志
	h.addWebhookSubscribeLog(payload, transactionInfo, renewalInfo)

	expireAt := time.UnixMilli(transactionInfo.ExpiresDate)

	switch payload.NotificationType {
	case appstore.NotificationTypeV2Subscribed, appstore.NotificationTypeV2DidRenew: // 订阅 和 续订
		transaction := AppleTransactionAdd{
			BundleID:              transactionInfo.BundleId,
			ExpiresDate:           expireAt,
			ProductID:             transactionInfo.ProductId,
			TransactionID:         transactionInfo.TransactionId,
			OriginalTransactionID: transactionInfo.OriginalTransactionId,
			Price:                 transactionInfo.Price,
			Currency:              transactionInfo.Currency,
			FromScene:             enums.PaymentFromSceneSubscribe,
		}
		if payload.NotificationType == appstore.NotificationTypeV2DidRenew {
			transaction.FromScene = enums.PaymentFromSceneRenew
		}

		// var subscribeUsers []model.SubscribeUser
		// rdb.Rdb.Model(&model.SubscribeUser{}).Where("original_transaction_id = ?", transactionInfo.OriginalTransactionId).Find(&subscribeUsers)
		// for _, subscribeUser := range subscribeUsers {
		// 	_err := h.ProcessAppleTransaction(c, subscribeUser.UserID, transaction)
		// 	if _err != nil {
		// 		logger.ErrorWithAppCtx(c, "processAppleWebhook ProcessAppleTransaction", zap.Error(_err), zap.Any("uid", subscribeUser.UserID))
		// 	}
		// }
		var subscribe model.Subscribe
		rdb.Rdb.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transactionInfo.OriginalTransactionId).First(&subscribe)
		if subscribe.ID > 0 {
			_err := h.ProcessAppleTransaction(c, subscribe.UserID, transaction)
			if _err != nil {
				logger.ErrorWithAppCtx(c, "processAppleWebhook ProcessAppleTransaction", zap.Error(_err), zap.Any("uid", subscribe.UserID))
			}
		}

	case appstore.NotificationTypeV2Expired, appstore.NotificationTypeV2GracePeriodExpired: // 到期
		_err := h.ClearSubscribe(c, AppleTransactionDel{
			OriginalTransactionID: transactionInfo.OriginalTransactionId,
			ExpiresDate:           expireAt,
			Status:                enums.SubExpired,
		})
		if _err != nil {
			logger.ErrorWithAppCtx(c, "processAppleWebhook ClearSubscribe Expired", zap.Error(_err))
		}
	case appstore.NotificationTypeV2Refund: // 退款
		_err := h.ClearSubscribe(c, AppleTransactionDel{
			OriginalTransactionID: transactionInfo.OriginalTransactionId,
			ExpiresDate:           expireAt,
			Status:                enums.SubRefund,
		})
		if _err != nil {
			logger.ErrorWithAppCtx(c, "processAppleWebhook ClearSubscribe Refund", zap.Error(_err))
		}
	case appstore.NotificationTypeV2DidFailToRenew: // 续订失败，只打点
	case appstore.NotificationTypeV2DidChangeRenewalStatus: // 取消订阅，只打点
		// // 自动订阅打点
		// var renewStats string
		// if payload.Subtype == "AUTO_RENEW_DISABLED" {
		// 	renewStats = "off"
		// } else if payload.Subtype == "AUTO_RENEW_ENABLED" {
		// 	renewStats = "on"
		// }
		// if len(renewStats) > 0 && sub.UserID > 0 {
		// 	utils.UpdateUserRenewalStatus(l.ctx, l.svcCtx, sub.ProductID, sub.UserID, renewStats)
		// }
		// if payload.Subtype == "AUTO_RENEW_DISABLED" {
		// 	// 取消订阅打点
		// }
	}

	return resp, nil
}

func (h *ApplePayment) GetJWSTransactionByTransactionId(c *app.Context, transactionId string) (*api.JWSTransaction, error) {
	ctx := context.Background()
	transactionInfo, err := h.appstoreApiClient.GetTransactionInfo(ctx, transactionId)
	if err != nil {
		transactionInfo, err = h.appstoreApiClientSandbox.GetTransactionInfo(ctx, transactionId) // 用沙盒来一遍
		if err != nil {
			return nil, err
		}
	}
	jwsTransaction, err := h.appstoreApiClient.ParseSignedTransaction(transactionInfo.SignedTransactionInfo)
	if err != nil {
		return nil, err
	}
	if transactionId != jwsTransaction.TransactionID {
		return nil, errors.New("transaction_id_not_match")
	}
	return jwsTransaction, nil
}

func (h *ApplePayment) FixAppleSubscription(sub model.Subscribe) error {
	originalTransactionId := sub.OriginalTransactionID
	ctx := context.Background()
	c := &app.Context{}

	allSubscriptionStatuses, err := h.appstoreApiClient.GetALLSubscriptionStatuses(ctx, originalTransactionId, nil)
	if err != nil {
		allSubscriptionStatuses, err = h.appstoreApiClientSandbox.GetALLSubscriptionStatuses(ctx, originalTransactionId, nil)
		if err != nil {
			logger.Error("FixAppleSubscription GetALLSubscriptionStatuses err", zap.Error(err))
			return err
		}
	}

	needWipe := len(allSubscriptionStatuses.Data) > 0
	for _, data := range allSubscriptionStatuses.Data {
		for _, lastTransaction := range data.LastTransactions {
			if lastTransaction.OriginalTransactionId != originalTransactionId {
				continue
			}

			// 找到对应的订单信息
			jwsTransaction, err := h.appstoreApiClient.ParseSignedTransaction(lastTransaction.SignedTransactionInfo)
			if err != nil {
				logger.Error("FixAppleSubscription ParseSignedTransaction err", zap.Error(err))
				continue
			}

			expireAt := time.UnixMilli(jwsTransaction.ExpiresDate)
			if expireAt.Before(time.Now().UTC()) { // 过期了
				_err := h.ClearSubscribe(c, AppleTransactionDel{
					OriginalTransactionID: originalTransactionId,
					Status:                enums.SubExpired,
					ExpiresDate:           expireAt,
				})
				if _err != nil {
					logger.Error("FixAppleSubscription ClearSubscribe err", zap.Error(_err), zap.Any("originalTransactionId", originalTransactionId), zap.Any("expireAt", expireAt))
				}
				common.SendServerAlarmText(context.Background(), fmt.Sprintf("修复苹果订阅（变为过期） err:%v, originalTransactionId: %s, expireAt: %v", _err, originalTransactionId, expireAt))
			} else { // 未过期，更新一下
				_err := h.ProcessAppleTransaction(c, sub.UserID, AppleTransactionAdd{
					BundleID:              jwsTransaction.BundleID,
					ProductID:             jwsTransaction.ProductID,
					TransactionID:         jwsTransaction.TransactionID,
					OriginalTransactionID: originalTransactionId,
					ExpiresDate:           expireAt,
					Price:                 jwsTransaction.Price,
					Currency:              jwsTransaction.Currency,
					FromScene:             enums.PaymentFromSceneSchedule,
				})
				if _err != nil {
					logger.Error("FixAppleSubscription ProcessAppleTransaction err", zap.Error(_err), zap.Any("originalTransactionId", originalTransactionId), zap.Any("expireAt", expireAt))
				}
				common.SendServerAlarmText(context.Background(), fmt.Sprintf("修复苹果订阅（正常续期） err:%v, originalTransactionId: %s, expireAt: %v", _err, originalTransactionId, expireAt))
			}
			needWipe = false
		}
	}

	if needWipe {
		rdb.Rdb.Model(&model.Subscribe{}).Where("id = ?", sub.ID).Update("status", enums.SubWipe)
		common.SendServerAlarmText(context.Background(), fmt.Sprintf("修复苹果订阅（异常清理）, originalTransactionId: %s", originalTransactionId))
	}
	return nil
}

func (h *ApplePayment) processSubscriptionDB(tx *gorm.DB, uid int64, transaction AppleTransactionAdd) error {
	productId := transaction.ProductID
	remark := utils.ToJsonString(transaction)

	var subscribe model.Subscribe
	err := tx.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).First(&subscribe).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新订阅
		subscribe = model.Subscribe{
			ID:                    sf.Node.Generate().Int64(),
			UserID:                uid,
			ProductID:             productId,
			TransactionID:         transaction.TransactionID,
			OriginalTransactionID: transaction.OriginalTransactionID,
			ExpireAt:              transaction.ExpiresDate,
			Status:                enums.SubActive,
			Vendor:                enums.AppleStore,
			Remark:                remark,
			TeValue:               "",
			CreateTime:            time.Now().UTC(),
			UpdateTime:            time.Now().UTC(),
		}
		if err := tx.Create(&subscribe).Error; err != nil {
			return err
		}
	} else if err != nil {
		return err
	} else {
		// 更新现有订阅
		if err := tx.Model(&model.Subscribe{}).Where("original_transaction_id = ?", transaction.OriginalTransactionID).Updates(map[string]any{
			"user_id":        uid,
			"product_id":     productId,
			"transaction_id": transaction.TransactionID,
			"expire_at":      transaction.ExpiresDate,
			"status":         enums.SubActive,
			"remark":         remark,
			"update_time":    time.Now().UTC(),
		}).Error; err != nil {
			return err
		}
	}

	// 创建或更新当前用户记录
	subscribeUser := model.SubscribeUser{
		ID:                    sf.Node.Generate().Int64(),
		SubscribeID:           subscribe.ID,
		UserID:                uid,
		ProductID:             productId,
		OriginalTransactionID: transaction.OriginalTransactionID,
		TransactionID:         transaction.TransactionID,
		ExpireAt:              transaction.ExpiresDate,
		Vendor:                enums.AppleStore,
		Remark:                remark,
		CreateTime:            time.Now().UTC(),
		UpdateTime:            time.Now().UTC(),
	}
	err = tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "subscribe_id"}, {Name: "user_id"}}, // 需要在 subscribe_id 和 user_id 上有唯一索引
		DoUpdates: clause.AssignmentColumns([]string{
			"product_id",
			"original_transaction_id",
			"transaction_id",
			"expire_at",
			"update_time",
		}),
	}).Create(&subscribeUser).Error
	if err != nil {
		return err
	}

	return nil
}

func (h *ApplePayment) finishOrder() (err error) {
	// var teValue middleware.TeIncomingValue
	// err2 := json.Unmarshal([]byte(req.TeValue), &teValue)
	// if err2 != nil {
	// 	l.Errorf("CreateOrderLogic json.Unmarshal err:%v, teValue: %v", err2, req.TeValue)
	// }
	// l.ctx = middleware.SetTeIncomingValue(l.ctx, teValue)

	// userIP := teValue.ClientIp
	// if userIP == "" {
	// 	userIP = middleware.GetUserAddressValue(l.ctx).ClientIp
	// }

	// price := Price2Amount(req.Price, req.Vendor)
	// currency := strings.ToUpper(req.Currency)
	// idfv := teValue.IDFV
	// idfa := teValue.IDFA
	// if len(idfv) == 0 {
	// 	u, _ := l.svcCtx.Client.User.Get(l.ctx, req.UserID)
	// 	if u != nil {
	// 		idfv = u.Idfv
	// 	}
	// }
	// if len(idfv) == 0 {
	// 	logx.WithContext(l.ctx).Error("idfv len 0")
	// }
	// switch v := enums.GetProduct(req.ProductID, l.svcCtx.Config.EnvTag); v.Type {
	// case enums.OneTimePayment: // 一次性购买，视频金币
	// 	if err = utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, req.UserID, "recharge", map[string]interface{}{
	// 		"order_id":                req.ID,
	// 		"product_id":              req.ProductID,
	// 		"product_price_usd":       v.Usd,
	// 		"transaction_id":          req.TransID,
	// 		"pay_type":                currency,
	// 		"pay_amount":              price,
	// 		"pay_amount_new":          price,
	// 		"pay_reason":              v.PayReason,
	// 		"endpoint":                "backend",
	// 		"pay_channel":             req.Vendor,
	// 		"pay_flow":                req.TeParams["pay_flow"],
	// 		"purchase_functiontype":   req.TeParams["purchase_functiontype"],
	// 		"purchase_functiondetail": req.TeParams["purchase_functiondetail"],
	// 		"product_type":            req.TeParams["product_type"],
	// 	}, nil); err != nil {
	// 		logx.Errorf("te register track failed, user id: %d, err: %v", req.UserID, err)
	// 	}

	// 	utils.TDUserAdd(l.ctx, l.svcCtx, req.UserID, "", map[string]any{
	// 		"total_recharge_count":       1,
	// 		"total_recharge_payment_usd": v.Usd,
	// 	})

	// case enums.Subscription, enums.OneTimeSubscription, enums.VideoCoinSubscription:
	// 	asyncGetAdjust(l.ctx, l.svcCtx, idfv, idfa, price, currency)
	// 	if req.TrialProduct && !req.FinishTrial {
	// 		price = 0
	// 		asyncAdjustSubscribeFb(l.ctx, l.svcCtx, idfv, idfa, price, currency, l.svcCtx.Config.Adjust.FbStartTrialEventToken, req.ID, userIP)
	// 	} else {
	// 		asyncAdjustSubscribeFb(l.ctx, l.svcCtx, idfv, idfa, price, currency, l.svcCtx.Config.Adjust.FbSubscribeEventToken, req.ID, userIP)
	// 	}
	// 	if err = utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, req.UserID, "subscription", map[string]interface{}{
	// 		"order_id":                req.ID,
	// 		"product_id":              req.ProductID,
	// 		"product_price_usd":       v.Usd,
	// 		"pay_type":                currency,
	// 		"pay_amount":              price,
	// 		"pay_amount_new":          price,
	// 		"pay_reason":              v.PayReason,
	// 		"endpoint":                "backend",
	// 		"pay_channel":             req.Vendor,
	// 		"new_duration":            utils.Membership2Duration(v.MemberShip),
	// 		"new_plan":                utils.Membership2Plan(v.MemberShip),
	// 		"order_type":              req.TeParams["order_type"],
	// 		"original_duration":       req.TeParams["original_duration"],
	// 		"original_plan":           req.TeParams["original_plan"],
	// 		"used_duration_day":       req.TeParams["used_duration_day"],
	// 		"is_free_trial":           req.TrialProduct && !req.FinishTrial,
	// 		"original_status":         req.TeParams["original_status"],
	// 		"current_status":          req.TeParams["current_status"],
	// 		"pay_flow":                req.TeParams["pay_flow"],
	// 		"purchase_functiontype":   req.TeParams["purchase_functiontype"],
	// 		"purchase_functiondetail": req.TeParams["purchase_functiondetail"],
	// 		"is_one_time":             req.IsOneTime,
	// 		"product_type":            req.TeParams["product_type"],
	// 		"original_status_addon":   req.TeParams["original_status_addon"],
	// 		"current_status_addon":    req.TeParams["current_status_addon"],
	// 	}, nil); err != nil {
	// 		logx.Errorf("te register track failed, user id: %d, err: %v", req.UserID, err)
	// 	}

	// 	if err = utils.TDUserAdd(l.ctx, l.svcCtx, req.UserID, "", map[string]interface{}{
	// 		"total_subscription_count":       1,
	// 		"total_subscription_payment_usd": v.Usd,
	// 		currency:                         price,
	// 	}); err != nil {
	// 		logx.Errorf("te UserSet track failed, user id: %d, err: %v", req.UserID, err)
	// 	}

	// 	if req.TeParams["order_type"] == "到期续期" && enums.IsSubscriptionWeek(req.ProductID) {
	// 		_err := user.NewGetUserResourcesLogic(l.ctx, l.svcCtx).AddUserVideoRewardCoin(
	// 			req.UserID, enums.VIDEO_REWARD_NOTYEAR_MAX_COIN, req.TransID, req.OriginTransID, req.ProductID, time.Now().UTC().Unix(),
	// 			enums.RewardCoinSceneSubscriptionRenew)
	// 		if _err != nil {
	// 			logx.Errorf("AddUserVideoRewardCoin subscription_renew_reward err: %v", _err)
	// 		}
	// 	}
	// 	if req.TeParams["order_type"] == "到期续期" && enums.IsVideoCoinSubscriptionMonth(req.ProductID) {
	// 		_err := user.NewGetUserResourcesLogic(l.ctx, l.svcCtx).AddUserVideoSubscriptionCoin(enums.AddUserVideoSubscriptionCoinReq{
	// 			UserId:          req.UserID,
	// 			Count:           int64(v.Coin.VideoCoin),
	// 			TransId:         req.TransID,
	// 			OriginTransId:   req.OriginTransID,
	// 			ExpireAt:        req.ExpireAt.UnixMilli(),
	// 			ProductId:       req.ProductID,
	// 			Scene:           enums.RewardCoinSceneSubscriptionRenewVideo,
	// 			IsCheckOriginId: false,
	// 		})
	// 		if _err != nil {
	// 			logx.Errorf("AddUserVideoSubscriptionCoin subscription_renew_reward err: %v", _err)
	// 		}
	// 	}
	// }

	// asyncAdjustPurchaseFb(l.ctx, l.svcCtx, idfv, idfa, price, currency, req.ID)
	// if price > 0 {
	// 	asyncAdjustPurchaseRealFb(l.ctx, l.svcCtx, idfv, idfa, price, currency, req.ID, userIP)
	// }
	return
}

func (h *ApplePayment) addWebhookSubscribeLog(payload appstore.SubscriptionNotificationV2DecodedPayload, transactionInfo appstore.JWSTransactionDecodedPayload, renewalInfo appstore.JWSRenewalInfoDecodedPayload) {
	subscribeLog := model.SubscribeLog{
		// TraceID:               trace.TraceIDFromContext(l.ctx),
		NotificationType:      string(payload.NotificationType),
		ProductID:             transactionInfo.ProductId,
		TransactionID:         transactionInfo.TransactionId,
		OriginalTransactionID: transactionInfo.OriginalTransactionId,
		PurchaseDate:          time.UnixMilli(transactionInfo.PurchaseDate),
		ExpiresDate:           time.UnixMilli(transactionInfo.ExpiresDate),
		PayloadInfo:           utils.ToJsonString(payload),         // https://developer.apple.com/documentation/appstoreservernotifications/responsebodyv2decodedpayload
		TransactionInfo:       utils.ToJsonString(transactionInfo), // https://developer.apple.com/documentation/appstoreservernotifications/jwstransactiondecodedpayload
		RenewalInfo:           utils.ToJsonString(renewalInfo),     // https://developer.apple.com/documentation/appstoreservernotifications/jwsrenewalinfodecodedpayload
	}
	rdb.Rdb.Create(&subscribeLog)
}
