package video

import (
	"context"
	"errors"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
)

type (
	InvokeVideoGenerationParams struct {
		TaskID           string            `json:"task_id"`
		PromptKey        string            `json:"prompt_key"` // 是 generate_type...
		Prompt           string            `json:"prompt"`
		NegativePrompt   string            `json:"negative_prompt"`
		FirstFrameImage  string            `json:"first_frame_image"`
		EffectImageList  []string          `json:"effect_image_list"`
		CfgScale         float32           `json:"cfg_scale"`
		AspectRatio      string            `json:"aspect_ratio"`
		InferParams      string            `json:"infer_params"`
		AbParams         string            `json:"ab_params"`
		CoinType         string            `json:"coin_type"`
		ExpectedDuration string            `json:"expected_duration"`
		FromThirdAssetID string            `json:"from_third_asset_id"`
		Body2VideoParams *Body2VideoParams `json:"body2video_params"`
		TasksVideo       model.TasksVideo  `json:"tasks_video"`
	}

	Body2VideoParams struct {
		// 辅助参数
		UserId     int64  `json:"user_id"`
		Error      string `json:"error"`
		GenTraceID string `json:"gen_trace_id"`
		RetryCount int    `json:"retry_count"`
		// 输入
		OriginVideoUrl      string `json:"origin_video_url"`
		BodyFirstFrameImage string `json:"body_first_frame_image"`
		BodyFirstFrameMask  string `json:"body_first_frame_mask"`
		// 输出
		BreastFullImage       string  `json:"breast_full_image"`
		VideoMaskTracking     string  `json:"video_mask_tracking"`
		VideoBreastFull       string  `json:"video_breast_full"`
		BreastFullImageSeed   int64   `json:"breast_full_image_seed"`
		VideoBreastFullSeed   int64   `json:"video_breast_full_seed"`
		BreastFullImageCost   float64 `json:"breast_full_image_cost"`
		VideoMaskTrackingCost float64 `json:"video_mask_tracking_cost"`
		VideoBreastFullCost   float64 `json:"video_breast_full_cost"`
		MergeVideo            string  `json:"merge_video"`
		MergeVideoCost        float64 `json:"merge_video_cost"`
	}

	InvokeVideoGenerationResult struct {
		ApiType         string         `json:"api_type"`
		TaskID          string         `json:"task_id"`
		RuntimeAbParams map[string]any `json:"runtime_ab_params"`
	}

	invokeVideoGenerationResult struct {
		InvokeVideoGenerationResult `json:",inline"`

		BaseResp BaseResp `json:"base_resp"`
	}

	invokeKlingResult struct {
		KlingBaseResp `json:",inline"`

		Data InvokeVideoGenerationResult `json:"data"`
	}

	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	}

	KlingBaseResp struct {
		Code      int    `json:"code"`
		Message   string `json:"message"`
		RequestID string `json:"request_id"`
	}
)

// 创建任务
func InvokeVideoGeneration(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	switch params.TasksVideo.Thirdparty {
	case "kling":
		return NewKlingSDK().InvokeVideoGeneration(ctx, params)
	case "wan":
		return NewWanSDK().InvokeVideoGeneration(ctx, params)
	case "pixverse":
		return NewPixverseSDK().InvokeVideoGeneration(ctx, params)
	case "minimax":
		return NewMinimaxSDK().InvokeVideoGeneration(ctx, params)
	default:
		return res, "", errors.New("invalid thirdparty")
	}
}

// 查询任务
func QueryVideoGeneration(ctx context.Context, thirdPartyName string, thirdId string, apiType string) (res *QueryVideoGenerationResult, traceID string, err error) {
	switch thirdPartyName {
	case "kling":
		return NewKlingSDK().QueryVideoGeneration(ctx, thirdId)
	case "wan":
		return NewWanSDK().QueryVideoGeneration(ctx, thirdId)
	case "pixverse":
		return NewPixverseSDK().QueryVideoGeneration(ctx, thirdId)
	case "minimax":
		return NewMinimaxSDK().QueryVideoGeneration(ctx, thirdId)
	default:
		return res, "", errors.New("invalid thirdparty")
	}
}

// 获取结果并保存到 s3
func FetchAndResaveVideoResult(ctx context.Context, thirdPartyName string, fileID string, apiType string) (res *FetchVideoGenerationResult, traceID string, err error) {
	switch thirdPartyName {
	case "kling":
		return NewKlingSDK().FetchAndResaveVideoResult(ctx, fileID)
	case "wan":
		return NewWanSDK().FetchAndResaveVideoResult(ctx, fileID)
	case "pixverse":
		return NewPixverseSDK().FetchAndResaveVideoResult(ctx, fileID)
	case "minimax":
		return NewMinimaxSDK().FetchAndResaveVideoResult(ctx, fileID)
	default:
		return res, "", errors.New("invalid thirdparty")
	}
}
