package video

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync/atomic"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/credit"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"github.com/hibiken/asynq"
	"github.com/sourcegraph/conc/pool"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type VideoTaskCreateScheduler struct{}

func NewVideoTaskCreateScheduler() *VideoTaskCreateScheduler {
	return &VideoTaskCreateScheduler{}
}

func (h *VideoTaskCreateScheduler) ScheduleCreateAll() {
	platforms := []string{"kling", "wan", "pixverse", "minimax"} // TODO: 从配置中获取
	for _, platform := range platforms {
		go func() {
			h.ScheduleCreateTaskByPlatType(platform)
		}()
	}
}

// 平台维度的执行队列检查
func (h *VideoTaskCreateScheduler) ScheduleCreateTaskByPlatType(thirdPartyName string) {
	startTime := time.Now()
	defer func() {
		if err := recover(); err != nil {
			logger.Error("ScheduleCreateTaskByPlatType", zap.String("panic", cast.ToString(err)), zap.String("stack", utils.GetStackInfo()))
		}
		logger.Info("ScheduleCreateTaskByPlatType", zap.String("thirdPartyName", thirdPartyName), zap.Duration("costTime", time.Since(startTime)))
	}()

	logger.Info("ScheduleCreateTaskByPlatType scan list processing", zap.String("thirdPartyName", thirdPartyName))

	// lock := l.lockSchedule(thirdPartyName)
	// if ok, err := lock.Acquire(); err != nil || !ok {
	// 	l.Infof("ScheduleCheckQueue: do not get lock, thirdPartyName: %s", thirdPartyName)
	// 	return
	// }
	// defer func() {
	// 	_, _ = lock.Release()
	// }()

	var dbScanLimit = 200
	//if l.svcCtx.Config.EnvTag == "dev" {
	//	dbScanLimit = 3
	//}

	var executedQueue []*model.TasksVideo // 平台5分钟内已经执行成功的任务
	if err := rdb.Rdb.Where("status = ? AND thirdparty = ? AND create_time > ?",
		int32(enums.TaskStatus_finished), thirdPartyName, time.Now().UTC().Add(-5*time.Minute)).
		Order("create_time ASC").Limit(dbScanLimit).Find(&executedQueue).Error; err != nil {
		logger.Errorf("ScheduleCheckQueue: scan queue failed: %v", err)
		return
	}
	var executingQueue []*model.TasksVideo // 平台正在执行中的任务
	if err := rdb.Rdb.Where("status IN ? AND thirdparty = ?",
		[]int32{int32(enums.TaskStatus_created), int32(enums.TaskStatus_resaving)}, thirdPartyName).
		Order("create_time ASC").Limit(dbScanLimit).Find(&executingQueue).Error; err != nil {
		logger.Errorf("ScheduleCheckQueue: scan queue failed: %v", err)
		return
	}
	var pendingQueue []*model.TasksVideo // 平台优先队列里的排队任务
	if err := rdb.Rdb.Where("status = ? AND priority = 0 AND thirdparty = ?",
		int32(enums.TaskStatus_queuing), thirdPartyName).
		Order("create_time ASC").Limit(dbScanLimit).Find(&pendingQueue).Error; err != nil {
		logger.Errorf("ScheduleCheckQueue: scan queue failed: %v", err)
		return
	}
	recentlyCnt := make(map[int64]int)
	for _, task := range executedQueue {
		recentlyCnt[task.UserID]++
	}
	for _, task := range executingQueue {
		recentlyCnt[task.UserID]++
	}
	for _, task := range pendingQueue {
		recentlyCnt[task.UserID]++
	}

	// 获取队列数据
	getQueueByPriority := func(priority int32) ([]*model.TasksVideo, error) {
		var queue []*model.TasksVideo
		for loopCnt := 0; ; loopCnt++ {
			if loopCnt > 10000 {
				logger.Errorf("getQueueByPriority: loop too many times")
				break
			}
			var lastCreateTime time.Time
			if len(queue) > 0 {
				lastCreateTime = queue[len(queue)-1].CreateTime
			}
			whereStmt := "status = ? AND priority = ? AND thirdparty = ?"
			whereArgs := []any{int32(enums.TaskStatus_queuing), priority, thirdPartyName}
			if !lastCreateTime.IsZero() {
				whereStmt += " AND create_time > ?"
				whereArgs = append(whereArgs, lastCreateTime)
			}
			var tmpRes []*model.TasksVideo
			if err := rdb.Rdb.Where(whereStmt, whereArgs...).
				Order("create_time ASC").Limit(dbScanLimit).Find(&tmpRes).Error; err != nil {
				logger.Errorf("getQueueByPriority: scan queue failed: %v, priority: %d", err, priority)
				return nil, err
			}
			if len(tmpRes) == 0 {
				break
			}
			queue = append(queue, tmpRes...)
		}
		return queue, nil
	}
	// 用户去重
	distinct := func(queue []*model.TasksVideo) (res []*model.TasksVideo, remain []*model.TasksVideo) {
		idSet := make(map[int64]struct{})
		for i := range queue {
			task := queue[i]
			if _, has := idSet[task.UserID]; !has {
				idSet[task.UserID] = struct{}{}
				res = append(res, task)
			} else {
				remain = append(remain, task)
			}
		}
		return res, remain
	}
	// 普通队列 转到 优先级队列里
	transferQueue := func(queue []*model.TasksVideo) (res []*model.TasksVideo, remain []*model.TasksVideo) {
		var tmpRes []*model.TasksVideo
		tmpRes, remain = distinct(queue)

		// 过滤掉「执行中任务+最近5分钟生成数」之和大于等于3的
		res = make([]*model.TasksVideo, 0, len(tmpRes))
		var userRecentlyMax = 3
		if thirdPartyName == "wan" {
			userRecentlyMax = 7 // 2025-05-09 临时调大
		}
		for _, v := range tmpRes {
			if recentlyCnt[v.UserID] < userRecentlyMax {
				res = append(res, v)
			}
		}
		for i, task := range res {
			logger.Infof("transferQueue: start transferTask task_id: %s, id: %d, third: %s", task.ID, task.ID, task.Thirdparty)
			recentlyCnt[task.UserID]++
			nowt := time.Now().UTC().Add(time.Duration(i) * time.Microsecond)
			waitQueueDuration := time.Now().UTC().Sub(task.FixedCreateTime).Seconds()
			// 优先级改0，重置了创建时间和更新时间
			res[i].Priority = 0
			res[i].CreateTime = nowt
			res[i].UpdateTime = nowt
			res[i].WaitQueueDuration = waitQueueDuration
			if err := rdb.Rdb.Model(&model.TasksVideo{}).
				Where(&model.TasksVideo{ID: task.ID}).
				Updates(map[string]any{
					"priority":            0,
					"create_time":         nowt,
					"update_time":         nowt,
					"wait_queue_duration": waitQueueDuration,
				}).Error; err != nil {
				logger.Errorf("transferQueue: update queue failed: %v, task_id: %s", err, task.ID)
				continue
			}
			// 发送长连接
			// threading.GoSafe(func() {
			// 	logicCtx := context.WithValue(context.Background(), "AccountId", task.UserID)
			// 	logic := NewVideoTaskQueryLogic(logicCtx, l.svcCtx)
			// 	taskQueryRsp, _err := logic.VideoTaskQuery(&types.VideoTaskQueryReq{TaskId: task.ID})
			// 	if _err != nil {
			// 		logx.WithContext(logicCtx).Errorf("videotaskcreatelogic transferQueue: query task failed: %v", _err)
			// 		return
			// 	}
			// 	l.svcCtx.Broadcaster.SendBroadcast(logicCtx, task.UserID, bc.CommandVideoGen, taskQueryRsp, "transferQueue")
			// })
		}
		return res, remain
	}
	// 并发上限如果不配置默认75
	concurrentLimit := 75
	//if l.svcCtx.Config.EnvTag == "dev" {
	//	concurrentLimit = 3
	//}
	// if thirdPartyName == "wan" {
	// 	concurrentLimit = utils.GetWanTotalCountV2(l.ctx, l.svcCtx)
	// 	if concurrentLimit == 0 {
	// 		if bot.EnvTag != "dev" {
	// 			bot.SendServerAlarmMsg(l.ctx, "wan平台没有机器，设置并发上限为150")
	// 		}
	// 		concurrentLimit = 150
	// 	}
	// } else if thirdPartyName == "wan_body2video" {
	// 	concurrentLimit = utils.GetBody2VideoAvailableCount(l.ctx, l.svcCtx)
	// 	if concurrentLimit == 0 {
	// 		if bot.EnvTag != "dev" {
	// 			bot.SendServerAlarmMsg(l.ctx, "wan_body2video平台没有机器，设置并发上限为150")
	// 		}
	// 		concurrentLimit = 150
	// 	}
	// }

	// if c, ok := l.svcCtx.Config.VideoGenConfig[thirdPartyName]; ok {
	// 	var limitSum int64
	// 	for _, ss := range c.Selector {
	// 		if ss.Concurrency == 0 {
	// 			// 0代表不设上限
	// 			limitSum = 0
	// 			break
	// 		}
	// 		limitSum += ss.Concurrency
	// 	}
	// 	if limitSum > 0 {
	// 		concurrentLimit = int(limitSum)
	// 	}
	// }

	// 把其他队列的数据转到优先级队列里
	oldQueueCnt := len(pendingQueue)
	logger.Infof("ScheduleCheckQueue transfer before: thirdPartyName:%s, pendingQueue:%v, executingQueue:%v, concurrentLimit:%d", thirdPartyName, len(pendingQueue), len(executingQueue), concurrentLimit)
	if len(pendingQueue)+len(executingQueue) < concurrentLimit {
		newUserQueue, _ := getQueueByPriority(100)
		oldUserQueue, _ := getQueueByPriority(200)
		logger.Infof("ScheduleCheckQueue transfer progress: thirdPartyName:%s, newUserQueue:%v, oldUserQueue:%v, sum:%d", thirdPartyName, len(newUserQueue), len(oldUserQueue), len(newUserQueue)+len(oldUserQueue))
		for loopCnt := 0; loopCnt < 10000; loopCnt++ {
			if len(pendingQueue)+len(executingQueue) >= concurrentLimit {
				break
			}
			var transferredNewUser []*model.TasksVideo
			transferredNewUser, newUserQueue = transferQueue(newUserQueue)
			pendingQueue = append(pendingQueue, transferredNewUser...)
			var transferredOldUser []*model.TasksVideo
			transferredOldUser, oldUserQueue = transferQueue(oldUserQueue)
			pendingQueue = append(pendingQueue, transferredOldUser...)
			if len(transferredNewUser) == 0 && len(transferredOldUser) == 0 {
				break
			}
		}
	}

	// 用户任务排序：确保每个用户的第一个任务优先执行，避免单个用户占用过多资源
	var sortedQueue []*model.TasksVideo
	var repeatQueue []*model.TasksVideo
	userTaskMap := make(map[int64]bool)
	for _, task := range pendingQueue {
		if _, exists := userTaskMap[task.UserID]; !exists {
			sortedQueue = append(sortedQueue, task)
			userTaskMap[task.UserID] = true
		} else {
			repeatQueue = append(repeatQueue, task)
		}
	}
	sortedQueue = append(sortedQueue, repeatQueue...)
	logger.Info("ScheduleCheckQueue transfer after", zap.String("thirdPartyName", thirdPartyName), zap.Int("pendingQueue", len(pendingQueue)), zap.Int("sortedQueue", len(sortedQueue)), zap.Int("transferCnt", len(pendingQueue)-oldQueueCnt))

	var successCnt int32
	nowT := time.Now()
	if thirdPartyName == "wan" {
		p := pool.New().WithErrors().WithMaxGoroutines(5).WithContext(context.Background())
		for _, task := range sortedQueue {
			task := task // 创建副本避免闭包问题
			p.Go(func(ctx context.Context) error {
				logger.Info("ScheduleCheckQueue: start handleQueuingTask", zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty))
				if err := h.handleQueuingTask(*task); err != nil {
					if errors.Is(err, enums.ErrReqTooFrequent) { // 机器满了直接返回，等待下轮扫描
						logger.Info("ScheduleCheckQueue: handleQueuingTask too frequent", zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty), zap.String("abParams", task.RuntimeAbParams))
						return nil
					}
					logger.Error("ScheduleCheckQueue: handleQueuingTask failed", zap.Error(err), zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty))
					return err
				}
				atomic.AddInt32(&successCnt, 1)
				return nil
			})
		}
		if err := p.Wait(); err != nil {
			logger.Errorf("ScheduleCheckQueue: pool wait failed: %v", err)
		}
	} else {
		for _, task := range sortedQueue {
			logger.Info("ScheduleCheckQueue: start handleQueuingTask", zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty))
			if err := h.handleQueuingTask(*task); err != nil {
				if errors.Is(err, enums.ErrReqTooFrequent) { // 机器满了直接返回，等待下轮扫描
					logger.Info("ScheduleCheckQueue: handleQueuingTask too frequent", zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty), zap.String("abParams", task.RuntimeAbParams))
					continue
				}
				logger.Error("ScheduleCheckQueue: handleQueuingTask failed", zap.Error(err), zap.Int64("task_id", task.ID), zap.String("third", task.Thirdparty))
			} else {
				atomic.AddInt32(&successCnt, 1)
			}
		}
	}
	logger.Info("ScheduleCheckQueue finish", zap.String("thirdPartyName", thirdPartyName), zap.Int("sortedQueue", len(sortedQueue)), zap.Int32("successCnt", successCnt), zap.Duration("costTime", time.Since(nowT)))
}

// 定时任务, 执行优先级队列任务的逻辑
func (h *VideoTaskCreateScheduler) handleQueuingTask(task model.TasksVideo) error {
	userDao := &model.User{}
	err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", task.UserID).First(userDao).Error
	if err != nil {
		logger.Errorf("handleQueuingTask: select users table failed: %v", err)
		return err
	}

	var coinInfo enums.VideoCoinInfo
	if err := json.Unmarshal([]byte(task.CoinInfo), &coinInfo); err != nil {
		logger.Errorf("handleQueuingTask: unmarshal CoinInfo failed: %v", err)
		return err
	}

	// lock := l.lockTask(task.TaskID)
	// if ok, err := lock.Acquire(); err != nil || !ok {
	// 	l.Infof("handleQueuingTask: do not get lock")
	// 	return enums.ErrReqTooFrequent
	// }
	// defer lock.Release()

	var taskTemp model.TasksVideo
	rdb.Rdb.Model(&model.TasksVideo{}).Where("id = ?", task.ID).Find(&taskTemp)
	if taskTemp.Status == int32(enums.TaskStatus_created) {
		logger.Infof("定时任务执行任务的逻辑，发现已经执行过的任务，task_id: %s, third: %s\n task:%v", task.ID, task.Thirdparty, task)
		return nil
	}
	logger.Infof("handleQueuingTask start, task: %v", utils.ToJsonString(task))

	nowt := time.Now().UTC()
	configVideo := &model.ConfigsVideo{}
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type = ?", task.GenerateType).First(configVideo).Error
	if err != nil {
		logger.Errorf("handleQueuingTask: get config video failed: %v", err)
		return err
	}

	var effectImageList []string
	// if videoConf.VideoType == enums.VideoTypeID2Video {
	// 	clipImage, effectList, err := l.handleClipFaceid(task, videoConf)
	// 	if err != nil {
	// 		if err := l.taskFailed(task, coinInfo, userDao, "not initialed", err); err != nil {
	// 			l.Errorf("handleQueuingTask: mark failed status failed: %v", err)
	// 		}
	// 		l.Errorf("handleQueuingTask: handleClipFaceid failed: %v", err)
	// 		return err
	// 	}
	// 	task.Image = clipImage
	// 	effectImageList = effectList
	// 	if videoConf.PretreatType == "faceswap2video" {
	// 		swapImage, err := l.handleFaceSwap(task, videoConf)
	// 		if err != nil {
	// 			if err := l.taskFailed(task, coinInfo, userDao, "not initialed", err); err != nil {
	// 				l.Errorf("handleQueuingTask: mark failed status failed: %v", err)
	// 			}
	// 			l.Errorf("handleQueuingTask: handleFaceSwap failed: %v", err)
	// 			return err
	// 		}
	// 		task.Image = swapImage
	// 		effectImageList = nil
	// 	}
	// }

	var coinType string
	if len(coinInfo.CostCoins) > 0 {
		coinType = strconv.Itoa(int(coinInfo.CostCoins[0].BusinessType))
	}

	var fromTaskVideo model.TasksVideo
	if task.FromTaskID != "" {
		err = rdb.Rdb.Model(&model.TasksVideo{}).Where("id = ?", task.FromTaskID).First(&fromTaskVideo).Error
		if fromTaskVideo.ThirdAssetID == "" {
			logger.Errorf("handleQueuingTask get tasks_video failed: %v, third_asset_id: %s", err, fromTaskVideo.ThirdAssetID)
			if err != nil {
				return err
			}
			return errors.New("miss third_asset_id")
		}
	}

	var body2videoParams Body2VideoParams
	_ = json.Unmarshal([]byte(task.Body2video), &body2videoParams)

	res, traceID, err := InvokeVideoGeneration(context.Background(),
		InvokeVideoGenerationParams{
			TaskID:           cast.ToString(task.ID),
			PromptKey:        task.GenerateType,
			Prompt:           task.Prompt,
			NegativePrompt:   configVideo.NegativePrompt,
			FirstFrameImage:  task.Image,
			EffectImageList:  effectImageList,
			CfgScale:         configVideo.CfgScale,
			AspectRatio:      configVideo.AspectRatio,
			InferParams:      configVideo.InferParams,
			AbParams:         configVideo.AbParams,
			CoinType:         coinType,
			ExpectedDuration: task.VideoExpectDuration,
			FromThirdAssetID: fromTaskVideo.ThirdAssetID,
			Body2VideoParams: &body2videoParams,
			TasksVideo:       task,
		})
	task.APIType = res.ApiType
	task.RuntimeAbParams = utils.ToJsonString(res.RuntimeAbParams)

	if errors.Is(err, enums.ErrReqTooFrequent) { // 机器满了直接返回，不修改状态
		return err
	} else if err != nil {
		if err := TaskFailed(task, coinInfo, userDao, traceID, err); err != nil {
			logger.Errorf("handleQueuingTask: mark failed status failed: %v", err)
		}
		logger.Errorf("handleQueuingTask: InvokeVideoGeneration failed: %v", err)
		return err
	}

	// 调用 GPU 服务成功了
	if err := rdb.Rdb.Model(&model.TasksVideo{}).
		Where(&model.TasksVideo{ID: task.ID}).
		Updates(&model.TasksVideo{
			ThirdID:         string(res.TaskID),
			APIType:         res.ApiType,
			Status:          int32(enums.TaskStatus_created),
			Reason:          NewVideoReason("", 0, "created").WithTrace(traceID).JsonStr(),
			Image:           task.Image,
			UpdateTime:      time.Now().UTC(),
			QueueDuration:   nowt.Sub(task.CreateTime).Seconds(), // 优先级队列排队时间
			CreateDuration:  time.Since(nowt).Seconds(),          // gpu创建任务时间
			RuntimeAbParams: utils.ToJsonString(res.RuntimeAbParams),
		}).Error; err != nil {
		if err := TaskFailed(task, coinInfo, userDao, traceID, err); err != nil {
			logger.Errorf("handleQueuingTask: mark failed status failed: %v", err)
		}
		logger.Errorf("handleQueuingTask: update third_id error: %v", err)
		return err
	}

	threading.GoSafe(func() {
		if task.Thirdparty != "wan" {
			return
		}
		for i := 0; i < 20; i++ {
			gpuBaseURL := h.GetGPUBaseUrl(task)
			if gpuBaseURL != "" {
				rdb.Rdb.Model(&model.TasksVideo{}).Where("id = ?", task.ID).Updates(map[string]any{"gpu_base_url": gpuBaseURL})
				break
			}
			time.Sleep(time.Second * 2)
		}
	})
	return nil
}

func (l *VideoTaskCreateScheduler) GetGPUBaseUrl(task model.TasksVideo) string {
	if task.Thirdparty != "wan" {
		return ""
	}
	info, err := l.GetVideoTaskInfo(cast.ToString(task.ID), config.Configuration.Env)
	if err != nil || info == nil {
		return ""
	}
	var result map[string]any
	_ = json.Unmarshal(info.Result, &result)
	server_id, _ := result["server_id"].(string)
	if server_id == "" {
		return ""
	}
	gpuURL := fmt.Sprintf("%s:%s", info.Queue, server_id)
	return gpuURL
}

func (l *VideoTaskCreateScheduler) GetVideoTaskInfo(id string, envTag string) (*asynq.TaskInfo, error) {
	inspector := rdb.AsynqInspector
	if inspector == nil {
		return nil, errors.New("inspector is nil")
	}
	rooms := []string{enums.WanRoomH100Us, enums.WanRoomH100Zj, enums.WanRoom4090Qy}
	var err error
	for _, room := range rooms {
		queue := envTag + ".ifonly:" + room
		info, e := inspector.GetTaskInfo(queue, id)
		if e == nil {
			return info, nil
		}
		err = errors.Join(err, e)
	}
	return nil, err
}

// TODO: 这个应该是公共的
func TaskFailed(task model.TasksVideo, coinInfo enums.VideoCoinInfo, userDao *model.User, traceID string, err error) error {
	nowt := time.Now().UTC()
	status := int32(enums.TaskStatus_failed)

	coinInfo.Status = enums.VideoCoinStatusRefunded
	coinInfoBs, _ := json.Marshal(coinInfo)

	var reason *VideoReason
	if !errors.As(err, &reason) {
		reason = &VideoReason{
			Trace: traceID,
			Msg:   err.Error(),
		}
	}
	// if reason != nil && reason.Code >= 1000 && reason.Code < 1300 {
	// 	bot.SendServerAlarmMsg(l.ctx, fmt.Sprintf("KlingSDK 调用失败，task_id：%s, 错误信息：%v", task.TaskID, err))
	// }

	// var queueLength int64
	// if err := l.svcCtx.DB.Where(&model.TasksVideo{
	// 	Status:     int32(enums.TaskStatus_queuing),
	// 	Thirdparty: task.Thirdparty,
	// }).Model(&model.TasksVideo{}).Count(&queueLength).Error; err != nil {
	// 	l.Errorf("taskFailed: get queue length failed: %v", err)
	// 	// just for track, do not return
	// }
	// var execQueueLength int64
	// if err := l.svcCtx.DB.Where(map[string]any{
	// 	"status":     int32(enums.TaskStatus_queuing),
	// 	"thirdparty": task.Thirdparty,
	// 	"priority":   0,
	// }).Model(&model.TasksVideo{}).Count(&execQueueLength).Error; err != nil {
	// 	l.Errorf("taskFailed: get exec queue length failed: %v", err)
	// 	// just for track, do not return
	// }

	// var concurrency int64
	// if err := l.svcCtx.DB.Where(&model.TasksVideo{
	// 	Status: int32(enums.TaskStatus_created), Thirdparty: task.Thirdparty,
	// }).Model(&model.TasksVideo{}).Count(&concurrency).Error; err != nil {
	// 	l.Errorf("taskFailed: get concurrency count failed: %v", err)
	// 	// just for track, do not return
	// }
	// var faceidModel model.UserVideoFaceid
	// if err := l.svcCtx.DB.Model(&faceidModel).Where("faceid = ?", task.Faceid).First(&faceidModel).Error; err != nil {
	// 	l.Errorf("taskFailed: select faceid failed: %v", err)
	// 	// just for track, do not return
	// }
	// var tag model.VideoTag
	// if err := l.svcCtx.DB.Model(&tag).Where(&model.VideoTag{
	// 	GenerateType: task.GenerateType,
	// 	TagType:      "sex",
	// 	Status:       1,
	// }).First(&tag).Error; err != nil {
	// 	l.Errorf("taskFailed: get video tag failed: %v", err)
	// 	// just for track, do not return
	// }

	costTime := nowt.Sub(task.CreateTime)

	if err := rdb.Rdb.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.TasksVideo{}).
			Where(&model.TasksVideo{ID: task.ID}).
			Updates(&model.TasksVideo{
				APIType:       task.APIType,
				Status:        status,
				Reason:        reason.JsonStr(),
				CoinInfo:      string(coinInfoBs),
				UpdateTime:    nowt,
				Duration:      costTime.Seconds(),
				InferDuration: nowt.Sub(task.UpdateTime).Seconds(),
			}).Error; err != nil {
			logger.Errorf("taskFailed: update tasks_video table failed: %v, task_id: %s", err, task.ID)
			return err
		}
		return nil
	}); err != nil {
		logger.Errorf("taskFailed: exec transaction err: %v, task_id: %s", err, task.ID)
		return err
	}

	// abtestMachine := GetAbTestMachine(task.RuntimeAbParams)
	// videoConf, _ := configs.GetVideoGenData(task.GenerateType)
	// trackData := map[string]any{
	// 	"generation_id":                   task.TaskID,
	// 	"template_id":                     task.GenerateType,
	// 	"video_ori_photo_id":              faceidModel.ImageURL,
	// 	"generate_api":                    task.APIType,
	// 	"is_trial":                        utils.IsTrial(userDao),
	// 	"wait_queue_duration":             task.WaitQueueDuration * 1e3,
	// 	"queue_duration":                  task.QueueDuration * 1e3,
	// 	"generate_duration":               costTime.Milliseconds(),
	// 	"server_total_duration":           time.Since(task.FixedCreateTime).Milliseconds(),
	// 	"generate_result":                 reason.WithoutTrace().JsonStr(),
	// 	"trace_id":                        reason.GetTrace(),
	// 	"exec_queue_length":               execQueueLength,
	// 	"queue_length":                    queueLength,
	// 	"concurrency":                     concurrency,
	// 	"title_key":                       videoConf.Title,
	// 	"title_en_name":                   configs.GetLocalizationConfig().Text("en", videoConf.Title),
	// 	"template_type":                   utils.GetVideoTemplateType(videoConf.VideoType),
	// 	"input_process_method":            videoConf.ClipType,
	// 	"template_sexy_tag":               tag.TagName,
	// 	"video_ori_photo_id_afterprocess": task.Image,
	// 	"master_template_id":              videoConf.MasterTemplate,
	// 	"abtest_machine":                  abtestMachine,
	// 	// "abtest_lora_name":                abtestLoraName,
	// 	"original_generation_id": task.FromTaskID,
	// }
	// if strings.Contains(task.APIType, "kling") {
	// 	trackData["generate_api"] = "kling_api"
	// 	trackData["video_duration"] = task.VideoExpectDuration
	// }
	// if task.FromTaskID != "" && task.Thirdparty == "kling_extend" {
	// 	var fromTaskVideo model.TasksVideo
	// 	if err = l.svcCtx.DB.Model(&model.TasksVideo{}).Where("task_id = ?", task.FromTaskID).First(&fromTaskVideo).Error; err == nil {
	// 		trackData["template_id"] = fromTaskVideo.GenerateType
	// 	}
	// 	trackData["video_duration"] = "extend_to_10s"
	// 	trackData["template_type"] = enums.VideoTypeVideo2Video
	// }
	// if task.GenerateType == "video_gen_customize" {
	// 	trackData["prompt"] = task.Prompt
	// } else if task.Prompt != videoConf.Prompt {
	// 	trackData["additional_prompt"] = strings.TrimPrefix(task.Prompt, videoConf.Prompt)
	// }
	// // 任务失败
	// if strings.Contains(task.GenerateType, "breast_video") {
	// 	td := map[string]any{
	// 		"generation_id":             task.TaskID,
	// 		"service":                   "breastplus",
	// 		"video_duration":            task.VideoExpectDuration,
	// 		"grade_category":            "round",
	// 		"grade":                     "round",
	// 		"template_type":             "video_edit_bodyplus",
	// 		"generate_api":              task.APIType,
	// 		"ori_video_id":              task.Image,
	// 		"is_trial":                  utils.IsTrial(userDao),
	// 		"waiting_and_generate_time": time.Now().Sub(task.FixedCreateTime).Seconds(),
	// 		"generate_result":           reason.WithoutTrace().JsonStr(),
	// 		"wait_queue_duration":       task.WaitQueueDuration * 1e3,
	// 		"queue_duration":            task.QueueDuration * 1e3,
	// 		"generate_duration":         costTime.Milliseconds(),
	// 		"server_total_duration":     time.Since(task.FixedCreateTime).Milliseconds(),
	// 	}
	// 	if td["video_duration"] == "" {
	// 		td["video_duration"] = "5"
	// 	}
	// 	if strings.Contains(task.APIType, "wan") {
	// 		td["generate_api"] = "wan_api"
	// 	}
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_bodyedit_generate_sever", td, nil); _err != nil {
	// 		logx.Errorf("VideoTaskCreateLogic video_bodyedit_generate_sever taskFailed: te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// } else {
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_generate_sever", trackData, nil); _err != nil {
	// 		logx.Errorf("VideoTaskCreateLogic video_generate_sever taskFailed: te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// }

	// 发送长连接  TODO: 抽出公共逻辑
	// threading.GoSafe(func() {
	// 	logicCtx := context.WithValue(l.ctx, "AccountId", task.UserID)
	// 	logic := NewVideoTaskQueryLogic(logicCtx, l.svcCtx)
	// 	taskQueryRsp, _err := logic.VideoTaskQuery(&types.VideoTaskQueryReq{TaskId: task.TaskID})
	// 	if _err != nil {
	// 		logx.WithContext(logicCtx).Errorf("videotaskcreatelogic taskFailed: query task failed: %v", _err)
	// 		return
	// 	}
	// 	l.svcCtx.Broadcaster.SendBroadcast(logicCtx, task.UserID, bc.CommandVideoGen, taskQueryRsp, "create|taskFailed")
	// })

	err = (&credit.Credit{}).RefundCredits(&app.Context{}, task.UserID, coinInfo.CostCoins)
	if err != nil {
		logger.Errorf("taskFailed: refund credit failed: %v, task_id: %d", err, task.ID)
	}
	return nil
}
