package video

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
)

type VideoTaskQueryScheduler struct{}

func NewVideoTaskQueryScheduler() *VideoTaskQueryScheduler {
	return &VideoTaskQueryScheduler{}
}

// 视频任务定时查询进度
func (l *VideoTaskQueryScheduler) ScheduleQueryAll() {
	platforms := []string{"kling", "wan", "pixverse", "minimax"} // TODO: 从配置中获取
	for _, platform := range platforms {
		threading.GoSafe(func() {
			l.ScheduleQueryByPlatType(platform)
		})
	}
}

// 定时查询创建的任务是否完成
func (l *VideoTaskQueryScheduler) ScheduleQueryByPlatType(thirdPartyName string) {
	startTime := time.Now()
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("ScheduleQueryByPlatType panic: %v, stack: %v", err, utils.GetStackInfo())
		}
		logx.Infof("ScheduleQueryByPlatType finished, thirdPartyName:%v, costTime: %v", thirdPartyName, time.Since(startTime))
	}()

	logx.Infof("ScheduleQueryByPlatType: scan list processing, thirdPartyName: %s", thirdPartyName)

	// lock := l.lockSchedule(thirdPartyName)
	// if ok, err := lock.Acquire(); err != nil || !ok {
	// 	l.Infof("ScheduleCheckAll: do not get lock, thirdPartyName: %s", thirdPartyName)
	// 	return
	// }
	// defer lock.Release()

	var dbScanLimit = 100
	//if l.svcCtx.Config.EnvTag == "dev" {
	//	dbScanLimit = 3
	//}

	// 获取所有状态为created和resaving的任务
	var taskList []model.TasksVideo
	if err := rdb.Rdb.Where("status IN ? AND thirdparty = ?",
		[]int32{int32(enums.TaskStatus_created), int32(enums.TaskStatus_resaving)}, thirdPartyName).
		Order("create_time ASC").Limit(dbScanLimit).Find(&taskList).Error; err != nil {
		logx.Errorf("ScheduleQueryByPlatType: scan task list failed: %v", err)
		return
	}

	nowt := time.Now().UTC()
	for _, task := range taskList {
		diff := nowt.Sub(task.CreateTime) // 任务创建时间与当前时间的时间差
		//if diff < 2*time.Minute { // 20250703 不到2分钟就会完成
		//	if diff < 0 {
		//		l.Errorf("ScheduleCheckAll: got invalid create_time, task_id: %s", task.TaskID)
		//	}
		//	continue
		//}
		if task.ThirdID == "" {
			if diff > 20*time.Minute {
				err := fmt.Errorf("task %v third_id has been empty for a long time", task.ID)
				logx.Errorf("ScheduleQueryByPlatType: %v", err)
				if _, err := l.taskFailed(task, "not initialed", err); err != nil {
					logx.Errorf("ScheduleQueryByPlatType: mark failed status failed: %v", err)
				}
			}
			continue
		}
		if task.Status == int32(enums.TaskStatus_resaving) {
			if diff < 20*time.Minute {
				continue
			}
		}
		logx.Infof("ScheduleQueryByPlatType: start fixTaskStatus task_id: %v", task.ID)
		if _, err := l.fixTaskStatus(task); err != nil {
			if errors.Is(err, enums.ErrReqTooFrequent) {
				logx.Infof("ScheduleQueryByPlatType: fixTaskStatus too frequent, task_id: %v", task.ID)
				continue
			}
			logx.Errorf("ScheduleQueryByPlatType: fixTaskStatus failed: %v, task_id: %v", err, task.ID)
		}
	}
}

// fixTaskStatus: 查询并修改最新任务状态
func (l *VideoTaskQueryScheduler) fixTaskStatus(task model.TasksVideo) (*models.VideoQueryResp, error) {
	if task.ThirdID == "" {
		return nil, errors.New("task third_id is empty")
	}

	// lock := l.lockTask(task.TaskID)
	// if ok, err := lock.Acquire(); err != nil || !ok {
	// 	l.Infof("fixTaskStatus: do not get lock")
	// 	return nil, enums.ReqTooFrequent
	// }
	// defer lock.Release()

	res, traceID, err := QueryVideoGeneration(context.Background(), task.Thirdparty, task.ThirdID, task.APIType)
	if err != nil {
		if _, err := l.taskFailed(task, traceID, err); err != nil {
			logx.Errorf("fixTaskStatus: mark failed status failed: %v, task_id: %v", err, task.ID)
			return nil, err
		}
		logx.Errorf("fixTaskStatus: query video generation failed: %v, task_id: %v", err, task.ID)
		return nil, err
	}

	if res.Status == VideoGenerationStatusQueueing || res.Status == VideoGenerationStatusProcessing {
		progress := float32(time.Now().Sub(task.CreateTime)) / float32(6*time.Minute) * 100
		if progress >= 100 {
			progress = 99
		}
		return &models.VideoQueryResp{
			Task: &models.VideoTask{
				Id:       cast.ToString(task.ID),
				Status:   task.Status,
				Progress: int32(progress),
			},
		}, nil
	} else if res.Status == VideoGenerationStatusSuccess {
		inferCost := time.Now().UTC().Sub(task.UpdateTime)
		logx.Infof("fixTaskStatus: success video generation, resaving task %v", task.ID)
		if err := rdb.Rdb.Model(&model.TasksVideo{}).
			Where(&model.TasksVideo{ID: task.ID}).
			Updates(&model.TasksVideo{
				Status:       int32(enums.TaskStatus_resaving), // 转存视频生成结果耗时较长，需要一个单独状态占位
				Reason:       NewVideoReason("", 0, "resaving").WithTrace(traceID).JsonStr(),
				UpdateTime:   time.Now().UTC(),
				ThirdAssetID: res.ThirdAssetID,
			}).Error; err != nil {
			logx.Errorf("fixTaskStatus: update tasks_video table failed: %v", err)
			return nil, err
		}
		video, traceID, err := FetchAndResaveVideoResult(context.Background(), task.Thirdparty, res.FileID, task.APIType)
		if err != nil {
			logx.Errorf("fixTaskStatus: FetchAndResaveVideoResult failed: %v, task_id: %v", err, task.ID)
			if err := rdb.Rdb.Model(&model.TasksVideo{}).
				Where(&model.TasksVideo{ID: task.ID}).
				Updates(&model.TasksVideo{
					Status:       int32(enums.TaskStatus_created), // 转存视频失败回归created状态，等待下次探测
					Reason:       NewVideoReason("", 0, "resave failed and reset created").WithTrace(traceID).JsonStr(),
					UpdateTime:   time.Now().UTC(),
					ThirdAssetID: res.ThirdAssetID,
				}).Error; err != nil {
				logx.Errorf("fixTaskStatus: update tasks_video table failed: %v", err)
				return nil, err
			}
			// 转存视频失败不返回error，避免中断客户端轮询
			return &models.VideoQueryResp{
				Task: &models.VideoTask{
					Id:       cast.ToString(task.ID),
					Status:   task.Status,
					Progress: 99,
				},
			}, nil
		}
		logx.Infof("fixTaskStatus: success video generation, marking task %v", task.ID)
		if rsp, err := l.taskFinished(task, traceID, video.DownloadURL, inferCost, video.ResaveCost, res.VideoInfo); err != nil {
			logx.Errorf("fixTaskStatus: mark finished status failed: %v", err)
			return nil, err
		} else {
			return rsp, nil
		}
	} else if res.Status == VideoGenerationStatusFail {
		logx.Infof("fixTaskStatus: failed video generation, task_id: %v, task: %v, res: %v", task.ID, utils.ToJsonString(task), utils.ToJsonString(res))
		if strings.Contains(res.StatusMsg, "no healthy backend") {
			diff := time.Since(task.CreateTime)
			startRetry := diff < time.Minute*4
			// bot.SendServerAlarmMsg(l.ctx, fmt.Sprintf("🎈(%v) no healthy backend, task:%v, err:%v \n diff:%v, start retry: %v", task.Thirdparty, task.TaskID, res.StatusMsg, diff, startRetry))
			if startRetry { // 再次放到排队队列
				rdb.Rdb.Model(&model.TasksVideo{}).Where("task_id = ?", task.ID).Updates(map[string]any{
					"status":   int32(enums.TaskStatus_queuing),
					"priority": 0,
				})
				return nil, nil
			}
		}
		reason := fmt.Errorf("got failed result status %s", res.StatusMsg)
		if rsp, err := l.taskFailed(task, traceID, reason); err != nil {
			logx.Errorf("fixTaskStatus: mark failed status failed: %v", err)
			return nil, err
		} else {
			return rsp, nil
		}
	} else {
		reason := fmt.Errorf("unknown result status: %s, msg: %s", res.Status, res.StatusMsg)
		if rsp, err := l.taskFailed(task, traceID, reason); err != nil {
			logx.Errorf("fixTaskStatus: mark unknown reason failed: %v", err)
			return nil, err
		} else {
			return rsp, nil
		}
	}
}

func (l *VideoTaskQueryScheduler) taskFinished(task model.TasksVideo, traceID, videoUrl string, inferCost, resaveCost time.Duration, videoInfo VideoInfo) (*models.VideoQueryResp, error) {
	nowt := time.Now().UTC()
	status := int32(enums.TaskStatus_finished)
	var userDao *model.User
	if err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", task.UserID).First(&userDao).Error; err != nil {
		logx.Errorf("taskFinished: select users table failed: %v", err)
		return nil, err
	}

	var queueLength int64
	if err := rdb.Rdb.Where(&model.TasksVideo{
		Status:     int32(enums.TaskStatus_queuing),
		Thirdparty: task.Thirdparty,
	}).Model(&model.TasksVideo{}).Count(&queueLength).Error; err != nil {
		logx.Errorf("taskFinished: get queue length failed: %v", err)
		// just for track, do not return
	}
	var execQueueLength int64
	if err := rdb.Rdb.Where(map[string]any{
		"status":     int32(enums.TaskStatus_queuing),
		"thirdparty": task.Thirdparty,
		"priority":   0,
	}).Model(&model.TasksVideo{}).Count(&execQueueLength).Error; err != nil {
		logx.Errorf("taskFinished: get exec queue length failed: %v", err)
		// just for track, do not return
	}

	var concurrency int64
	if err := rdb.Rdb.Where(&model.TasksVideo{
		Status: int32(enums.TaskStatus_created), Thirdparty: task.Thirdparty,
	}).Model(&model.TasksVideo{}).Count(&concurrency).Error; err != nil {
		logx.Errorf("taskFinished: get concurrency count failed: %v", err)
		// just for track, do not return
	}
	var coinInfo enums.VideoCoinInfo
	if err := json.Unmarshal([]byte(task.CoinInfo), &coinInfo); err != nil {
		logx.Errorf("taskFinished: unmarshal CoinInfo failed: %v", err)
		// just for track, do not return
	}
	// var faceidModel model.UserVideoFaceid
	// if err := l.svcCtx.DB.Model(&faceidModel).Where("faceid = ?", task.Faceid).First(&faceidModel).Error; err != nil {
	// 	l.Errorf("taskFinished: select faceid failed: %v", err)
	// 	// just for track, do not return
	// }
	// var tag model.VideoTag
	// if err := l.svcCtx.DB.Model(&tag).Where(&model.VideoTag{
	// 	GenerateType: task.GenerateType,
	// 	TagType:      "sex",
	// 	Status:       1,
	// }).First(&tag).Error; err != nil {
	// 	l.Errorf("taskFinished: get video tag failed: %v", err)
	// 	// just for track, do not return
	// }
	// var tokenType string
	// if len(coinInfo.CostCoins) > 0 {
	// 	tokenType = enums.VideoTypeToTrack(coinInfo.CostCoins[0].CoinType)
	// }
	// var tokenNum, creditNum int64
	// if len(coinInfo.CostCoins) > 0 {
	// 	tokenNum = coinInfo.CostCoins[0].Count
	// 	creditNum = tokenNum
	// 	// if coinInfo.CostCoins[0].Version == enums.VideoCoinCostVersion1 {
	// 	// 	creditNum = enums.GetCoin2VideoCoinCount(creditNum)
	// 	// }
	// }

	costTime := nowt.Sub(task.CreateTime)

	videoInfoBs, _ := json.Marshal(videoInfo)
	if err := rdb.Rdb.Model(&model.TasksVideo{}).
		Where(&model.TasksVideo{ID: task.ID}).
		Updates(&model.TasksVideo{
			Status:         status,
			Reason:         NewVideoReason("", 0, "success").WithTrace(traceID).JsonStr(),
			Video:          videoUrl,
			ExtInfo:        string(videoInfoBs),
			UpdateTime:     nowt,
			Duration:       costTime.Seconds(),
			InferDuration:  inferCost.Seconds(),
			ResaveDuration: resaveCost.Seconds(),
		}).Error; err != nil {
		logx.Errorf("taskFinished: update tasks_video table failed: %v", err)
		return nil, err
	}

	// machineRoom := ""
	// if strings.Contains(task.GpuBaseURL, "10-5-35") || strings.Contains(task.GpuBaseURL, "10-5-36") {
	// 	machineRoom = "美国H100"
	// } else if strings.Contains(task.GpuBaseURL, "10-133-0") {
	// 	machineRoom = "浙江H100"
	// } else if strings.Contains(task.GpuBaseURL, "10-0-114") {
	// 	machineRoom = "庆阳8卡"
	// } else if strings.Contains(task.GpuBaseURL, "seetacloud.com") {
	// 	machineRoom = "Autodl"
	// }

	// abtestMachine := GetAbTestMachine(task.RuntimeAbParams)
	// videoConf, _ := configs.GetVideoGenData(task.GenerateType)
	// trackData := map[string]any{
	// 	"generation_id":                   task.TaskID,
	// 	"template_id":                     task.GenerateType,
	// 	"video_ori_photo_id":              faceidModel.ImageURL,
	// 	"generate_api":                    task.APIType,
	// 	"is_trial":                        utils.IsTrialV2(userDao),
	// 	"wait_queue_duration":             task.WaitQueueDuration * 1e3,
	// 	"queue_duration":                  task.QueueDuration * 1e3,                        // 优先级排队时间
	// 	"generate_duration":               costTime.Milliseconds(),                         // 优先级队列 + 生成的时间 (生成时间)
	// 	"infer_duration":                  inferCost.Milliseconds(),                        // GPU时间
	// 	"server_total_duration":           time.Since(task.FixedCreateTime).Milliseconds(), // 服务端总时间(包含购物车时间)
	// 	"generate_result":                 "success",                                       // 生成成功的点
	// 	"exec_queue_length":               execQueueLength,
	// 	"queue_length":                    queueLength,
	// 	"concurrency":                     concurrency,
	// 	"token_type":                      tokenType,
	// 	"token_num":                       tokenNum,
	// 	"credit_num":                      creditNum,
	// 	"title_key":                       videoConf.Title,
	// 	"title_en_name":                   configs.GetLocalizationConfig().Text("en", videoConf.Title),
	// 	"template_type":                   utils.GetVideoTemplateType(videoConf.VideoType),
	// 	"input_process_method":            videoConf.ClipType,
	// 	"template_sexy_tag":               tag.TagName,
	// 	"video_ori_photo_id_afterprocess": task.Image,
	// 	"master_template_id":              videoConf.MasterTemplate,
	// 	"abtest_machine":                  abtestMachine,
	// 	// "abtest_lora_name":                abtestLoraName,
	// 	"original_generation_id": task.FromTaskID,
	// 	"machine_room":           machineRoom,
	// }
	// if strings.Contains(task.APIType, "kling") {
	// 	trackData["generate_api"] = "kling_api"
	// 	trackData["video_duration"] = task.VideoExpectDuration
	// }
	// if task.FromTaskID != "" && task.Thirdparty == "kling_extend" {
	// 	var fromTaskVideo model.TasksVideo
	// 	if err := l.svcCtx.DB.Model(&model.TasksVideo{}).Where("task_id = ?", task.FromTaskID).First(&fromTaskVideo).Error; err == nil {
	// 		trackData["template_id"] = fromTaskVideo.GenerateType
	// 	}
	// 	trackData["video_duration"] = "extend_to_10s"
	// 	trackData["template_type"] = enums.VideoTypeVideo2Video
	// }
	// if task.GenerateType == "video_gen_customize" {
	// 	trackData["prompt"] = task.Prompt
	// } else if task.Prompt != videoConf.Prompt {
	// 	trackData["additional_prompt"] = strings.TrimPrefix(task.Prompt, videoConf.Prompt)
	// }

	// if strings.Contains(task.GenerateType, "breast_video") {
	// 	td := map[string]any{
	// 		"generation_id":             task.TaskID,
	// 		"service":                   "breastplus",
	// 		"video_duration":            task.VideoExpectDuration,
	// 		"grade_category":            "round",
	// 		"grade":                     "round",
	// 		"template_type":             "video_edit_bodyplus",
	// 		"generate_api":              task.APIType,
	// 		"ori_video_id":              task.Image,
	// 		"is_trial":                  utils.IsTrialV2(userDao),
	// 		"waiting_and_generate_time": time.Now().Sub(task.FixedCreateTime).Seconds(),
	// 		"token_type":                tokenType,
	// 		"token_num":                 tokenNum,
	// 		"credit_num":                creditNum,
	// 		"generate_result":           "success",
	// 		"wait_queue_duration":       task.WaitQueueDuration * 1e3,
	// 		"queue_duration":            task.QueueDuration * 1e3,                        // 优先级排队时间
	// 		"generate_duration":         costTime.Milliseconds(),                         // 优先级队列 + 生成的时间 (生成时间)
	// 		"infer_duration":            inferCost.Milliseconds(),                        // GPU时间
	// 		"server_total_duration":     time.Since(task.FixedCreateTime).Milliseconds(), // 服务端总时间(包含购物车时间)
	// 	}
	// 	if td["video_duration"] == "" {
	// 		td["video_duration"] = "5"
	// 	}
	// 	if strings.Contains(task.APIType, "wan") {
	// 		td["generate_api"] = "wan_api"
	// 	}
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_bodyedit_generate_sever", td, nil); _err != nil {
	// 		logx.Errorf("taskFinished video_bodyedit_generate_sever te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// } else {
	// 	// 任务成功
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_generate_sever", trackData, nil); _err != nil {
	// 		logx.Errorf("taskFinished video_generate_sever te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// }
	// threading.GoSafe(func() {
	// 	feeds.NewFeedsBehaviorLogic(context.WithoutCancel(l.ctx), l.svcCtx).AddUserBehavior(task.UserID, enums.BehaviorTypeVideoGenerate)
	// 	track.NewUserTrackLogic(context.WithoutCancel(l.ctx), l.svcCtx).UserTrackVideoGenerate(userDao)
	// })

	// // 发送长连接
	// threading.GoSafe(func() {
	// 	logicCtx := context.WithValue(l.ctx, "AccountId", task.UserID)
	// 	logic := NewVideoTaskQueryLogic(logicCtx, l.svcCtx)
	// 	taskQueryRsp, _err := logic.VideoTaskQuery(&types.VideoTaskQueryReq{TaskId: task.TaskID})
	// 	logx.WithContext(logicCtx).Infof("videotaskquerylogic taskFinished: send broadcast, task_id: %s, taskQueryRsp: %v, _err: %v, broadcaster: %v", task.TaskID, utils.ToJsonString(taskQueryRsp), _err, l.svcCtx.Broadcaster)
	// 	if _err != nil {
	// 		logx.WithContext(logicCtx).Errorf("videotaskquerylogic taskFinished: query task failed: %v,", _err)
	// 		return
	// 	}
	// 	l.svcCtx.Broadcaster.SendBroadcast(logicCtx, task.UserID, bc.CommandVideoGen, taskQueryRsp, "taskFinished")
	// })

	return &models.VideoQueryResp{
		Task: &models.VideoTask{
			Id:                  cast.ToString(task.ID),
			Status:              status,
			Progress:            100,
			ExpectedCostSeconds: enums.GetVideoExpectedCostSeconds(task.Thirdparty),
		},
		Results: []*models.VideoAsset{{
			Id:          cast.ToString(task.ID),
			VideoUrl:    videoUrl,
			VideoWidth:  videoInfo.VideoWidth,
			VideoHeight: videoInfo.VideoHeight,
		}},
	}, nil
}

func (l *VideoTaskQueryScheduler) taskFailed(task model.TasksVideo, traceID string, err error) (*models.VideoQueryResp, error) {
	nowt := time.Now().UTC()
	status := int32(enums.TaskStatus_failed)
	// userDao, userErr := l.svcCtx.Client.User.Get(l.ctx, task.UserID)
	// if userErr != nil {
	// 	l.Errorf("taskFailed: select users table failed: %v, task_id: %s", userErr, task.TaskID)
	// 	return nil, userErr
	// }

	var coinInfo enums.VideoCoinInfo
	if err := json.Unmarshal([]byte(task.CoinInfo), &coinInfo); err != nil {
		logx.Errorf("taskFailed: unmarshal CoinInfo failed: %v, task_id: %v", err, task.ID)
		// fallthrough, do not return
	}
	coinInfo.Status = enums.VideoCoinStatusRefunded
	coinInfoBs, _ := json.Marshal(coinInfo)

	var reason *VideoReason
	if !errors.As(err, &reason) {
		reason = &VideoReason{
			Trace: traceID,
			Msg:   err.Error(),
		}
	}

	var queueLength int64
	if err := rdb.Rdb.Where(&model.TasksVideo{
		Status:     int32(enums.TaskStatus_queuing),
		Thirdparty: task.Thirdparty,
	}).Model(&model.TasksVideo{}).Count(&queueLength).Error; err != nil {
		logx.Errorf("taskFailed: get queue length failed: %v", err)
		// just for track, do not return
	}
	var execQueueLength int64
	if err := rdb.Rdb.Where(map[string]any{
		"status":     int32(enums.TaskStatus_queuing),
		"thirdparty": task.Thirdparty,
		"priority":   0,
	}).Model(&model.TasksVideo{}).Count(&execQueueLength).Error; err != nil {
		logx.Errorf("taskFailed: get exec queue length failed: %v", err)
		// just for track, do not return
	}

	var concurrency int64
	if err := rdb.Rdb.Where(&model.TasksVideo{
		Status: int32(enums.TaskStatus_created), Thirdparty: task.Thirdparty,
	}).Model(&model.TasksVideo{}).Count(&concurrency).Error; err != nil {
		logx.Errorf("taskFailed: get concurrency count failed: %v", err)
		// just for track, do not return
	}
	// var faceidModel model.UserVideoFaceid
	// if err := l.svcCtx.DB.Model(&faceidModel).Where("faceid = ?", task.Faceid).First(&faceidModel).Error; err != nil {
	// 	l.Errorf("taskFailed: select faceid failed: %v", err)
	// 	// just for track, do not return
	// }
	// var tag model.VideoTag
	// if err := l.svcCtx.DB.Model(&tag).Where(&model.VideoTag{
	// 	GenerateType: task.GenerateType,
	// 	TagType:      "sex",
	// 	Status:       1,
	// }).First(&tag).Error; err != nil {
	// 	l.Errorf("taskFailed: get video tag failed: %v", err)
	// 	// just for track, do not return
	// }

	// costTime := nowt.Sub(task.CreateTime)
	if err := rdb.Rdb.Model(&model.TasksVideo{}).
		Where(&model.TasksVideo{
			ID: task.ID,
		}).
		Where("status IN ?", []int32{
			int32(enums.TaskStatus_created),
			int32(enums.TaskStatus_resaving),
		}).
		Updates(&model.TasksVideo{
			Status:        status,
			Reason:        reason.JsonStr(),
			CoinInfo:      string(coinInfoBs),
			UpdateTime:    nowt,
			Duration:      nowt.Sub(task.CreateTime).Seconds(),
			InferDuration: nowt.Sub(task.UpdateTime).Seconds(),
		}).Error; err != nil {
		logx.Errorf("taskFailed: update tasks_video table failed: %v, task_id: %v", err, task.ID)
		return nil, err
	}

	// abtestMachine := GetAbTestMachine(task.RuntimeAbParams)
	// videoConf, _ := configs.GetVideoGenData(task.GenerateType)
	// trackData := map[string]any{
	// 	"generation_id":                   task.TaskID,
	// 	"template_id":                     task.GenerateType,
	// 	"video_ori_photo_id":              faceidModel.ImageURL,
	// 	"generate_api":                    task.APIType,
	// 	"is_trial":                        utils.IsTrial(userDao),
	// 	"wait_queue_duration":             task.WaitQueueDuration * 1e3,
	// 	"queue_duration":                  task.QueueDuration * 1e3,
	// 	"generate_duration":               costTime.Milliseconds(),
	// 	"server_total_duration":           time.Since(task.FixedCreateTime).Milliseconds(),
	// 	"generate_result":                 reason.WithoutTrace().JsonStr(),
	// 	"trace_id":                        reason.GetTrace(),
	// 	"exec_queue_length":               execQueueLength,
	// 	"queue_length":                    queueLength,
	// 	"concurrency":                     concurrency,
	// 	"title_key":                       videoConf.Title,
	// 	"title_en_name":                   configs.GetLocalizationConfig().Text("en", videoConf.Title),
	// 	"template_type":                   utils.GetVideoTemplateType(videoConf.VideoType),
	// 	"input_process_method":            videoConf.ClipType,
	// 	"template_sexy_tag":               tag.TagName,
	// 	"video_ori_photo_id_afterprocess": task.Image,
	// 	"master_template_id":              videoConf.MasterTemplate,
	// 	"abtest_machine":                  abtestMachine,
	// 	// "abtest_lora_name":                abtestLoraName,
	// 	"original_generation_id": task.FromTaskID,
	// }
	// if strings.Contains(task.APIType, "kling") {
	// 	trackData["generate_api"] = "kling_api"
	// 	trackData["video_duration"] = task.VideoExpectDuration
	// }
	// if task.FromTaskID != "" && task.Thirdparty == "kling_extend" {
	// 	var fromTaskVideo model.TasksVideo
	// 	if err = l.svcCtx.DB.Model(&model.TasksVideo{}).Where("task_id = ?", task.FromTaskID).First(&fromTaskVideo).Error; err == nil {
	// 		trackData["template_id"] = fromTaskVideo.GenerateType
	// 	}
	// 	trackData["video_duration"] = "extend_to_10s"
	// 	trackData["template_type"] = enums.VideoTypeVideo2Video
	// }
	// if task.GenerateType == "video_gen_customize" {
	// 	trackData["prompt"] = task.Prompt
	// } else if task.Prompt != videoConf.Prompt {
	// 	trackData["additional_prompt"] = strings.TrimPrefix(task.Prompt, videoConf.Prompt)
	// }
	// //	任务失败
	// if strings.Contains(task.GenerateType, "breast_video") {
	// 	td := map[string]any{
	// 		"generation_id":             task.TaskID,
	// 		"service":                   "breastplus",
	// 		"video_duration":            task.VideoExpectDuration,
	// 		"grade_category":            "round",
	// 		"grade":                     "round",
	// 		"template_type":             "video_edit_bodyplus",
	// 		"generate_api":              task.APIType,
	// 		"ori_video_id":              task.Image,
	// 		"is_trial":                  utils.IsTrial(userDao),
	// 		"waiting_and_generate_time": time.Now().Sub(task.FixedCreateTime).Seconds(),
	// 		"generate_result":           reason.WithoutTrace().JsonStr(),
	// 		"wait_queue_duration":       task.WaitQueueDuration * 1e3,
	// 		"queue_duration":            task.QueueDuration * 1e3,
	// 		"generate_duration":         costTime.Milliseconds(),
	// 		"server_total_duration":     time.Since(task.FixedCreateTime).Milliseconds(),
	// 	}
	// 	if td["video_duration"] == "" {
	// 		td["video_duration"] = "5"
	// 	}
	// 	if strings.Contains(task.APIType, "wan") {
	// 		td["generate_api"] = "wan_api"
	// 	}
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_bodyedit_generate_sever", td, nil); _err != nil {
	// 		logx.Errorf("VideoTaskQueryLogic video_bodyedit_generate_sever taskFailed te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// } else {
	// 	if _err := utils.TrackWithBaseAttributes(l.ctx, l.svcCtx, task.UserID, "video_generate_sever", trackData, nil); _err != nil {
	// 		logx.Errorf("VideoTaskQueryLogic video_generate_sever taskFailed te track failed, user id: %d, err: %v", task.UserID, _err)
	// 	}
	// }

	// if err := user.NewGetUserResourcesLogic(l.ctx, l.svcCtx).RefundVideoCoin(coinInfo.CostCoins, task.UserID); err != nil {
	// 	l.Errorf("taskFailed: RefundVideoCoin error: %v, task_id: %s", err, task.TaskID)
	// 	// fallthrough, do not return
	// }

	// if execQueueLength >= 30 {
	// 	bot.SendServerAlarmMsg(l.ctx, fmt.Sprintf("视频生成队列：\n优先级排队过高，排队数：%d，平台：%s\n\n task_id:%v, reason:%v, task: %v, track: %v",
	// 		execQueueLength, task.Thirdparty,
	// 		task.ID, reason, utils.ToJsonString(task), utils.ToJsonString(trackData)))
	// }

	// 发送长连接
	// threading.GoSafe(func() {
	// 	logicCtx := context.WithValue(l.ctx, "AccountId", task.UserID)
	// 	logic := NewVideoTaskQueryLogic(logicCtx, l.svcCtx)
	// 	taskQueryRsp, _err := logic.VideoTaskQuery(&types.VideoTaskQueryReq{TaskId: task.TaskID})
	// 	if _err != nil {
	// 		logx.WithContext(logicCtx).Errorf("videotaskquerylogic taskFailed: query task failed: %v", _err)
	// 		return
	// 	}
	// 	l.svcCtx.Broadcaster.SendBroadcast(logicCtx, task.UserID, bc.CommandVideoGen, taskQueryRsp, "schedule|taskFailed")
	// })

	return &models.VideoQueryResp{
		Task: &models.VideoTask{
			Id:       cast.ToString(task.ID),
			Status:   status,
			Progress: 0,
			Msg:      reason.JsonStr(),
		},
	}, nil
}
