package video

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	mRand "math/rand"
	"net"
	"net/http"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	WanStatusGenerating = "running"
	WanStatusSuccessful = "success"
	WanStatusFailed     = "error"
)

var WanStatusMapping = map[string]string{
	WanStatusGenerating: VideoGenerationStatusProcessing,
	WanStatusSuccessful: VideoGenerationStatusSuccess,
	WanStatusFailed:     VideoGenerationStatusFail,
}

type WanSDK struct {
	httpCliGen   *resty.Client
	httpCliQuery *resty.Client
	Endpoint     string
	// taskQueue    *aqx.Queue
}

func NewWanSDK() *WanSDK {
	endpoint := "http://internal-onlineifonlyservice-2108720070.us-west-2.elb.amazonaws.com:8001"
	if config.Configuration.Env == "dev" || config.Configuration.Env == "local" { // TODO: 走配置
		endpoint = "http://10.228.1.232:6004"
	}
	return &WanSDK{
		Endpoint:     endpoint,
		httpCliGen:   resty.New().SetTimeout(time.Second * 2),
		httpCliQuery: resty.New().SetTimeout(time.Second * 15),
	}
}

type (
	WanVideoGenerationResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			TaskID          string `json:"task_id"`
			Video           string `json:"video"`
			OptimizedPrompt string `json:"optimized_prompt"`
			Seed            int    `json:"seed"`
		} `json:"data"`
	}
	WanVideoQueryResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			TaskID   string `json:"task_id"`
			Video    string `json:"video"`
			Seed     int    `json:"seed"`
			Status   string `json:"status"`
			Progress int    `json:"progress"`
			ErrorMsg string `json:"error_msg"`
		} `json:"data"`
	}
)

func (s *WanSDK) InvokeVideoGeneration(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	data, trace, err := s.InvokeVideoGenerationSync(ctx, params)
	if err != nil {
		var netErr net.Error
		if errors.As(err, &netErr) && netErr.Timeout() { // 超时不认为错误
			return data, trace, nil
		}
		return data, trace, err
	}
	return data, trace, nil
}

func (s *WanSDK) InvokeVideoGenerationSync(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	apiType := "wan"
	res.ApiType = apiType
	res.TaskID = params.TaskID
	res.RuntimeAbParams = map[string]any{"machine_group": "wan"}
	nowt := time.Now().UTC()
	defer func() {
		var taskID = string(res.TaskID)
		logx.WithContext(ctx).Infof("WanSDK.InvokeVideoGeneration", "costTime", time.Since(nowt), "taskID", params.TaskID, "thirdTaskID", taskID, "prompt", params.Prompt, "res", utils.ToJsonString(res))
		if err != nil {
			logx.WithContext(ctx).Errorf("WanSDK.InvokeVideoGeneration", "err", err, "apiType", apiType, "taskID", params.TaskID)
		}
		res.TaskID = params.TaskID
		res.ApiType = apiType
	}()

	inferParams := make(map[string]any)
	_ = json.Unmarshal([]byte(params.InferParams), &inferParams)
	loraName, _ := inferParams["lora"].(string)
	loraWeight, _ := inferParams["lora_wight"].(float64)
	triggerWords, _ := inferParams["trigger_words"].(string)
	promptOptimizationSystemPrompt, _ := inferParams["prompt_optimization_system_prompt"].(string)
	frames, _ := inferParams["frames"].(float64)
	frameRate, _ := inferParams["frame_rate"].(float64)
	taggerStatus, _ := inferParams["tagger_status"].(float64)
	if loraName == "" {
		logx.WithContext(ctx).Errorf("WanSDK.params.invalid got empty lora")
		return res, "", NewVideoReason("WanSDK.params.invalid", 400, "got empty lora")
	}

	path := "/wan_video_generation"
	abtestQuene := GetAbtestQuene(ctx, params.TasksVideo.UserID)
	if abtestQuene == 0 {
		// 20250703 默认全部打到8卡
		res.RuntimeAbParams = map[string]any{"machine_group": "wan_remote"}
		if mRand.New(mRand.NewSource(time.Now().UnixNano())).Intn(5) == 1 && s.IsEnoughAccelerateLora(ctx) {
			res.RuntimeAbParams["machine_group"] = "wan_remote_accelerate_lora"
		} else if s.IsEnough8(ctx) {
			res.RuntimeAbParams["machine_group"] = "wan_remote"
		} else {
			// 8卡机器不够，可以再等等
			if time.Since(params.TasksVideo.CreateTime) < time.Second*6 {
				logx.WithContext(ctx).Infof("WanSDK 8卡机器不够, 再等等, task_id: %s, diff: %v", params.TaskID, time.Since(params.TasksVideo.CreateTime))
				return res, "", enums.ErrReqTooFrequent
			}
			if s.IsEnough2(ctx) {
				res.RuntimeAbParams["machine_group"] = "wan"
				logx.WithContext(ctx).Infof("WanSDK 8卡打满，切换到2卡机器 \nparams: %v", params)
			} else {
				// if globalAlarmManager.ShouldSendAlarm(AlarmKeyMachine2And8, time.Minute) {
				// 	bot.SendServerAlarmMsg(ctx, fmt.Sprintf("🔥WanSDK 8卡和2卡全部打满 \nparams: %v, res:%v", params, ToJsonString(res)))
				// }
				return res, "", enums.ErrReqTooFrequent
			}
		}
	} else {
		isForceFusionix := enums.IsFusionix(params.TasksVideo.GenerateType)
		path = "/v2/wan_video_generation"
		machineGroup := s.GetMachineGroup(ctx, params.TaskID, isForceFusionix)
		if machineGroup == "" {
			// if globalAlarmManager.ShouldSendAlarm(AlarmKeyMachineFull, time.Minute) {
			// 	bot.SendServerAlarmMsg(ctx, fmt.Sprintf("🔥WanSDK 队列全部打满 \nparams: %v, res:%v", params, utils.ToJsonString(res)))
			// }
			return res, "", enums.ErrReqTooFrequent
		}
		res.RuntimeAbParams["machine_group"] = machineGroup
	}

	url := fmt.Sprintf("%s%s", s.Endpoint, path)
	ossPath := fmt.Sprintf("videos/%s/%s_%s_%s.mp4", nowt.Format(time.DateOnly), nowt.Format("20060102150405"), sf.Node.Generate().String(), params.TaskID)
	uploadUrl := "s3://movelyai/" + ossPath

	payload := map[string]any{
		"task_id":       params.TaskID,
		"image":         params.FirstFrameImage,
		"output_video":  uploadUrl,
		"lora_name":     loraName,
		"lora_weight":   loraWeight,
		"user_prompt":   params.Prompt,
		"frames":        int64(frames),
		"frame_rate":    int64(frameRate),
		"machine_group": res.RuntimeAbParams["machine_group"],
		"tagger_status": taggerStatus,
	}
	if triggerWords != "" {
		payload["trigger_words"] = triggerWords
	}
	if promptOptimizationSystemPrompt != "" {
		payload["prompt_optimization_system_prompt"] = promptOptimizationSystemPrompt
	}
	if params.NegativePrompt != "" {
		payload["negative_prompt"] = params.NegativePrompt
	}

	traceID = generateTraceID()
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-Trace-Id":   traceID,
	}
	var result WanVideoGenerationResp
	rsp, err := s.httpCliGen.R().SetHeaders(headers).SetBody(payload).SetResult(&result).Post(url)
	logx.WithContext(ctx).Infof("WanSDK.InvokeVideoGeneration payload: %+v, runtime_ab: %v, task_id: %s, result:%v, err:%v", utils.ToJsonString(payload), utils.ToJsonString(res.RuntimeAbParams), params.TaskID, utils.ToJsonString(result), err)
	if err != nil {
		return res, traceID, err
	} else if rsp.StatusCode() != http.StatusOK {
		// bot.SendProductAlarmPost("WanSDK.InvokeVideoGeneration http failed", fmt.Sprintf("status_code: %v, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), params.TaskID))
		return res, traceID, NewVideoReason("InvokeVideoGeneration http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), params.TaskID)).WithTrace(traceID)
	}
	if result.Code != 0 {
		if strings.Contains(result.Msg, "no healthy backend") { // 这个基本不会触发，因为有打标，时间在 2s 内不会返回
			// bot.SendServerAlarmMsg(ctx, fmt.Sprintf("WanSDK.InvokeVideoGeneration no healthy backend, result: %v, task_id: %s", utils.ToJsonString(result), params.TaskID))
			logx.WithContext(ctx).Infof("WanSDK.InvokeVideoGeneration no healthy backend, result: %v, task_id: %s", utils.ToJsonString(result), params.TaskID)
			return res, traceID, enums.ErrReqTooFrequent
		}
		return res, traceID, NewVideoReason("InvokeVideoGeneration failed", result.Code, result.Msg).WithTrace(traceID)
	}
	res.TaskID = result.Data.TaskID
	return res, traceID, nil
}

func (s *WanSDK) GetMachineGroup(ctx context.Context, taskID string, isForceFusionix bool) string {
	rooms := []string{enums.WanRoomH100Us, enums.WanRoomH100Zj, enums.WanRoom4090Qy} // "wan_4090_autodl" 暂时不考虑2卡
	if isForceFusionix {
		rooms = []string{enums.WanRoomH100Zj}
	}

	for _, room := range rooms {
		queue := config.Configuration.Env + ".ifonly:" + room
		pendingCount, err := s.GetVideoQuenePending(queue)
		logx.WithContext(ctx).Infof("WanSDK.GetMachineGroup quene: %s, pendingCount: %d, taskID: %s", queue, pendingCount, taskID)
		if err != nil {
			// bot.SendServerAlarmMsg(ctx, fmt.Sprintf("WanSDK.GetMachineGroup get pending failed: %v", err))
			if !strings.Contains(err.Error(), "NOT_FOUND") {
				continue
			}
		}
		if pendingCount < 3 { // 这个值要比 gpu-dispatch 的值要小
			return enums.WanRoomGroupMap[room]
		}
	}
	return ""
}

func (s *WanSDK) GetVideoQuenePending(queue string) (int, error) {
	infos, e := rdb.AsynqInspector.ListPendingTasks(queue)
	if e == nil {
		return len(infos), nil
	}
	return 0, e
}

func GetAbtestQuene(ctx context.Context, userId int64) int {
	// if userId == 1816354499850141696 || userId == 1812699656111546368 {
	// 	return 1
	// }
	// return int((userId >> 22) % 2)
	return 1
}

type AbParams struct {
	IsGlobalEnable bool   `json:"is_global_enable"`
	WanRemoteRatio int    `json:"wan_remote_ratio"`
	WanLoraRation  int    `json:"wan_lora_ration"`
	WanLoraName    string `json:"wan_lora_name"`
}

func (s *WanSDK) VideoGenAbTest(abParams string, originLoraName string) (isUseRemote bool, loraName string) {
	isUseRemote = false
	loraName = originLoraName

	if abParams == "" {
		return
	}
	var ab AbParams
	err := json.Unmarshal([]byte(abParams), &ab)
	if err != nil {
		logx.Errorf("WanSDK.VideoGenTest unmarshal abParams failed: %v", err)
		return
	}
	if !ab.IsGlobalEnable {
		return
	}

	r1 := mRand.New(mRand.NewSource(time.Now().UnixNano()))
	isUseRemote = r1.Intn(100) < ab.WanRemoteRatio

	r2 := mRand.New(mRand.NewSource(time.Now().UnixNano()))
	if r2.Intn(100) < ab.WanLoraRation {
		loraName = ab.WanLoraName
	}
	return
}

// 加速lora机器上是否可用
func (s *WanSDK) IsEnoughAccelerateLora(ctx context.Context) bool {
	result := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Total     int `json:"total"`
			Running   int `json:"running"`
			Available int `json:"available"`
		} `json:"data"`
	}{}
	if _, err := s.httpCliQuery.R().SetResult(&result).Post(
		fmt.Sprintf("%s/wan_remote_accelerate_lora_video_backend_status", s.Endpoint),
	); err != nil {
		// bot.SendProductAlarmPost("WanSDK.IsEnoughAccelerateLora http failed", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get remote backend status failed: %v", err)
		return false
	} else if result.Code != 0 {
		// bot.SendProductAlarmPost("WanSDK.IsEnoughAccelerateLora got unexpected code", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get remote backend status got unexpected code: %d, msg: %s", result.Code, result.Msg)
		return false
	}
	if result.Data.Available == 0 {
		return false
	}
	// if bot.EnvTag != "dev" && result.Data.Available <= 2 {
	// 	return mRand.New(mRand.NewSource(time.Now().UnixNano())).Intn(5) == 1 // 机器不多时，随机，防止并发
	// }
	logx.WithContext(ctx).Infof("WanSDK get backend accelerate lora: %v", result.Data)
	return true
}

// 8卡机器上是否可用
func (s *WanSDK) IsEnough8(ctx context.Context) bool {
	result := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Total     int `json:"total"`
			Running   int `json:"running"`
			Available int `json:"available"`
		} `json:"data"`
	}{}
	if _, err := s.httpCliQuery.R().SetResult(&result).Post(
		fmt.Sprintf("%s/wan_remote_video_backend_status", s.Endpoint),
	); err != nil {
		// bot.SendProductAlarmPost("WanSDK.IsEnough8 http failed", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get remote backend status failed: %v", err)
		return false
	} else if result.Code != 0 {
		// bot.SendProductAlarmPost("WanSDK.IsEnough8 got unexpected code", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get remote backend status got unexpected code: %d, msg: %s", result.Code, result.Msg)
		return false
	}
	if result.Data.Available == 0 {
		return false
	}
	// if bot.EnvTag != "dev" && result.Data.Available <= 2 {
	// 	isNextRemote := mRand.New(mRand.NewSource(time.Now().UnixNano())).Intn(10) == 1 // 机器不多时，随机，防止并发
	// 	logx.WithContext(ctx).Infof("WanSDK.IsEnough8 8卡将满，%v, isNextRemote: %v", utils.ToJsonString(result.Data), isNextRemote)
	// 	return isNextRemote
	// }
	logx.WithContext(ctx).Infof("WanSDK get backend8: %v", result.Data)
	return true
}

// 2卡机器上是否可用
func (s *WanSDK) IsEnough2(ctx context.Context) bool {
	result := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Total     int `json:"total"`
			Running   int `json:"running"`
			Available int `json:"available"`
		} `json:"data"`
	}{}
	if _, err := s.httpCliQuery.R().SetResult(&result).Post(
		fmt.Sprintf("%s/wan_video_backend_status", s.Endpoint),
	); err != nil {
		// bot.SendProductAlarmPost("WanSDK.IsEnough2 http failed", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get backend status failed: %v", err)
	} else if result.Code != 0 {
		// bot.SendProductAlarmPost("WanSDK.IsEnough2 got unexpected code", utils.ToJsonString(result))
		logx.WithContext(ctx).Errorf("WanSDK get backend status got unexpected code: %d, msg: %s", result.Code, result.Msg)
	}

	if result.Data.Available == 0 {
		return false
	}
	if result.Data.Available <= 2 { // 小于等于2，随机一下
		return mRand.New(mRand.NewSource(time.Now().UnixNano())).Intn(5) == 1
	}
	logx.WithContext(ctx).Infof("WanSDK get backend2 %v", result.Data)
	return true
}

func (s *WanSDK) QueryVideoGeneration(ctx context.Context, taskID string) (res *QueryVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("WanSDK.QueryVideoGeneration costTime: %v, thirdTaskID: %s", time.Since(nowt), taskID)
		if err != nil {
			logx.WithContext(ctx).Errorf("WanSDK.QueryVideoGeneration err: %v, taskID: %s", err, taskID)
		}
	}()

	payload := map[string]any{
		"task_id": taskID,
	}
	traceID = generateTraceID()
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-Trace-Id":   traceID,
	}

	// task := model.TasksVideo{}
	// rdb.Rdb.Model(&model.TasksVideo{}).Where("id = ?", taskID).First(&task)
	// path := "/wan_video_query"
	// abtestQuene := GetAbtestQuene(ctx, task.UserID)
	// if abtestQuene == 1 {
	path := "/v2/wan_video_query"
	// }

	var result WanVideoQueryResp
	rsp, err := s.httpCliQuery.R().SetHeaders(headers).SetBody(payload).SetResult(&result).
		Post(fmt.Sprintf("%s%s", s.Endpoint, path))
	if err != nil {
		return nil, traceID, err
	} else if rsp.StatusCode() != http.StatusOK {
		return nil, traceID, NewVideoReason("QueryVideoGeneration http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), taskID)).WithTrace(traceID)
	}
	if result.Code != 0 {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", result.Code,
			fmt.Sprintf("%s, task_id: %s", result.Msg, taskID)).WithTrace(traceID)
	}
	if result.Data.Video != "" {
		result.Data.Video = utils.ConvertS3ToCDN(result.Data.Video)
	}

	res = &QueryVideoGenerationResult{
		TaskID:    result.Data.TaskID,
		Status:    WanStatusMapping[result.Data.Status],
		StatusMsg: result.Data.ErrorMsg,
		VideoInfo: VideoInfo{
			FileID:   string(result.Data.TaskID),
			VideoUrl: result.Data.Video,
		},
	}
	return res, traceID, nil
}

func (s *WanSDK) FetchAndResaveVideoResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	res, traceID, err = s.FetchVideoGenerationResult(ctx, fileID)
	if err != nil {
		return nil, traceID, err
	}
	res.DownloadURL, res.ResaveCost, err = s.ResaveResultVideo(ctx, res.DownloadURL)
	return res, traceID, err
}

func (s *WanSDK) FetchVideoGenerationResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	data, trace, err := s.QueryVideoGeneration(ctx, fileID)
	if err != nil {
		return nil, trace, err
	}
	res = &FetchVideoGenerationResult{
		DownloadURL: data.VideoUrl,
	}
	return res, trace, nil
}

func (s *WanSDK) ResaveResultVideo(ctx context.Context, downloadURL string) (videoURL string, costTime time.Duration, err error) {
	nowt := time.Now().UTC()
	return downloadURL, time.Since(nowt), nil
}

func generateTraceID() string {
	var buf [8]byte
	_, _ = io.ReadFull(rand.Reader, buf[:])
	return fmt.Sprintf("1f%s%x", time.Now().Format("20060102150405"), buf[:]) // 统一前缀 "if" + 14 位日期 + 16 位随机数
}
