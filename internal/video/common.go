package video

import "encoding/json"

const (
	VideoGenerationStatusQueueing   = "Queueing"
	VideoGenerationStatusProcessing = "Processing"
	VideoGenerationStatusSuccess    = "Success"
	VideoGenerationStatusFail       = "Fail"
)

type VideoReason struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Trace string `json:"trace,omitempty"`
	Type  string `json:"type"`
}

type (
	VideoInfo struct {
		FileID       string `json:"file_id"`
		VideoUrl     string `json:"video_url"`
		VideoWidth   int    `json:"video_width"`
		VideoHeight  int    `json:"video_height"`
		ThirdAssetID string `json:"third_asset_id"`
	}

	QueryVideoGenerationResult struct {
		TaskID    string `json:"task_id"`
		Status    string `json:"status"`
		StatusMsg string `json:"status_msg"`
		VideoInfo `json:",inline"`
	}

	queryVideoGenerationResult struct {
		QueryVideoGenerationResult `json:",inline"`

		BaseResp BaseResp `json:"base_resp"`
	}
)

func NewVideoReason(typ string, code int, msg string) *VideoReason {
	return &VideoReason{Type: typ, Code: code, Msg: msg}
}

func (e *VideoReason) JsonStr() string {
	if e == nil {
		return "{}"
	}
	bs, _ := json.Marshal(e)
	return string(bs)
}

func (e *VideoReason) GetTrace() string {
	if e == nil {
		return ""
	}
	return e.Trace
}

func (e *VideoReason) WithTrace(trace string) *VideoReason {
	if e == nil {
		return &VideoReason{Trace: trace}
	}
	e.Trace = trace
	return e
}

func (e *VideoReason) WithoutTrace() *VideoReason {
	if e == nil {
		return &VideoReason{}
	}
	cp := *e
	cp.Trace = ""
	return &cp
}

func (e *VideoReason) Error() string {
	return e.JsonStr()
}
