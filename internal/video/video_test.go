package video

import (
	"context"
	"os"
	"testing"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
)

func TestMain(m *testing.M) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	err := logger.Init()
	if err != nil {
		return
	}
	err = s3.Init()
	if err != nil {
		return
	}
	rdb.Init()
	sf.Init(true)
	os.Exit(m.Run())
}

func TestVideo(t *testing.T) {
	// NewVideoTaskCreateScheduler().ScheduleCreateAll()
	// video.NewVideoTaskQueryScheduler().ScheduleQueryAll()

	// NewVideoTaskCreateScheduler().ScheduleCreateTaskByPlatType("minimax") // king, wan, pixverse, minimax
	NewVideoTaskQueryScheduler().ScheduleQueryByPlatType("minimax")

	time.Sleep(10 * time.Minute)
}

func TestQueryVideoGeneration(t *testing.T) {
	res, traceID, err := NewWanSDK().QueryVideoGeneration(context.Background(), "1947192066413649920")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res: %v, traceID: %s", utils.ToJsonString(res), traceID)
}
