package video

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"path/filepath"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/go-resty/resty/v2"
	"github.com/golang-jwt/jwt"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	KlingStatusQueueing   = "submitted"
	KlingStatusProcessing = "processing"
	KlingStatusSuccess    = "succeed"
	KlingStatusFail       = "failed"
)

var KlingStatusMapping = map[string]string{
	KlingStatusQueueing:   VideoGenerationStatusQueueing,
	KlingStatusProcessing: VideoGenerationStatusProcessing,
	KlingStatusSuccess:    VideoGenerationStatusSuccess,
	KlingStatusFail:       VideoGenerationStatusFail,
}

type (
	FetchVideoGenerationResult struct {
		ResaveCost  time.Duration `json:"resave_cost"`
		DownloadURL string        `json:"download_url"`
	}

	fetchVideoGenerationResult struct {
		File FetchVideoGenerationResult `json:"file"`

		BaseResp BaseResp `json:"base_resp"`
	}

	fetchKlingResult struct {
		KlingBaseResp `json:",inline"`

		Data fetchKlingResultData `json:"data"`
	}

	fetchKlingResultData struct {
		TaskID        string `json:"task_id"`
		TaskStatus    string `json:"task_status"`
		TaskStatusMsg string `json:"task_status_msg"`
		TaskResult    struct {
			Videos []fetchKlingResultDataVideo `json:"videos"`
		} `json:"task_result"`
	}

	fetchKlingResultDataVideo struct {
		Id       string `json:"id"`
		Url      string `json:"url"`
		Duration string `json:"duration"`
	}
)

type KlingSDK struct {
	httpCli *resty.Client
}

// 文档：https://docs.qingque.cn/d/home/<USER>
func NewKlingSDK() *KlingSDK {
	return &KlingSDK{
		httpCli: resty.New().SetTimeout(time.Second * 30),
	}
}

// https://docs.qingque.cn/d/home/<USER>
func (s *KlingSDK) InvokeVideoGeneration(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	concurrency := 70 // TOOD: 读取配置
	apiType := "kling_api"

	defer func() {
		var taskID = string(res.TaskID)
		logx.WithContext(ctx).Infof("KlingSDK.InvokeVideoGeneration costTime: %v, taskID: %s, thirdTaskID: %s, prompt: %s",
			time.Since(nowt), params.TaskID, taskID, params.Prompt)
		if err != nil {
			logx.WithContext(ctx).Errorf("KlingSDK.InvokeVideoGeneration err: %v, apiType: %s, taskID: %s", err, apiType, params.TaskID)
		}
		res.ApiType = apiType
	}()

	var count int64
	if err := rdb.Rdb.Where(&model.TasksVideo{Status: int32(enums.TaskStatus_created), APIType: apiType}).
		Model(&model.TasksVideo{}).Count(&count).Error; err != nil {
		logx.WithContext(ctx).Errorf("KlingSDK get concurrency count failed: %v", err)
		return res, "", err
	}
	if count >= int64(concurrency) {
		return res, "", enums.ErrReqTooFrequent
	}

	// 只支持这两个数
	duration := "5"
	if params.ExpectedDuration == "10" {
		duration = "10"
	}
	modelName := "kling-v1-6"
	inferParams := make(map[string]any)
	_ = json.Unmarshal([]byte(params.InferParams), &inferParams)
	_modelName, _ := inferParams["model_name"].(string)
	if _modelName != "" {
		modelName = _modelName
	}
	url := fmt.Sprintf("%s%s", "https://api.klingai.com", "/v1/videos/image2video")
	payload := map[string]any{
		"model_name": modelName,
		"mode":       "std",
		"duration":   duration,
		"image":      params.FirstFrameImage,
		"prompt":     params.Prompt,
		"cfg_scale":  0.5,
	}
	if params.FromThirdAssetID != "" {
		payload["video_id"] = params.FromThirdAssetID
	}
	if params.NegativePrompt != "" {
		payload["negative_prompt"] = params.NegativePrompt
	}
	if params.CfgScale != 0 {
		payload["cfg_scale"] = params.CfgScale
	}
	if params.AspectRatio != "" {
		payload["aspect_ratio"] = params.AspectRatio
	}
	if len(params.EffectImageList) > 0 {
		delete(payload, "image")
		payload["image_list"] = append([]string{params.FirstFrameImage}, params.EffectImageList...)
	}
	token, err := s.encodeJWTToken("e9163038fb4846ae824486a2514afba2", "ae0f476a0be446e99fc4cb0ac8fba0b5")
	if err != nil {
		return res, "", err
	}
	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
		"Content-Type":  "application/json",
	}
	var result invokeKlingResult
	logx.WithContext(ctx).Infof("KlingSDK invoke create begin, url: %s, payload: %+v, task_id: %s", url, payload, params.TaskID)
	rsp, err := s.httpCli.R().SetHeaders(headers).SetBody(payload).Post(url)
	if err != nil {
		return result.Data, "", err
	} else if err := json.Unmarshal(rsp.Body(), &result); err != nil {
		return result.Data, "", err
	}
	traceID = result.RequestID
	if result.Code != 0 {
		if result.Code == 1303 { // 触发限流
			logx.WithContext(ctx).Infof("KlingSDK.InvokeVideoGeneration: %s", result.Message)
			return result.Data, traceID, enums.ErrReqTooFrequent
		}
		return result.Data, traceID, NewVideoReason("KlingSDK.InvokeVideoGeneration failed",
			result.Code, result.Message).WithTrace(traceID)
	} else if result.Data.TaskID == "" {
		return result.Data, traceID, NewVideoReason("KlingSDK.InvokeVideoGeneration got empty taskID",
			result.Code, result.Message).WithTrace(traceID)
	}
	return result.Data, traceID, nil
}

func (s *KlingSDK) QueryVideoGeneration(ctx context.Context, taskID string) (res *QueryVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("KlingSDK.QueryVideoGeneration costTime: %v, thirdTaskID: %s", time.Since(nowt), taskID)
		if err != nil {
			logx.WithContext(ctx).Errorf("KlingSDK.QueryVideoGeneration err: %v, taskID: %s", err, taskID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://api.klingai.com", "/v1/videos/image2video/"+taskID)
	var result fetchKlingResult
	token, err := s.encodeJWTToken("e9163038fb4846ae824486a2514afba2", "ae0f476a0be446e99fc4cb0ac8fba0b5")
	if err != nil {
		return res, "", err
	}
	if _, err := s.httpCli.R().SetHeader("Authorization", fmt.Sprintf("Bearer %s", token)).
		SetResult(&result).Get(url); err != nil {
		return nil, "", err
	}
	traceID = result.RequestID
	if result.Code != 0 {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", result.Code,
			fmt.Sprintf("%s, task_id: %s", result.Message, taskID)).WithTrace(traceID)
	} else if result.Data.TaskStatusMsg == "Failure to pass the risk control system" {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", 1301,
			fmt.Sprintf("%s, task_id: %s", result.Data.TaskStatusMsg, taskID)).WithTrace(traceID)
	} else if result.Data.TaskStatusMsg == "The resolution or aspect ratio of the input image is invalid. Please ensure it meets the required specifications." {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", 1201,
			fmt.Sprintf("%s, task_id: %s", result.Data.TaskStatusMsg, taskID)).WithTrace(traceID)
	}
	var thirdAssetId string
	if len(result.Data.TaskResult.Videos) > 0 {
		thirdAssetId = result.Data.TaskResult.Videos[0].Id
	}
	res = &QueryVideoGenerationResult{
		TaskID:    result.Data.TaskID,
		Status:    KlingStatusMapping[result.Data.TaskStatus],
		StatusMsg: result.Data.TaskStatusMsg,
		VideoInfo: VideoInfo{FileID: string(result.Data.TaskID), ThirdAssetID: thirdAssetId},
	}
	return res, traceID, nil
}

func (s *KlingSDK) FetchAndResaveVideoResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	res, traceID, err = s.FetchVideoGenerationResult(ctx, fileID)
	if err != nil {
		return nil, traceID, err
	}
	res.DownloadURL, res.ResaveCost, err = s.ResaveResultVideo(ctx, res.DownloadURL)
	return res, traceID, err
}

func (s *KlingSDK) FetchVideoGenerationResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("KlingSDK.FetchVideoResult costTime: %v, fileID: %s", time.Since(nowt), fileID)
		if err != nil {
			logx.WithContext(ctx).Errorf("KlingSDK.FetchVideoResult err: %v, fileID: %s", err, fileID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://api.klingai.com", "/v1/videos/image2video/"+fileID)
	var result fetchKlingResult
	token, err := s.encodeJWTToken("e9163038fb4846ae824486a2514afba2", "ae0f476a0be446e99fc4cb0ac8fba0b5")
	if err != nil {
		return res, "", err
	}
	if _, err := s.httpCli.R().SetHeader("Authorization", fmt.Sprintf("Bearer %s", token)).
		SetResult(&result).Get(url); err != nil {
		return nil, "", err
	}
	traceID = result.RequestID
	if result.Code != 0 {
		return nil, traceID, NewVideoReason("FetchVideoResult failed", result.Code,
			fmt.Sprintf("%s, file_id: %s", result.Message, fileID)).WithTrace(traceID)
	}
	videos := result.Data.TaskResult.Videos
	if len(videos) == 0 {
		return nil, traceID, NewVideoReason("FetchVideoResult got empty videos", result.Code,
			fmt.Sprintf("%s, file_id: %s", result.Message, fileID)).WithTrace(traceID)
	}
	if videos[0].Url == "" {
		return nil, traceID, NewVideoReason("FetchVideoResult got empty url", result.Code,
			fmt.Sprintf("%s, file_id: %s", result.Message, fileID)).WithTrace(traceID)
	}
	res = &FetchVideoGenerationResult{
		DownloadURL: videos[0].Url,
	}
	return res, traceID, nil
}

func (s *KlingSDK) ResaveResultVideo(ctx context.Context, downloadURL string) (videoURL string, costTime time.Duration, err error) {
	nowt := time.Now().UTC()
	rsp, err := s.httpCli.R().Get(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	urlParsed, err := url.Parse(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	ossPath := "videos/" + time.Now().Format(time.DateOnly) + "/" + sf.Node.Generate().String() + "_" + filepath.Base(urlParsed.Path)
	_, err = s3.Cli.Upload("movelyai", ossPath, bytes.NewReader(rsp.Body()), "video/mp4")
	if err != nil {
		return "", time.Since(nowt), err
	}
	return "https://static.aimovely.com/" + ossPath, time.Since(nowt), nil
}

func (s *KlingSDK) encodeJWTToken(ak, sk string) (string, error) {
	// 定义 JWT 的 Header 和 Payload
	claims := jwt.MapClaims{
		"iss": ak,                                      // 发行人
		"exp": time.Now().Add(30 * time.Minute).Unix(), // 过期时间 (当前时间 + 30 分钟)
		"nbf": time.Now().Add(-5 * time.Second).Unix(), // 不早于时间 (当前时间 - 5 秒)
	}

	// 创建一个新的 JWT Token，使用 HS256 签名方法
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 使用 Secret Key (sk) 进行签名
	tokenString, err := token.SignedString([]byte(sk))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
