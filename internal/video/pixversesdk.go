package video

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"path/filepath"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

type (
	PixverseBaseResp struct {
		ErrCode int    `json:"ErrCode"`
		ErrMsg  string `json:"ErrMsg"`
	}

	PixverseUploadResult struct {
		PixverseBaseResp `json:",inline"`

		Resp struct {
			ImgID  int    `json:"img_id"`
			ImgURL string `json:"img_url"`
		} `json:"Resp"`
	}

	PixverseCreateResult struct {
		PixverseBaseResp `json:",inline"`

		Resp struct {
			VideoID int `json:"video_id"`
		} `json:"Resp"`
	}

	PixverseFetchResult struct {
		PixverseBaseResp `json:",inline"`

		Resp struct {
			CreateTime      string `json:"create_time"`
			ID              int    `json:"id"`
			ModifyTime      string `json:"modify_time"`
			NegativePrompt  string `json:"negative_prompt"`
			OutputHeight    int    `json:"outputHeight"`
			OutputWidth     int    `json:"outputWidth"`
			Prompt          string `json:"prompt"`
			ResolutionRatio int    `json:"resolution_ratio"`
			Seed            int    `json:"seed"`
			Size            int    `json:"size"`
			Status          int    `json:"status"`
			Style           string `json:"style"`
			URL             string `json:"url"`
		} `json:"Resp"`
	}
)

// https://docs.platform.pixverse.ai/get-video-generation-status-13016632e0
// 1: Generation successful; 5: Generating; 7: Contents moderation failed; 8: Generation failed;
const (
	PixverseStatusSuccessful       = 1
	PixverseStatusGenerating       = 5
	PixverseStatusModerationFailed = 7
	PixverseStatusGenerationFailed = 8
)

var PixverseStatusMapping = map[int]string{
	PixverseStatusModerationFailed: VideoGenerationStatusFail,
	PixverseStatusGenerating:       VideoGenerationStatusProcessing,
	PixverseStatusSuccessful:       VideoGenerationStatusSuccess,
	PixverseStatusGenerationFailed: VideoGenerationStatusFail,
}

type PixverseSDK struct {
	httpCli *resty.Client
}

func NewPixverseSDK() *PixverseSDK {
	return &PixverseSDK{
		httpCli: resty.New().SetTimeout(3 * time.Minute),
	}
}

func (s *PixverseSDK) UploadImage(ctx context.Context, imageURL string) (int, error) {
	resp, err := s.httpCli.R().SetDoNotParseResponse(true).Get(imageURL)
	if err != nil {
		logx.WithContext(ctx).Errorf("UploadImage: Download %s failed: %v", imageURL, err)
		return 0, err
	}
	defer resp.RawBody().Close()

	var payload bytes.Buffer
	writer := multipart.NewWriter(&payload)
	part, err := writer.CreateFormFile("image", filepath.Base(imageURL))
	if err != nil {
		logx.WithContext(ctx).Errorf("UploadImage: CreateFormFile failed: %v", err)
		return 0, err
	}
	if _, err = io.Copy(part, resp.RawBody()); err != nil {
		logx.WithContext(ctx).Errorf("UploadImage: io.Copy failed: %v", err)
		return 0, err
	}
	if err = writer.Close(); err != nil {
		logx.WithContext(ctx).Errorf("UploadImage: writer.Close failed: %v", err)
		return 0, err
	}

	traceID := sf.Node.Generate().String()
	headers := map[string]string{
		"API-KEY":      "sk-1cea55067666f8a0eb1850aa96fbbacf", // TODO: 从配置中心获取
		"Ai-trace-id":  traceID,
		"Content-Type": writer.FormDataContentType(),
	}
	url := "https://app-api.pixverse.ai/openapi/v2/image/upload"
	var result PixverseUploadResult
	rsp, err := s.httpCli.R().SetHeaders(headers).
		SetBody(payload.Bytes()).
		SetResult(&result).Post(url)
	if err != nil {
		logx.WithContext(ctx).Errorf("UploadImage: Upload failed: %v", err)
		return 0, err
	} else if rsp.StatusCode() != http.StatusOK {
		_ = json.Unmarshal(rsp.Body(), &result)
		if result.ErrCode == 500054 || result.ErrCode == 500033 {
			return 0, NewVideoReason("UploadImage: failed",
				result.ErrCode, result.ErrMsg).WithTrace(traceID)
		}
		return 0, NewVideoReason("UploadImage: http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, image: %s", rsp.Status(), string(rsp.Body()), imageURL)).WithTrace(traceID)
	}
	if result.ErrCode != 0 {
		return 0, NewVideoReason("UploadImage: failed",
			result.ErrCode, result.ErrMsg).WithTrace(traceID)
	} else if result.Resp.ImgID == 0 {
		return 0, NewVideoReason("UploadImage: got empty ImgID",
			result.ErrCode, result.ErrMsg).WithTrace(traceID)
	}
	return result.Resp.ImgID, nil
}

var PixverseTemplateMap = map[string]int64{
	"video_gen_ripped_stripped":       308621408717184,
	"video_temp_20250624_5uQU8TMWtCL": 339848996187712, // Eye Zoom Challenge 瞳孔无限放大
	"video_temp_20250624_5uQVGAGbWXA": 340541567573824, // Fin-tastic Mermaid 夏日美人鱼
}

func (s *PixverseSDK) InvokeVideoGeneration(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	apiType := "pixverse_api"

	nowt := time.Now().UTC()
	defer func() {
		var taskID = string(res.TaskID)
		logx.WithContext(ctx).Infof("PixverseSDK.InvokeVideoGeneration costTime: %v, taskID: %s, thirdTaskID: %s, prompt: %s",
			time.Since(nowt), params.TaskID, taskID, params.Prompt)
		if err != nil {
			logx.WithContext(ctx).Errorf("PixverseSDK.InvokeVideoGeneration err: %v, apiType: %s, taskID: %s", err, apiType, params.TaskID)
		}
		res.ApiType = apiType
	}()

	var concurrency int64
	if err := rdb.Rdb.Where(&model.TasksVideo{Status: int32(enums.TaskStatus_created), APIType: apiType}).
		Model(&model.TasksVideo{}).Count(&concurrency).Error; err != nil {
		logx.WithContext(ctx).Errorf("PixverseSDK get concurrency count failed: %v", err)
		return res, "", err
	}
	if concurrency >= 40 { // TODO: 从配置中心获取
		return res, "", enums.ErrReqTooFrequent
	}

	imgID, err := s.UploadImage(ctx, params.FirstFrameImage)
	if err != nil {
		logx.WithContext(ctx).Errorf("PixverseSDK.UploadImage failed: %v", err)
		return res, "", err
	}

	model := "v3.5"
	templateID := int64(0)
	inferParams := make(map[string]any)
	_ = json.Unmarshal([]byte(params.InferParams), &inferParams)
	_modelName, _ := inferParams["model_name"].(string)
	if _modelName != "" {
		model = _modelName
	}
	_templateID := cast.ToInt64(inferParams["template_id"])
	if _templateID != 0 {
		templateID = _templateID
	}

	url := fmt.Sprintf("%s%s", "https://app-api.pixverse.ai", "/openapi/v2/video/img/generate")
	payload := map[string]any{
		"duration": 5,
		"img_id":   imgID,
		"model":    model,
		"prompt":   params.Prompt,
		"quality":  "540p",
	}
	if params.NegativePrompt != "" {
		payload["negative_prompt"] = params.NegativePrompt
	}
	if templateID, ok := PixverseTemplateMap[params.PromptKey]; ok {
		payload["template_id"] = templateID
	}
	if templateID > 0 {
		payload["template_id"] = templateID
	}

	traceID = sf.Node.Generate().String()
	headers := map[string]string{
		"API-KEY":      "sk-1cea55067666f8a0eb1850aa96fbbacf", // TODO: 从配置中心获取
		"Ai-trace-id":  traceID,
		"Content-Type": "application/json",
	}
	var result PixverseCreateResult
	rsp, err := s.httpCli.R().SetHeaders(headers).SetBody(payload).SetResult(&result).Post(url)
	logx.WithContext(ctx).Infof("PixverseSDK.InvokeVideoGeneration req start, url: %s, payload: %v, headers: %v, rsp: %v, err: %v, inferParams: %v, _templateID: %v", url, utils.ToJsonString(payload), utils.ToJsonString(headers), utils.ToJsonString(rsp), err, utils.ToJsonString(inferParams), _templateID)
	if err != nil {
		return res, "", err
	} else if rsp.StatusCode() != http.StatusOK {
		return res, traceID, NewVideoReason("InvokeVideoGeneration http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), params.TaskID)).WithTrace(traceID)
	}
	if result.ErrCode != 0 {
		return res, traceID, NewVideoReason("InvokeVideoGeneration failed",
			result.ErrCode, result.ErrMsg).WithTrace(traceID)
	} else if result.Resp.VideoID == 0 {
		return res, traceID, NewVideoReason("InvokeVideoGeneration got empty VideoID",
			result.ErrCode, result.ErrMsg).WithTrace(traceID)
	}
	res.TaskID = cast.ToString(result.Resp.VideoID)
	return res, traceID, nil
}

func (s *PixverseSDK) QueryVideoGeneration(ctx context.Context, taskID string) (res *QueryVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("PixverseSDK.QueryVideoGeneration costTime: %v, thirdTaskID: %s", time.Since(nowt), taskID)
		if err != nil {
			logx.WithContext(ctx).Errorf("PixverseSDK.QueryVideoGeneration err: %v, taskID: %s", err, taskID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://app-api.pixverse.ai", "/openapi/v2/video/result/"+taskID)
	traceID = sf.Node.Generate().String()
	headers := map[string]string{
		"API-KEY":     "sk-1cea55067666f8a0eb1850aa96fbbacf", // TODO: 从配置中心获取
		"Ai-trace-id": traceID,
	}
	var result PixverseFetchResult
	rsp, err := s.httpCli.R().SetHeaders(headers).SetResult(&result).Get(url)
	if err != nil {
		return nil, traceID, NewVideoReason("QueryVideoGeneration http error", 0,
			fmt.Sprintf("%s, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), taskID)).WithTrace(traceID)
	} else if rsp.StatusCode() != http.StatusOK {
		return nil, traceID, NewVideoReason("QueryVideoGeneration http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, task_id: %s", rsp.Status(), string(rsp.Body()), taskID)).WithTrace(traceID)
	}
	if result.ErrCode != 0 {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", result.ErrCode,
			fmt.Sprintf("%s, task_id: %s", result.ErrMsg, taskID)).WithTrace(traceID)
	} else if result.Resp.Status == PixverseStatusModerationFailed {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", result.Resp.Status,
			fmt.Sprintf("%s, task_id: %s", "Contents moderation failed", taskID)).WithTrace(traceID)
	}
	res = &QueryVideoGenerationResult{
		TaskID: cast.ToString(result.Resp.ID),
		Status: PixverseStatusMapping[result.Resp.Status],
		VideoInfo: VideoInfo{
			FileID:      cast.ToString(result.Resp.ID),
			VideoWidth:  result.Resp.OutputWidth,
			VideoHeight: result.Resp.OutputHeight,
		},
	}
	return res, traceID, nil
}

func (s *PixverseSDK) FetchAndResaveVideoResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	res, traceID, err = s.FetchVideoGenerationResult(ctx, fileID)
	if err != nil {
		return nil, traceID, err
	}
	res.DownloadURL, res.ResaveCost, err = s.ResaveResultVideo(ctx, res.DownloadURL)
	return res, traceID, err
}

func (s *PixverseSDK) FetchVideoGenerationResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("PixverseSDK.FetchVideoResult costTime: %v, fileID: %s", time.Since(nowt), fileID)
		if err != nil {
			logx.WithContext(ctx).Errorf("PixverseSDK.FetchVideoResult err: %v, fileID: %s", err, fileID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://app-api.pixverse.ai", "/openapi/v2/video/result/"+fileID)
	traceID = sf.Node.Generate().String()
	headers := map[string]string{
		"API-KEY":     "sk-1cea55067666f8a0eb1850aa96fbbacf", // TODO: 从配置中心获取
		"Ai-trace-id": traceID,
	}
	var result PixverseFetchResult
	rsp, err := s.httpCli.R().SetHeaders(headers).SetResult(&result).Get(url)
	if err != nil {
		return nil, "", err
	} else if rsp.StatusCode() != http.StatusOK {
		return nil, traceID, NewVideoReason("FetchVideoResult http failed", rsp.StatusCode(),
			fmt.Sprintf("%s, rsp_body: %s, file_id: %s", rsp.Status(), string(rsp.Body()), fileID)).WithTrace(traceID)
	}
	if result.ErrCode != 0 {
		return nil, traceID, NewVideoReason("FetchVideoResult failed", result.ErrCode,
			fmt.Sprintf("%s, file_id: %s", result.ErrMsg, fileID)).WithTrace(traceID)
	}
	if result.Resp.URL == "" {
		return nil, traceID, NewVideoReason("FetchVideoResult got empty url", result.ErrCode,
			fmt.Sprintf("%s, file_id: %s", result.ErrMsg, fileID)).WithTrace(traceID)
	}
	res = &FetchVideoGenerationResult{
		DownloadURL: result.Resp.URL,
	}
	return res, traceID, nil
}

func (s *PixverseSDK) ResaveResultVideo(ctx context.Context, downloadURL string) (videoURL string, costTime time.Duration, err error) {
	nowt := time.Now().UTC()
	rsp, err := s.httpCli.R().Get(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	urlParsed, err := url.Parse(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	ossPath := "videos/" + time.Now().Format(time.DateOnly) + "/" + sf.Node.Generate().String() + "_" + filepath.Base(urlParsed.Path)
	_, err = s3.Cli.Upload("movelyai", ossPath, bytes.NewReader(rsp.Body()), "video/mp4")
	if err != nil {
		return "", time.Since(nowt), err
	}
	return "https://static.aimovely.com/" + ossPath, time.Since(nowt), nil
}
