package video

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"path/filepath"
	"time"

	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"git.hoxigames.xyz/movely/movely-server/pkg/sf"
	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type MinimaxSDK struct {
	httpCli *resty.Client
	ApiKey  string
}

func NewMinimaxSDK() *MinimaxSDK {
	return &MinimaxSDK{
		httpCli: resty.New().SetTimeout(3 * time.Minute),
		ApiKey:  "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", // TODO: 从配置中心获取
	}
}

func (s *MinimaxSDK) InvokeVideoGeneration(ctx context.Context, params InvokeVideoGenerationParams) (res InvokeVideoGenerationResult, traceID string, err error) {
	apiType := "minimax_api"

	nowt := time.Now().UTC()
	defer func() {
		var taskID = string(res.TaskID)
		logx.WithContext(ctx).Infof("VideoSDK.InvokeVideoGeneration costTime: %v, taskID: %s, thirdTaskID: %s, prompt: %s",
			time.Since(nowt), params.TaskID, taskID, params.Prompt)
		if err != nil {
			logx.WithContext(ctx).Errorf("VideoSDK.InvokeVideoGeneration err: %v, apiType: %s, taskID: %s", err, apiType, params.TaskID)
		}
		res.ApiType = apiType
	}()

	// if s.c.Concurrency > 0 {
	// 	var concurrency int64
	// 	if err := s.dbCli.Where(&model.TasksVideo{Status: int32(enums.TaskStatus_created), APIType: s.c.ApiType}).
	// 		Model(&model.TasksVideo{}).Count(&concurrency).Error; err != nil {
	// 		logx.WithContext(ctx).Errorf("VideoSDK get concurrency count failed: %v", err)
	// 		return res, "", err
	// 	}
	// 	if concurrency >= s.c.Concurrency {
	// 		return res, "", enums.ReqTooFrequent
	// 	}
	// }

	url := fmt.Sprintf("%s%s", "https://api.minimax.chat", "/v1/video_generation")
	payload := map[string]any{
		"model":             "video-01-live2d",
		"prompt":            params.Prompt,
		"prompt_optimizer":  false,
		"first_frame_image": params.FirstFrameImage,
	}
	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", s.ApiKey),
		"Content-Type":  "application/json",
	}
	var result invokeVideoGenerationResult
	rsp, err := s.httpCli.R().SetHeaders(headers).SetBody(payload).SetResult(&result).Post(url)
	if err != nil {
		return result.InvokeVideoGenerationResult, "", err
	}
	traceID = rsp.Header().Get("Trace-ID")
	if result.BaseResp.StatusCode != 0 {
		if result.BaseResp.StatusCode == 1002 { // 触发限流
			logx.WithContext(ctx).Infof("VideoSDK.InvokeVideoGeneration: %s", result.BaseResp.StatusMsg)
			return result.InvokeVideoGenerationResult, traceID, enums.ErrReqTooFrequent
		}
		return result.InvokeVideoGenerationResult, traceID, NewVideoReason("InvokeVideoGeneration failed",
			result.BaseResp.StatusCode, result.BaseResp.StatusMsg).WithTrace(traceID)
	}
	return result.InvokeVideoGenerationResult, traceID, nil
}

func (s *MinimaxSDK) QueryVideoGeneration(ctx context.Context, taskID string) (res *QueryVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("VideoSDK.QueryVideoGeneration finished, costTime: %v, thirdTaskID: %s", time.Since(nowt), taskID)
		if err != nil {
			logx.WithContext(ctx).Errorf("VideoSDK.QueryVideoGeneration err: %v, taskID: %s", err, taskID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://api.minimax.chat", "/v1/query/video_generation?task_id="+taskID)
	var result queryVideoGenerationResult
	rsp, err := s.httpCli.R().SetHeader("Authorization", fmt.Sprintf("Bearer %s", s.ApiKey)).
		SetResult(&result).Get(url)
	if err != nil {
		return nil, "", err
	}
	traceID = rsp.Header().Get("Trace-ID")
	if result.BaseResp.StatusCode != 0 {
		return nil, traceID, NewVideoReason("QueryVideoGeneration failed", result.BaseResp.StatusCode,
			fmt.Sprintf("%s, task_id: %s", result.BaseResp.StatusMsg, taskID)).WithTrace(traceID)
	}
	return &result.QueryVideoGenerationResult, traceID, nil
}

func (s *MinimaxSDK) FetchAndResaveVideoResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	res, traceID, err = s.FetchVideoGenerationResult(ctx, fileID)
	if err != nil {
		return nil, traceID, err
	}
	res.DownloadURL, res.ResaveCost, err = s.ResaveResultVideo(ctx, res.DownloadURL)
	return res, traceID, err
}

func (s *MinimaxSDK) FetchVideoGenerationResult(ctx context.Context, fileID string) (res *FetchVideoGenerationResult, traceID string, err error) {
	nowt := time.Now().UTC()
	defer func() {
		logx.WithContext(ctx).Infof("VideoSDK.FetchVideoResult costTime: %v, fileID: %s", time.Since(nowt), fileID)
		if err != nil {
			logx.WithContext(ctx).Errorf("VideoSDK.FetchVideoResult err: %v, fileID: %s", err, fileID)
		}
	}()

	url := fmt.Sprintf("%s%s", "https://api.minimax.chat", "/v1/files/retrieve?file_id="+fileID)
	var result fetchVideoGenerationResult
	rsp, err := s.httpCli.R().SetHeader("Authorization", fmt.Sprintf("Bearer %s", s.ApiKey)).
		SetResult(&result).Get(url)
	if err != nil {
		return nil, "", err
	}
	traceID = rsp.Header().Get("Trace-ID")
	if result.BaseResp.StatusCode != 0 {
		return nil, traceID, NewVideoReason("FetchVideoResult failed", result.BaseResp.StatusCode,
			fmt.Sprintf("%s, file_id: %s", result.BaseResp.StatusMsg, fileID)).WithTrace(traceID)
	} else if result.File.DownloadURL == "" {
		return nil, traceID, NewVideoReason("FetchVideoResult got empty url", result.BaseResp.StatusCode,
			fmt.Sprintf("%s, file_id: %s", result.BaseResp.StatusMsg, fileID)).WithTrace(traceID)
	}
	return &result.File, traceID, nil
}

func (s *MinimaxSDK) ResaveResultVideo(ctx context.Context, downloadURL string) (videoURL string, costTime time.Duration, err error) {
	nowt := time.Now().UTC()
	rsp, err := s.httpCli.R().Get(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	urlParsed, err := url.Parse(downloadURL)
	if err != nil {
		return "", time.Since(nowt), err
	}
	ossPath := "videos/" + time.Now().Format(time.DateOnly) + "/" + sf.Node.Generate().String() + "_" + filepath.Base(urlParsed.Path)
	_, err = s3.Cli.Upload("movelyai", ossPath, bytes.NewReader(rsp.Body()), "video/mp4")
	if err != nil {
		return "", time.Since(nowt), err
	}
	return "https://static.aimovely.com/" + ossPath, time.Since(nowt), nil
}
