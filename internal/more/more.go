package more

import (
	movelyServer "git.hoxigames.xyz/movely/movely-server"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
)

type Build struct{}

type (
	infoReq struct {
		Pass string `form:"pass" binding:"required"`
	}
	infoResp struct {
		BuildTime string `json:"build_time"`
		Git       string `json:"git"`
	}
)

func (build *Build) Info(ctx *app.Context) {
	req := &infoReq{}
	err := ctx.ShouldBind(req)
	if err != nil {
		output.Error(ctx, 20001, err)
		return
	}
	if req.Pass != "Hix4RM9nm7NakogmckaZrJiiCTXFfm4m" {
		output.SuccessNil(ctx)
		return
	}

	resp := &infoResp{}
	resp.BuildTime = movelyServer.BuildTime
	resp.Git = movelyServer.Git

	output.Success(ctx, resp)
}
