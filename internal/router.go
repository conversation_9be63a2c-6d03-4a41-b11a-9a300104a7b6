package internal

import (
	"net/http"

	config_handler "git.hoxigames.xyz/movely/movely-server/internal/config"
	"git.hoxigames.xyz/movely/movely-server/internal/feeds"
	"git.hoxigames.xyz/movely/movely-server/internal/handler"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/more"
	"git.hoxigames.xyz/movely/movely-server/internal/onboarding"
	"git.hoxigames.xyz/movely/movely-server/internal/resource"
	"git.hoxigames.xyz/movely/movely-server/internal/user"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func Init(engine *gin.Engine) {
	app.Init(logger.SugaredLogger)

	engine.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"https://localhost:3000", "https://staging.movelyai.com", "https://movelyai.com"},
		AllowMethods:     []string{"GET", "POST", "OPTIONS"},
		AllowHeaders:     []string{"Content-Type,AccessToken,X-CSRF-Token, Authorization, Token, Analytics"},
		ExposeHeaders:    []string{"Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type"},
		AllowCredentials: true,
	}))

	root := engine.Group("/")
	root.Use(app.BuildExternalCtx())
	root.Use(app.ExternalHandler(middleware.TraceMiddleware))
	// root.Use(app.ExternalHandler(middleware.Cors))

	dev := root.Group("/dev")
	dev.GET("info", app.ExternalHandler((&more.Build{}).Info))
	dev.GET("ping", func(ctx *gin.Context) {
		ctx.JSON(http.StatusOK, gin.H{
			"msg": "pong",
		})
	})

	v1 := root.Group("/v1")

	userGroup := v1.Group("/user")
	userHandle := user.NewUser()
	userGroup.POST("verifyapple", app.ExternalHandler(userHandle.VerifyApple))
	userGroup.POST("verifygoogle", app.ExternalHandler(userHandle.VerifyGoogle))
	userGroup.POST("verifyvcode", app.ExternalHandler(userHandle.VerifyVCode))
	userGroup.POST("sendvcodetoemail", app.ExternalHandler(userHandle.SendVCodeToEmail))
	userGroup.POST("token/refresh", app.ExternalHandler(userHandle.RefreshToken))
	userGroup.Use(app.ExternalHandler(middleware.Auth))
	userGroup.POST("getuserinfo", app.ExternalHandler(userHandle.GetUserInfo))
	userGroup.POST("update", app.ExternalHandler(userHandle.Update))
	userGroup.POST("delete", app.ExternalHandler(userHandle.Delete))

	resourceGroup := v1.Group("/resource")
	resourceGroup.Use(app.ExternalHandler(middleware.Auth))
	resourceGroup.POST("upload", app.ExternalHandler((&resource.Resource{}).Upload))
	resourceGroup.GET("history", app.ExternalHandler((&resource.Resource{}).History))
	resourceGroup.POST("remove", app.ExternalHandler((&resource.Resource{}).Remove))

	homeGroup := v1.Group("/home")
	// homeGroup.Use(app.ExternalHandler(middleware.Auth))
	homeHandle := &feeds.Home{}
	homeGroup.POST("config", app.ExternalHandler(homeHandle.HomeConfig))

	feedsGroup := v1.Group("/feeds")
	// feedsGroup.Use(app.ExternalHandler(middleware.Auth))
	feedsHandle := &feeds.Feeds{}
	feedsGroup.POST("template/list", app.ExternalHandler(feedsHandle.TemplateList))
	feedsGroup.POST("template/myfx", app.ExternalHandler(middleware.Auth), app.ExternalHandler(feedsHandle.TemplateMyfx))
	feedsGroup.POST("template/like", app.ExternalHandler(middleware.Auth), app.ExternalHandler(feedsHandle.TemplateLike))
	feedsGroup.POST("template/share", app.ExternalHandler(middleware.Auth), app.ExternalHandler(feedsHandle.TemplateShare))
	feedsGroup.POST("template/search", app.ExternalHandler(feedsHandle.TemplateSearch))
	feedsGroup.POST("tag/like", app.ExternalHandler(middleware.Auth), app.ExternalHandler(feedsHandle.TagLike))
	feedsGroup.GET("tag/list", app.ExternalHandler(middleware.Auth), app.ExternalHandler(feedsHandle.TagList))

	v1.GET("template/query", app.ExternalHandler(feedsHandle.TemplateQuery))
	v1.GET("tag/query", app.ExternalHandler(feedsHandle.TagQuery))

	paymentGroup := v1.Group("/payment")
	paymentHandle := handler.NewPaymentHandler()

	paymentGroup.GET("product/list", app.ExternalHandler(paymentHandle.ProductList))
	paymentGroup.POST("apple/webhook", app.ExternalHandler(paymentHandle.AppleWebhook))
	paymentGroup.POST("stripe/webhook", app.ExternalHandler(paymentHandle.StripeWebhook))

	paymentGroup.POST("apple/purchase", app.ExternalHandler(middleware.Auth), app.ExternalHandler(paymentHandle.ApplePurchase))
	paymentGroup.POST("apple/restore", app.ExternalHandler(middleware.Auth), app.ExternalHandler(paymentHandle.AppleRestore))
	paymentGroup.POST("stripe/order/create", app.ExternalHandler(middleware.Auth), app.ExternalHandler(paymentHandle.StripeOrderCreate))
	paymentGroup.POST("stripe/billing_portal_url", app.ExternalHandler(middleware.Auth), app.ExternalHandler(paymentHandle.GetBillingPortalURL))

	taskGroup := v1.Group("/task")
	taskGroup.Use(app.ExternalHandler(middleware.Auth))
	videoTaskHandle := handler.NewVideoTaskHandler()
	taskGroup.POST("video/create", app.ExternalHandler(videoTaskHandle.VideoCreate))
	taskGroup.POST("video/query", app.ExternalHandler(videoTaskHandle.VideoQuery))
	taskGroup.POST("video/cancel", app.ExternalHandler(videoTaskHandle.VideoCancel))
	taskGroup.GET("video/history", app.ExternalHandler(videoTaskHandle.VideoHistory))

	onboardingGroup := v1.Group("/onboarding")
	onboardingGroup.GET("", app.ExternalHandler((&onboarding.Onboarding{}).Items))
	onboardingGroup.Use(app.ExternalHandler(middleware.Auth))
	onboardingGroup.POST("", app.ExternalHandler((&onboarding.Onboarding{}).Select))

	configGroup := v1.Group("/config")
	configGroup.GET("longconn", app.ExternalHandler(middleware.Auth), app.ExternalHandler(config_handler.Longconn))
}
