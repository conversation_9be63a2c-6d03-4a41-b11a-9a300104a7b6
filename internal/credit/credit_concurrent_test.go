package credit

import (
	"fmt"
	"os"
	"sync"
	"testing"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"

	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
)

func TestCreditConcurrent_AddAndDeduct(t *testing.T) {
	_ = os.Setenv("ENVIRON", "local")
	err := config.Init("../../etc/app/local.json")
	if err != nil {
		return
	}
	err = logger.Init()
	if err != nil {
		return
	}
	err = rdb.Init()
	if err != nil {
		return
	}
	userId := int64(1944971929024090112)

	addAmount := int64(10)
	deductAmount := int64(20)

	decrFail := 0
	incrFail := 0
	addCount := 20
	deductCount := 0

	var wg sync.WaitGroup
	wg.Add(addCount + deductCount)

	for i := 0; i < addCount; i++ {
		go func() {
			defer wg.Done()
			err = (&Credit{}).freeCreditGiveawayAdd(&app.Context{}, FreeCreditAddParam{
				UserId:       userId,
				BusinessType: enums.FreeGiveawayBusinessType,
				Balance:      addAmount,
			})
			if err != nil {
				incrFail += 1
				fmt.Println("incrFail", err)
			}
			err = (&Credit{}).RechargeCreditAdd(&app.Context{}, RechargeCreditAddParam{
				UserId:       userId,
				BusinessType: enums.FreeGiveawayBusinessType,
				Balance:      addAmount,
			})
			if err != nil {
				incrFail += 1
				fmt.Println("incrFail", err)
			}
		}()
	}

	for i := 0; i < deductCount; i++ {
		go func() {
			defer wg.Done()
			_, err = (&Credit{}).DeductCredit(&app.Context{}, userId, deductAmount, "test")
			if err != nil {
				decrFail += 1
				fmt.Println("decrFail", err)
			}
		}()
	}

	wg.Wait()

	fmt.Println("decrFail:", decrFail)
	fmt.Println("incrFail:", incrFail)
}
