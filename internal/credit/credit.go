package credit

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/enums"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"github.com/bsm/redislock"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type (
	Credit struct{}
)

var (
	BalanceLteZeroErr      = errors.New("balance lte zero")
	BalanceNotEnoughErr    = errors.New("balance not enough")
	UnknownBusinessTypeErr = errors.New("unknown business type")
	UnknownSceneErr        = errors.New("unknown scene")
	ProductNotExitErr      = errors.New("product not exit")
)

const (
	incr = 1
	decr = 2
)

func logError(ctx *app.Context, msg string, err error, userId int64, businessType int32, balance int64) {
	logger.ErrorWithAppCtx(ctx, msg, zap.Error(err), zap.Int64("userId", userId),
		zap.Int32("businessType", businessType), zap.Int64("balance", balance))
}

func checkBalance(balance int64) error {
	if balance <= 0 {
		return BalanceLteZeroErr
	}
	return nil
}

func selectMin(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

type RechargeCreditAddParam struct {
	UserId           int64
	BusinessType     int32
	Balance          int64
	ProductId        string
	ExpireAt         int64
	Scene            string
	RewardId         int64
	RewardLastReward int64
}

func (c *Credit) RechargeCreditAdd(ctx *app.Context, param RechargeCreditAddParam) error {
	if param.Scene == "" {
		return UnknownSceneErr
	}

	switch param.BusinessType {
	case enums.RechargeSubsBusinessType:
		return c.rechargeCreditSubAdd(ctx, param)
	case enums.RechargePurBusinessType:
		return c.rechargeCreditPurAdd(ctx, param)
	default:
		return UnknownBusinessTypeErr
	}
}

func (c *Credit) rechargeCreditPurAdd(ctx *app.Context, param RechargeCreditAddParam) error {
	userId := param.UserId
	businessType := param.BusinessType
	balance := param.Balance

	if err := checkBalance(balance); err != nil {
		return err
	}

	tx := rdb.Rdb.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.ErrorWithAppCtx(ctx, "RechargeCreditPurAdd-recover", zap.Any("recover", r))
		}
	}()

	_, oldBalance := c.GetRechargeWithBusinessType(tx, userId, businessType)
	credit := model.RechargeCredit{
		UserID:       userId,
		BusinessType: businessType,
		Balance:      balance,
	}
	err := tx.Model(&model.RechargeCredit{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "user_id"}, {Name: "business_type"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"balance": gorm.Expr("balance + ?", credit.Balance),
		}),
	}).Create(&credit).Error
	if err != nil {
		tx.Rollback()
		logger.ErrorWithAppCtx(ctx, "RechargeCreditPurAdd-OnConflict", zap.Error(err), zap.Any("credit", credit))
		return err
	}
	_, newBalance := c.GetRechargeWithBusinessType(tx, userId, businessType)
	creditsLog := model.CreditsLog{
		UserID:       userId,
		Action:       incr,
		Scene:        param.Scene,
		BusinessType: businessType,
		Old:          oldBalance,
		New:          newBalance,
		Count:        1,
		Total:        balance,
	}
	err = tx.Create(&creditsLog).Error
	if err != nil {
		tx.Rollback()
		logger.ErrorWithAppCtx(ctx, "RechargeCreditPurAdd-CreditsLog", zap.Any("credit", credit))
		return err
	}

	// 根据不同的Scene执行
	switch param.Scene {
	case enums.PurScene:
		if product := enums.GetProduct(param.ProductId, config.Configuration.Env); product != nil {
			creditRechargeLog := model.CreditRechargeLog{
				UserID:    userId,
				ProductID: product.Id,
				Amount:    balance,
				Entry:     c.getCreditRechargeEntry(product.Id),
			}
			err = tx.Create(&creditRechargeLog).Error
			if err != nil {
				tx.Rollback()
				logger.ErrorWithAppCtx(ctx, "RechargeCreditPurAdd-CreditRechargeLog", zap.Any("credit", credit))
				return err
			}
		}
	}
	if err = tx.Commit().Error; err != nil {
		logger.ErrorWithAppCtx(ctx, "RechargeCreditPurAdd-Commit", zap.Error(err), zap.Any("credit", credit))
		return err
	}
	logger.InfoWithAppCtx(ctx, "RechargeCreditPurAdd-Success", zap.Any("credit", credit))
	return nil
}

func (c *Credit) rechargeCreditSubAdd(ctx *app.Context, param RechargeCreditAddParam) error {
	userId := param.UserId
	businessType := param.BusinessType
	balance := param.Balance

	if err := checkBalance(balance); err != nil {
		return err
	}

	lock, err := rdb.RedisLock.Obtain(context.Background(), fmt.Sprintf("movely-server|RechargeCredit|%d", userId),
		5*time.Minute, &redislock.Options{RetryStrategy: redislock.ExponentialBackoff(5, 100*time.Millisecond)})
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.Release(context.Background())
	}()

	tx := rdb.Rdb.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.ErrorWithAppCtx(ctx, "RechargeCreditSubAdd-recover", zap.Any("recover", r))
		}
	}()

	err, oldBalance := c.GetRechargeWithBusinessType(tx, param.UserId, param.BusinessType)
	if err != nil {
		tx.Rollback()
		logError(ctx, "RechargeCreditSubAdd-GetRechargeWithBusinessType", err, userId, businessType, balance)
		return err
	}

	updates := map[string]interface{}{"balance": balance}
	switch param.Scene {
	case enums.SubScene:
		isUpgrade, add, err := c.rechargeCreditSubAddCheckBalance(ctx, param)
		if err == nil && isUpgrade {
			updates["balance"] = gorm.Expr("balance + ?", add)
		}
	case enums.RefundScene:
		updates["balance"] = gorm.Expr("balance + ?", balance)
	}

	err = tx.Model(&model.RechargeCredit{}).Where("user_id = ? AND business_type = ?", userId, businessType).
		Updates(updates).Error
	if err != nil {
		tx.Rollback()
		logError(ctx, "RechargeCreditSubAdd-Updates", err, userId, businessType, balance)
		return err
	}
	_, newBalance := c.GetRechargeWithBusinessType(tx, userId, businessType)
	creditsLog := model.CreditsLog{
		UserID:       userId,
		Action:       incr,
		Scene:        param.Scene,
		BusinessType: businessType,
		Old:          oldBalance,
		New:          newBalance,
		Count:        1,
		Total:        balance,
	}
	err = tx.Create(&creditsLog).Error
	if err != nil {
		tx.Rollback()
		logError(ctx, "RechargeCreditSubAdd-CreditsLog", err, userId, businessType, balance)
		return err
	}

	// 根据不同的Scene执行
	switch param.Scene {
	case enums.SubScene:
		if product := enums.GetProduct(param.ProductId, config.Configuration.Env); product != nil {
			creditRechargeLog := model.CreditRechargeLog{
				ExpireAt:  param.ExpireAt,
				UserID:    userId,
				ProductID: product.Id,
				Amount:    balance,
				Entry:     c.getCreditRechargeEntry(product.Id),
			}
			err = tx.Model(&model.CreditRechargeLog{}).Create(&creditRechargeLog).Error
			if err != nil {
				tx.Rollback()
				logError(ctx, "RechargeCreditSubAdd-creditRechargeLog-Create", err, userId, businessType, balance)
				return err
			}
			if c.IsAnnual(product.Id) {
				creditReward := model.CreditReward{
					UserID:          userId,
					BusinessType:    businessType,
					RechargeBalance: product.Credits.BaseCount,
					LastReward:      time.Now().Unix(),
				}
				err = tx.Model(&model.CreditReward{}).Create(&creditReward).Error
				if err != nil {
					tx.Rollback()
					logError(ctx, "RechargeCreditSubAdd-CreditReward-Create", err, userId, businessType, balance)
					return err
				}
			}
		}
	case enums.RewardScene:
		err = tx.Model(&model.CreditReward{}).Where("id = ?", param.RewardId).
			Updates(map[string]interface{}{"last_reward": param.RewardLastReward}).Error
		if err != nil {
			tx.Rollback()
			logError(ctx, "RechargeCreditSubAdd-CreditReward-Update", err, userId, businessType, balance)
			return err
		}
	}

	if err = tx.Commit().Error; err != nil {
		logError(ctx, "RechargeCreditSubAdd-Commit", err, userId, businessType, balance)
		return err
	}
	logger.InfoWithAppCtx(ctx, "RechargeCreditSubAdd-Success", zap.Any("creditsLog", creditsLog))
	return nil
}

func (c *Credit) rechargeCreditSubAddCheckBalance(ctx *app.Context, param RechargeCreditAddParam) (bool, int64, error) {
	nowProduct := enums.GetProduct(param.ProductId, config.Configuration.Env)
	if nowProduct == nil {
		return false, 0, ProductNotExitErr
	}
	creditRechargeLog := model.CreditRechargeLog{}
	err := rdb.Rdb.Model(&model.CreditRechargeLog{}).Where("user_id = ? AND entry = ?", param.UserId, enums.SubscribeEntry).
		Order("create_at desc").First(&creditRechargeLog).Error
	if err != nil {
		return false, nowProduct.Credits.BaseCount, nil
	}
	if creditRechargeLog.ExpireAt < time.Now().Unix() {
		return false, nowProduct.Credits.BaseCount, nil
	}

	if creditRechargeLog.ProductID != nowProduct.Id {
		// 用户用了多少
		/*
			已给 30 币  ->  升级 60 币
			第0天升级:   补30币
			第1天升级:   补31币
			第2天升级:   补32币
			第29天升级:  补59币
			第30天升级:  补60币
			第31天升级:  补60币
			第310天升级: 补60币
		*/
		useDay := int64(time.Since(creditRechargeLog.CreateAt).Hours() / 24)
		deserveCoin := math.Ceil(float64(creditRechargeLog.Amount) * float64(useDay) / 30.0)
		addCount := nowProduct.Credits.BaseCount - creditRechargeLog.Amount + int64(deserveCoin)
		if addCount < 0 {
			addCount = 0
		}
		if addCount > nowProduct.Credits.BaseCount {
			addCount = nowProduct.Credits.BaseCount
		}
		return true, addCount, nil
	} else {
		return false, nowProduct.Credits.BaseCount, nil
	}
}

type FreeCreditAddParam struct {
	UserId       int64
	BusinessType int32
	Balance      int64
	Scene        string
}

func (c *Credit) FreeCreditAdd(ctx *app.Context, param FreeCreditAddParam) error {
	if param.Scene == "" {
		return UnknownSceneErr
	}

	switch param.BusinessType {
	case enums.FreeAutoBusinessType:
		return c.freeCreditAutoAdd(ctx, param)
	case enums.FreeGiveawayBusinessType:
		return c.freeCreditGiveawayAdd(ctx, param)
	default:
		return UnknownBusinessTypeErr
	}
}

func (c *Credit) freeCreditAutoAdd(ctx *app.Context, param FreeCreditAddParam) error {
	userId := param.UserId
	balance := param.Balance
	businessType := int32(enums.FreeAutoBusinessType)

	now := time.Now().Unix()
	// 普通用户 默认
	factor := enums.FactorDefault
	maxCredit := enums.MaxDefault
	supplyRate := enums.SupplyRateDefault

	// 获取会员状态
	var user model.User
	err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", userId).First(&user).Error
	if err != nil {
		logError(ctx, "FreeCreditAutoAdd-User", err, userId, businessType, balance)
		return err
	}
	if user.SubscribeProductID != "" {
		product := enums.GetProduct(user.SubscribeProductID, config.Configuration.Env)
		if product != nil {
			factor = product.Credits.RewardIntervalCount
			maxCredit = product.Credits.RewardMaxLimit
			supplyRate = product.Credits.RewardIntervalSeconds
		}
	}

	lock, err := rdb.RedisLock.Obtain(context.Background(), fmt.Sprintf("movely-server|FreeCredit|%d", userId),
		5*time.Minute, &redislock.Options{RetryStrategy: redislock.ExponentialBackoff(5, 100*time.Millisecond)})
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.Release(context.Background())
	}()

	tx := rdb.Rdb.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.ErrorWithAppCtx(ctx, "FreeCreditAutoAdd-recover", zap.Any("recover", r))
		}
	}()

	var oldBalance int64
	var credit model.FreeCredit
	err = tx.Model(&model.FreeCredit{}).Where("user_id = ? AND business_type = ?", userId, businessType).First(&credit).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		oldBalance = 0
		credit = model.FreeCredit{
			UserID:       userId,
			BusinessType: businessType,
			Balance:      balance,
			LastSupply:   now,
		}
		err = tx.Create(&credit).Error
		if err != nil {
			tx.Rollback()
			logError(ctx, "FreeCreditAutoAdd-Create", err, userId, businessType, balance)
			return err
		}
	} else if err != nil {
		tx.Rollback()
		logError(ctx, "FreeCreditAutoAdd-Where", err, userId, businessType, balance)
		return err
	} else {
		oldBalance = credit.Balance
	}

	_, giveawayBalance := c.GetFreeWithBusinessType(tx, userId, enums.FreeGiveawayBusinessType)

	var increment int64
	var logFields []zap.Field
	updateFields := make(map[string]interface{})
	hours := (now - credit.LastSupply) / supplyRate
	add := hours * factor
	canAdd := maxCredit - (credit.Balance + giveawayBalance)
	newLastSupply := credit.LastSupply + hours*supplyRate
	if canAdd > 0 {
		if add > 0 {
			increment = selectMin(add+balance, canAdd)
			updateFields["balance"] = gorm.Expr("balance + ?", increment)
			updateFields["last_supply"] = newLastSupply
		} else if balance > 0 {
			increment = selectMin(balance, canAdd)
			updateFields["balance"] = gorm.Expr("balance + ?", increment)
		}
		logFields = []zap.Field{
			zap.Int64("now", now),
			zap.Int64("hours", hours),
			zap.Int64("canAdd", canAdd),
			zap.Int64("factor", factor),
			zap.Int64("increment", increment),
			zap.Int64("maxCredit", maxCredit),
			zap.Int64("balance", credit.Balance),
			zap.Int64("newLastSupply", newLastSupply),
			zap.Int64("lastSupply", credit.LastSupply),
			zap.Int64("SupplyRate", supplyRate),
		}
		if len(updateFields) > 0 {
			err = tx.Model(&model.FreeCredit{}).
				Where("user_id = ? AND business_type = ?", userId, businessType).
				Updates(updateFields).Error
			if err != nil {
				tx.Rollback()
				logFields = append(logFields, zap.Error(err))
				logger.ErrorWithAppCtx(ctx, "FreeCreditAutoAdd-Updates", logFields...)
				return err
			}
		}
	}
	_, newBalance := c.GetFreeWithBusinessType(tx, userId, businessType)
	creditsLog := model.CreditsLog{
		UserID:       userId,
		Action:       incr,
		Scene:        param.Scene,
		BusinessType: businessType,
		Old:          oldBalance,
		New:          newBalance,
		Count:        1,
		Total:        increment,
	}
	err = tx.Create(&creditsLog).Error
	if err != nil {
		tx.Rollback()
		logFields = append(logFields, zap.Error(err))
		logger.ErrorWithAppCtx(ctx, "FreeCreditAutoAdd-CreditsLog", logFields...)
		return err
	}
	if err = tx.Commit().Error; err != nil {
		logFields = append(logFields, zap.Error(err))
		logger.ErrorWithAppCtx(ctx, "FreeCreditAutoAdd-Commit", logFields...)
		return err
	}
	logger.InfoWithAppCtx(ctx, "FreeCreditAutoAdd-Success", logFields...)
	return nil
}

func (c *Credit) freeCreditGiveawayAdd(ctx *app.Context, param FreeCreditAddParam) error {
	userId := param.UserId
	balance := param.Balance
	businessType := int32(enums.FreeGiveawayBusinessType)

	if err := checkBalance(balance); err != nil {
		return err
	}

	tx := rdb.Rdb.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.ErrorWithAppCtx(ctx, "FreeCreditGiveawayAdd-recover", zap.Any("recover", r))
		}
	}()

	_, oldBalance := c.GetFreeWithBusinessType(tx, userId, businessType)
	credit := model.FreeCredit{
		UserID:       userId,
		BusinessType: businessType,
		Balance:      balance,
	}
	err := tx.Model(&model.FreeCredit{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "user_id"}, {Name: "business_type"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"balance": gorm.Expr("balance + ?", credit.Balance),
		}),
	}).Create(&credit).Error
	if err != nil {
		tx.Rollback()
		logger.ErrorWithAppCtx(ctx, "FreeCreditGiveawayAdd-OnConflict", zap.Error(err), zap.Any("credit", credit))
		return err
	}

	_, newBalance := c.GetFreeWithBusinessType(tx, userId, businessType)
	creditsLog := model.CreditsLog{
		UserID:       userId,
		Action:       incr,
		Scene:        param.Scene,
		BusinessType: businessType,
		Old:          oldBalance,
		New:          newBalance,
		Count:        1,
		Total:        balance,
	}
	err = tx.Create(&creditsLog).Error
	if err != nil {
		tx.Rollback()
		logger.ErrorWithAppCtx(ctx, "FreeCreditGiveawayAdd-CreditsLog", zap.Error(err), zap.Any("credit", credit))
		return err
	}
	if err = tx.Commit().Error; err != nil {
		logger.ErrorWithAppCtx(ctx, "FreeCreditGiveawayAdd-Commit", zap.Error(err), zap.Any("credit", credit))
		return err
	}
	logger.InfoWithAppCtx(ctx, "FreeCreditGiveawayAdd-Success", zap.Any("credit", credit))
	return nil
}

func (c *Credit) DeductCredit(ctx *app.Context, userId int64, amount int64, scene string) ([]enums.DeductCredits, error) {
	_ = c.FreeCreditAdd(ctx, FreeCreditAddParam{
		UserId:       userId,
		BusinessType: enums.FreeAutoBusinessType,
		Balance:      0,
		Scene:        enums.FreeAutoScene,
	})

	if err := checkBalance(amount); err != nil {
		return nil, err
	}
	remain := amount

	lock, err := rdb.RedisLock.Obtain(context.Background(), fmt.Sprintf("movely-server|DeductCredit|%d", userId),
		5*time.Minute, &redislock.Options{RetryStrategy: redislock.ExponentialBackoff(5, 100*time.Millisecond)})
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = lock.Release(context.Background())
	}()

	tx := rdb.Rdb.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.ErrorWithAppCtx(ctx, "DeductCredit-recover", zap.Any("recover", r))
		}
	}()

	var deductCredits []enums.DeductCredits

	types := []struct {
		table        interface{}
		businessType int32
		isRecharge   bool
	}{
		{&model.FreeCredit{}, enums.FreeAutoBusinessType, false},
		{&model.FreeCredit{}, enums.FreeGiveawayBusinessType, false},
		{&model.RechargeCredit{}, enums.RechargeSubsBusinessType, true},
		{&model.RechargeCredit{}, enums.RechargePurBusinessType, true},
	}
	for _, t := range types {
		if remain <= 0 {
			break
		}
		var balance int64
		if t.isRecharge {
			var recharge model.RechargeCredit
			tx.Model(t.table).Where("user_id = ? AND business_type = ?", userId, t.businessType).First(&recharge)
			balance = recharge.Balance
		} else {
			var free model.FreeCredit
			tx.Model(t.table).Where("user_id = ? AND business_type = ?", userId, t.businessType).First(&free)
			balance = free.Balance
		}
		deduct := selectMin(balance, remain)
		if deduct <= 0 {
			continue
		}

		err = tx.Model(t.table).Where("user_id = ? AND business_type = ? and balance >= ?", userId, t.businessType, deduct).
			Updates(map[string]interface{}{"balance": gorm.Expr("balance - ?", deduct)}).Error
		if err != nil {
			logError(ctx, "DeductCredit-Deduct", err, userId, t.businessType, deduct)
			continue
		}
		creditsLog := model.CreditsLog{
			UserID:       userId,
			Action:       decr,
			Scene:        scene,
			BusinessType: t.businessType,
			Old:          balance,
			New:          balance - deduct,
			Count:        1,
			Total:        deduct,
		}
		err = tx.Create(&creditsLog).Error
		if err != nil {
			tx.Rollback()
			logError(ctx, "DeductCredit-CreditsLog", err, userId, t.businessType, deduct)
			return nil, err
		}
		deductCredits = append(deductCredits, enums.DeductCredits{
			Balance:      deduct,
			BusinessType: t.businessType,
		})
		remain -= deduct
	}

	if remain != 0 {
		tx.Rollback()
		logError(ctx, "DeductCredit-remain", BalanceNotEnoughErr, userId, 0, amount)
		return nil, BalanceNotEnoughErr
	}
	if err := tx.Commit().Error; err != nil {
		logError(ctx, "DeductCredit-Commit", err, userId, 0, amount)
		return nil, err
	}
	logger.InfoWithAppCtx(ctx, "DeductCredit-Success", zap.Int64("amount", amount), zap.Int64("userId", userId))
	return deductCredits, nil
}

func (c *Credit) GetFreeCredits(ctx *app.Context, userId int64) (map[int32]model.FreeCredit, error) {
	_ = c.FreeCreditAdd(ctx, FreeCreditAddParam{
		UserId:       userId,
		BusinessType: enums.FreeAutoBusinessType,
		Balance:      0,
		Scene:        enums.FreeAutoScene,
	})

	businessTypes := []int32{enums.FreeAutoBusinessType, enums.FreeGiveawayBusinessType}

	var credits []model.FreeCredit
	err := rdb.Rdb.Model(&model.FreeCredit{}).
		Where("user_id = ? AND business_type IN ?", userId, businessTypes).
		Find(&credits).Error
	if err != nil {
		return nil, err
	}
	result := make(map[int32]model.FreeCredit)
	for _, c := range credits {
		result[c.BusinessType] = c
	}
	return result, nil
}

func (c *Credit) GetRechargeCredits(ctx *app.Context, userId int64) (map[int32]model.RechargeCredit, error) {
	businessTypes := []int32{enums.RechargePurBusinessType, enums.RechargeSubsBusinessType}

	var credits []model.RechargeCredit
	err := rdb.Rdb.Model(&model.RechargeCredit{}).
		Where("user_id = ? AND business_type IN ?", userId, businessTypes).
		Find(&credits).Error
	if err != nil {
		return nil, err
	}
	result := make(map[int32]model.RechargeCredit)
	for _, c := range credits {
		result[c.BusinessType] = c
	}
	return result, nil
}

func (c *Credit) GetRechargeWithBusinessType(tx *gorm.DB, userId int64, businessType int32) (error, int64) {
	if tx == nil {
		tx = rdb.Rdb
	}
	credit := model.RechargeCredit{}
	err := tx.Model(&model.RechargeCredit{}).Where("user_id = ? and business_type = ?", userId, businessType).First(&credit).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err, 0
	}
	return nil, credit.Balance
}

func (c *Credit) GetFreeWithBusinessType(tx *gorm.DB, userId int64, businessType int32) (error, int64) {
	if tx == nil {
		tx = rdb.Rdb
	}
	credit := model.FreeCredit{}
	err := tx.Model(&model.FreeCredit{}).Where("user_id = ? and business_type = ?", userId, businessType).First(&credit).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err, 0
	}
	return nil, credit.Balance
}

func (c *Credit) RechargeReward(ctx *app.Context, userId int64) error {
	businessType := int32(enums.RechargeSubsBusinessType)
	var user model.User
	err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", userId).First(&user).Error
	if err != nil {
		logger.ErrorWithAppCtx(ctx, "RechargeReward-User", zap.Error(err), zap.Int64("userId", userId))
		return err
	}
	if user.SubscribeProductID == "" {
		return nil
	}
	if !c.IsAnnual(user.SubscribeProductID) {
		return nil
	}
	product := enums.GetProduct(user.SubscribeProductID, config.Configuration.Env)
	if product == nil {
		common.SendServerAlarmMsg(context.Background(), userId, "",
			fmt.Sprintf("product not found, product id: %s", user.SubscribeProductID))
		return nil
	}

	now := time.Now().Unix()
	creditReward := model.CreditReward{}
	err = rdb.Rdb.Model(&model.CreditReward{}).Where("user_id = ? AND business_type = ?", userId, businessType).
		Order("create_at desc").First(&creditReward).Error
	if err != nil {
		return err
	}

	lastRewardTime := creditReward.LastReward

	if now >= creditReward.LastReward+int64(enums.RechargeRewardInterval.Seconds()) {
		lastRewardTime = creditReward.LastReward + int64(enums.RechargeRewardInterval.Seconds())
		return c.RechargeCreditAdd(ctx, RechargeCreditAddParam{
			UserId:           userId,
			BusinessType:     businessType,
			Balance:          creditReward.RechargeBalance,
			Scene:            enums.RewardScene,
			RewardId:         creditReward.ID,
			RewardLastReward: lastRewardTime,
		})
	}
	return nil
}

func (c *Credit) IsAnnual(id string) bool {
	return strings.Contains(strings.ToLower(id), "annual")
}

func (c *Credit) getCreditRechargeEntry(id string) string {
	if strings.HasPrefix(id, "movely_video_credits") {
		return enums.PurchaseEntry
	} else {
		return enums.SubscribeEntry
	}
}

func (c *Credit) RefundCredits(ctx *app.Context, userId int64, deducts []enums.DeductCredits) error {
	for _, deduct := range deducts {
		switch deduct.BusinessType {
		case enums.FreeAutoBusinessType, enums.FreeGiveawayBusinessType:
			_ = c.FreeCreditAdd(ctx, FreeCreditAddParam{
				UserId:       userId,
				BusinessType: deduct.BusinessType,
				Balance:      deduct.Balance,
				Scene:        enums.RefundScene,
			})
		case enums.RechargeSubsBusinessType, enums.RechargePurBusinessType:
			_ = c.RechargeCreditAdd(ctx, RechargeCreditAddParam{
				UserId:       userId,
				BusinessType: deduct.BusinessType,
				Balance:      deduct.Balance,
				Scene:        enums.RefundScene,
			})
		}
	}
	return nil
}

func (c *Credit) ClearCredits(ctx *app.Context, userId int64) {
	_ = c.RechargeCreditAdd(ctx, RechargeCreditAddParam{
		UserId:       userId,
		BusinessType: enums.RechargeSubsBusinessType,
		Balance:      0,
		Scene:        enums.ClearScene,
	})
}
