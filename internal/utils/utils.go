package utils

import (
	"encoding/json"
	"fmt"
	"regexp"
	"runtime"
	"strings"

	"git.hoxigames.xyz/movely/movely-server/config"
)

func GetStackInfo() string {
	var buf [4096]byte
	n := runtime.Stack(buf[:], false)
	return string(buf[:n])
}

func ConvertS3ToCDN(s3URL string) string {
	if !strings.HasPrefix(s3URL, "s3://") {
		return s3URL
	}
	path := strings.TrimPrefix(s3URL, "s3://")
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		return s3URL
	}
	//bucket := parts[0]
	objectPath := parts[1]
	cdnURL := fmt.Sprintf("%s/%s", config.Configuration.Aws.Cdn.Srv.Domain, objectPath)
	return cdnURL
}

func ToJsonString(v any) string {
	bs, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(bs)
}

func ExtractMemberShipFromProduct(s string) string {
	if s == "" {
		return ""
	}
	s = strings.TrimPrefix(s, "dev_")
	re := regexp.MustCompile(`^movely_([a-zA-Z]+(?:_[a-zA-Z]+)*)_\d+$`)
	matches := re.FindStringSubmatch(s)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}
