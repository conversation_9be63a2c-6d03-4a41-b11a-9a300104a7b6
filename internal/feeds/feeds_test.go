package feeds

import (
	"context"
	"fmt"
	"math/rand/v2"
	"os"
	"testing"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/s3"
	"gorm.io/gorm"
)

func TestTrending(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	s3.Init()
	bizconf.Init(config.Configuration.LanguageConf)
	rdb.Init()
	trending := NewTrendingRecommend(1944957363481366528, 1, common.ShelfTrendingKey)
	templateList, err := trending.GetTemplateNameList(context.Background(), &GetTemplateNameListReq{
		Start: 0,
		Size:  10,
	})
	fmt.Println(templateList.GenerateTypes, err)
	entries, err := common.GetTemplateListByGenerateType(templateList.GenerateTypes, 1944957363481366528, "en")
	for _, entry := range entries {
		fmt.Println(entry.GenerateType, entry.TemplateLevel)
	}
}

func TestForyou(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	bizconf.Init(config.Configuration.LanguageConf)
	rdb.Init()
	trending := NewForyouRecommend(1944957363481366528, "en", 1)
	templateList, err := trending.GetTemplateNameList(context.Background(), &GetTemplateNameListReq{})
	fmt.Println(templateList, err)
	fmt.Println(len(templateList.GenerateTypes))
}

func TestFeeds(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	users := make([]*model.User, 0)
	err := rdb.Rdb.Model(&model.User{}).Find(&users).Error
	fmt.Println(users, err, users[0].UID)
}

func TestLike(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	generateType := "video_gen_shake_booty"
	err := rdb.Rdb.Transaction(func(tx *gorm.DB) error {
		result := tx.Model(&model.UserTemplateAction{}).Where("user_id = ? AND generate_type = ?", 1942921478829133824, generateType).Delete(&model.UserTemplateAction{})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return nil
		}
		err := tx.Model(&model.ConfigsVideo{}).Where("generate_type = ?", generateType).Where("like_count > 0").UpdateColumn("like_count", gorm.Expr("like_count - 1")).Error
		return err
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestJoin(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	type Result struct {
		TagKey  string `json:"tag_key"`
		TagName string `json:"tag_name"`
	}
	var results []Result
	res := rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ?", 1942921478829133824).Order("updated_at desc").
		Select("user_tag_actions.tag_key, feed_tag.tag_name, feed_tag.status").Joins("join feed_tag on feed_tag.tag_key = user_tag_actions.tag_key").Having("feed_tag.status = 1")
	fmt.Println(rdb.Rdb.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Model(&model.UserTagAction{}).Where("user_id = ?", 1942921478829133824).Order("updated_at desc").
			Select("user_tag_actions.tag_key, feed_tag.tag_name, feed_tag.status").Joins("join feed_tag on feed_tag.tag_key = user_tag_actions.tag_key").Having("feed_tag.status = 1").Find(&results)
	}))
	err := res.Find(&results).Error
	if err != nil {
		t.Fatal(err)
	}
	fmt.Print(results)
}

func TestCreate(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	data := &model.UserTagAction{
		UserID:     1942921478829133824,
		TagKey:     "tag_shelf3",
		ActionType: "like",
	}
	res := rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ? AND tag_key = ?", 1942921478829133824, "tag_shelf3").FirstOrCreate(data)
	fmt.Println(res.Error, data)
}

func TestQueryTemplateTag(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	tags := make([]*model.FeedTag, 0)
	err := rdb.Rdb.Raw("select * from feed_tag where tag_type = ? and status = 1 and tag_key in (select tag_key from template_tags where generate_type = ?)",
		common.TagTypeShelf, "video_gen_sexy_lady").Find(&tags).Error
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(tags)
}

func TestQueryTemplateTagTop(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	// tags := make([]*model.TemplateTag, 0)
	var generateTypes []string
	err := rdb.Rdb.Model(&model.TemplateTag{}).Where("tag_key = ? and top > 0", "tag_trending").Order("top").Select("generate_type").Find(&generateTypes).Error
	if err != nil {
		t.Fatal(err)
	}
	// for _, tag := range tags {
	// 	fmt.Println(tag.GenerateType, tag.Top)
	// }
	fmt.Println(generateTypes)
}

func TestGetNewTemplate(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	videoEntries := make([]model.ConfigsVideo, 0)
	rdb.Rdb.Model(&model.ConfigsVideo{}).Order("id desc").Limit(100).Where("create_time > ?", time.Now().AddDate(0, 0, -1)).Find(&videoEntries)
	for _, videoEntry := range videoEntries {
		fmt.Println(videoEntry.GenerateType, videoEntry.CreateTime)
	}
}

func TestSlice(t *testing.T) {
	datas := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	fmt.Println(datas[0:20])
	fmt.Println(datas[14:20])
}

func TestGetTop(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	var templateTags []*model.TemplateTag
	err := rdb.Rdb.Model(&model.TemplateTag{}).Where("top > 0").Find(&templateTags).Error
	if err != nil {
		t.Fatal(err)
	}
	if len(templateTags) == 0 {
		return
	}
	tagMaps := make(map[string]*model.TemplateTag, 0)
	generateTypes := make([]string, 0)
	for _, tag := range templateTags {
		generateTypes = append(generateTypes, tag.GenerateType)
		tagMaps[tag.GenerateType] = tag
	}
	var templates []*model.ConfigsVideo
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type in ?", generateTypes).Find(&templates).Error
	if err != nil {
		return
	}
	tagTopTemplateMap := make(map[string][]*model.ConfigsVideo)
	for _, template := range templates {
		tag, ok := tagMaps[template.GenerateType]
		if !ok {
			continue
		}
		tagTopTemplateMap[tag.TagKey] = append(tagTopTemplateMap[tag.TagKey], template)
	}
	fmt.Println(tagTopTemplateMap)
}

func TestScanTemplates(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	rdb.Init()
	var templates []*model.ConfigsVideo
	err := rdb.Rdb.Model(&model.ConfigsVideo{}).Find(&templates).Error
	if err != nil {
		t.Fatal(err)
	}
	for _, template := range templates {
		if template.ShareCount == 0 {
			rdb.Rdb.Model(&model.ConfigsVideo{}).Where("id = ?", template.ID).Update("share_count", 1+rand.IntN(10))
		}
		if template.BaseLikeCount == 0 {
			rdb.Rdb.Model(&model.ConfigsVideo{}).Where("id = ?", template.ID).Update("base_like_count", 1+rand.IntN(10))
		}
	}
}
