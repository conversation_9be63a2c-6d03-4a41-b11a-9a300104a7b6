package feeds

import (
	"context"
	"errors"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Feeds struct {
}

type (
	TemplateListReq struct {
		CategoryId string `json:"category_id" binding:"required"`
		Index      int    `json:"index"`
		Size       int    `json:"size"`
		PageInfo   string `json:"page_info"`
	}

	TemplateListResp struct {
		Entries      []*models.VideoEntry `json:"entries"`
		NextIndex    int                  `json:"next_index"`
		NextPageInfo string               `json:"next_page_info"`
		Total        int64                `json:"total"`
	}

	TemplateMyfxReq struct {
		Type  string `json:"type" binding:"required"`
		Index int    `json:"index"`
		Size  int    `json:"size"`
	}

	TemplateLikeReq struct {
		GenerateType string `json:"generate_type" binding:"required"`
		Like         bool   `json:"like"`
	}

	TemplateShareReq struct {
		GenerateType string `json:"generate_type" binding:"required"`
	}

	TagLikeReq struct {
		TagKey string `json:"tag_key" binding:"required"`
		Like   bool   `json:"like"`
	}

	TagListReq struct {
	}

	Tag struct {
		Title       string `json:"title"`
		CategoryTag string `json:"category_tag"`
	}

	TagListResp struct {
		Tags []Tag `json:"tags"`
	}

	TemplateQueryReq struct {
		GenerateType string `json:"generate_type" binding:"required"`
	}

	TemplateQueryResp struct {
		Entry models.VideoEntry `json:"entry"`
	}

	TagQueryResp struct {
		Tag model.FeedTag `json:"tag_info"`
	}
)

type GetTemplateNameListReq struct {
	Start int64
	Size  int64
}

type GetTemplateNameListRsp struct {
	GenerateTypes []string
	NextIndex     int64
}

type TemplateRecommendInterface interface {
	GetTemplateNameList(ctx context.Context, req *GetTemplateNameListReq) (*GetTemplateNameListRsp, error)
	GetTotal(ctx context.Context) (int64, error)
}

func NewTemplateListGenerator(c *app.Context, categoryId string, userLevel int, isNewUser bool) TemplateRecommendInterface {
	// 分类获取推荐
	uid := middleware.GetAccountId(c)
	switch categoryId {
	case common.ShelfTrendingKey:
		return NewTrendingRecommend(uid, userLevel, categoryId)
	case common.ShelfHotMovesKey:
		isGeneratedHotMove := false
		if uid != 0 {
			userAction := &model.UserAction{}
			err := rdb.Rdb.Model(&model.UserAction{}).Where("user_id = ?", uid).Select("use_hotmove").First(userAction).Error
			if err == nil {
				isGeneratedHotMove = userAction.UseHotmove
			} else if err != gorm.ErrRecordNotFound {
				logger.Warn("NewTemplateListGenerator db get user action error", zap.Error(err))
			}
		}
		return NewHotMovesRecommend(c, userLevel, isNewUser, isGeneratedHotMove)
	case common.RecommendForYouKey:
		return NewForyouRecommend(uid, c.Lang, userLevel)
	}
	return NewOtherTagRecommend(uid, isNewUser, userLevel, categoryId)
}

func (feeds *Feeds) TemplateList(c *app.Context) {
	var req TemplateListReq
	err := c.ShouldBind(&req)
	if err != nil || req.Index < 0 {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	if req.Size <= 0 {
		req.Size = 10
	}
	uid := GetUidFromAuth(c)
	// 兼容非登录状态
	user := &model.User{
		UserLevel:  1,
		CreateTime: time.Now(),
	}
	if uid != 0 {
		user, err = common.GetUserInfo(uid)
		if err != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
	}
	var templateList []*models.VideoEntry
	ctx := context.WithValue(context.Background(), logger.TraceIDKey, c.GetTraceID())

	start := int64(req.Index)
	size := int64(req.Size)

	generator := NewTemplateListGenerator(c, req.CategoryId, int(user.UserLevel), user.CreateTime.After(time.Now().AddDate(0, 0, -1)))
	for i := 0; i < 3 && len(templateList) < 10; i++ {
		rsp, err := generator.GetTemplateNameList(ctx, &GetTemplateNameListReq{
			Start: start,
			Size:  size,
		})
		if err != nil {
			logger.ErrorWithCtx(ctx, "TemplateList GetTemplateNameList", zap.Error(err))
			continue
		}
		generateTypes := rsp.GenerateTypes
		curnum := int64(len(generateTypes))
		start = start + curnum
		if curnum <= 0 {
			break
		}
		templates, err := common.GetTemplateListByGenerateType(generateTypes, uid, c.Lang)
		if err != nil {
			logger.ErrorWithCtx(ctx, "TemplateList GetTemplateListByGenerateType", zap.Error(err))
			continue
		}
		templates = FilterTemplateByUserLevel(ctx, int(user.UserLevel), templates)
		templatesNum := int64(len(templates))
		size -= templatesNum
		templateList = append(templateList, templates...)
	}
	total, _ := generator.GetTotal(ctx)
	templateList = common.UniqueSlice(templateList, func(t *models.VideoEntry) string {
		return t.GenerateType
	})
	resp := &TemplateListResp{
		Entries:   templateList,
		NextIndex: int(start),
		Total:     total,
	}
	output.Success(c, resp)
}

func (feeds *Feeds) TemplateMyfx(c *app.Context) {
	var req TemplateMyfxReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	if req.Size <= 0 {
		req.Size = 10
	}
	if req.Type != common.UserTemplateListLike && req.Type != common.UserTemplateListRecent {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	uid := middleware.GetAccountId(c)
	var generateTypes []string
	var total int64
	if req.Type == common.UserTemplateListLike {
		var actions []model.UserTemplateAction
		err = rdb.Rdb.Model(&model.UserTemplateAction{}).Where("user_id = ?", uid).Order("updated_at desc").Offset(req.Index).Limit(req.Size).Find(&actions).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
		rdb.Rdb.Model(&model.UserTemplateAction{}).Where("user_id = ?", uid).Count(&total)
		for _, v := range actions {
			generateTypes = append(generateTypes, v.GenerateType)
		}
	} else {
		var recents []model.UserTemplateRecent
		err = rdb.Rdb.Model(&model.UserTemplateRecent{}).Where("user_id = ?", uid).Order("updated_at desc").Offset(req.Index).Limit(req.Size).Find(&recents).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
		for _, v := range recents {
			generateTypes = append(generateTypes, v.GenerateType)
		}
		rdb.Rdb.Model(&model.UserTemplateRecent{}).Where("user_id = ?", uid).Count(&total)
	}
	entries, err := common.GetTemplateListByGenerateType(generateTypes, uid, c.Lang)
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	resp := &TemplateListResp{
		Entries:   entries,
		NextIndex: req.Index + len(entries),
		Total:     total,
	}
	output.Success(c, resp)
}

func (feeds *Feeds) TemplateLike(c *app.Context) {
	var req TemplateLikeReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	uid := middleware.GetAccountId(c)
	if req.Like {
		var count int64
		err = rdb.Rdb.Model(&model.UserTemplateAction{}).Where("user_id = ?", uid).Count(&count).Error
		if err != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
		if count >= 200 {
			output.Error(c, output.LikeNumLimitErrorCode, err)
			return
		}
		err = rdb.Rdb.Transaction(func(tx *gorm.DB) error {
			result := tx.Model(&model.UserTemplateAction{}).Where("user_id = ? AND generate_type = ?", uid, req.GenerateType).FirstOrCreate(&model.UserTemplateAction{
				UserID:       uid,
				GenerateType: req.GenerateType,
				ActionType:   common.UserVideoActionTypeLike,
			})
			if result.Error != nil {
				return result.Error
			}
			if result.RowsAffected == 0 {
				return nil
			}
			err := tx.Model(&model.ConfigsVideo{}).Where("generate_type = ?", req.GenerateType).UpdateColumn("like_count", gorm.Expr("like_count + 1")).Error
			return err
		})
	} else {
		err = rdb.Rdb.Transaction(func(tx *gorm.DB) error {
			result := tx.Model(&model.UserTemplateAction{}).Where("user_id = ? AND generate_type = ?", uid, req.GenerateType).Delete(&model.UserTemplateAction{})
			if result.Error != nil {
				return result.Error
			}
			if result.RowsAffected == 0 {
				return nil
			}
			err := tx.Model(&model.ConfigsVideo{}).Where("generate_type = ?", req.GenerateType).Where("like_count > 0").UpdateColumn("like_count", gorm.Expr("like_count - 1")).Error
			return err
		})
	}
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, nil)
}

func (feeds *Feeds) TemplateShare(c *app.Context) {
	var req TemplateShareReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type = ?", req.GenerateType).UpdateColumn("share_count", gorm.Expr("share_count + 1")).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, nil)
}

func (feeds *Feeds) TagLike(c *app.Context) {
	var req TagLikeReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	uid := middleware.GetAccountId(c)
	if req.Like {
		var count int64
		err = rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ?", uid).Count(&count).Error
		if err != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
		if count >= 10 {
			output.Error(c, output.LikeNumLimitErrorCode, err)
			return
		}
		result := rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ? AND tag_key = ?", uid, req.TagKey).Assign(&model.UserTagAction{
			ActionType: common.UserVideoActionTypeLike,
		}).FirstOrCreate(&model.UserTagAction{
			UserID:     uid,
			TagKey:     req.TagKey,
			ActionType: common.UserVideoActionTypeLike,
		})
		if result.Error != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
	} else {
		result := rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ? AND tag_key = ?", uid, req.TagKey).UpdateColumn("action_type", "")
		if result.Error != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
	}
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	output.Success(c, nil)
}

func (feeds *Feeds) TagList(c *app.Context) {
	var req TagListReq
	err := c.ShouldBind(&req)
	if err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	uid := middleware.GetAccountId(c)
	type Result struct {
		TagKey  string `json:"tag_key"`
		TagName string `json:"tag_name"`
	}
	var results []Result
	err = rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ?", uid).Order("updated_at desc").
		Select("user_tag_actions.tag_key, feed_tag.tag_name, feed_tag.status").
		Joins("join feed_tag on feed_tag.tag_key = user_tag_actions.tag_key").Having("feed_tag.status = 1").Find(&results).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	var resp TagListResp
	for _, v := range results {
		resp.Tags = append(resp.Tags, Tag{
			Title:       bizconf.LangConf.Text(c.Lang, v.TagName),
			CategoryTag: v.TagKey,
		})
	}
	output.Success(c, resp)
}

func (feeds *Feeds) TemplateQuery(c *app.Context) {
	generateType, ok := c.GetQuery("generate_type")
	if !ok {
		output.Error(c, output.ParamErrorCode, errors.New("generate_type is required"))
		return
	}
	var entry model.ConfigsVideo
	err := rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type = ?", generateType).First(&entry).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	var resp TemplateQueryResp
	resp.Entry = models.VideoEntry{
		VideoType:      entry.VideoType,
		GenerateType:   entry.GenerateType,
		Title:          entry.Title,
		ImageUrl:       entry.Image,
		VideoUrl:       entry.Video,
		VideoMediumUrl: entry.VideoMedium,
		VideoLowUrl:    entry.VideoLow,
		Blurhash:       entry.Blurhash,
		VideoWidth:     int(entry.VideoWidth),
		VideoHeight:    int(entry.VideoHeight),
		Autoplay:       entry.Autoplay == 1,
		MasterTemplate: entry.MasterTemplate,
		TemplateLevel:  entry.TemplateLevel,
		FreeTrial:      entry.FreeTrial == 1,
		LikeCount:      entry.LikeCount,
		BaseLikeCount:  entry.BaseLikeCount,
		ShareCount:     entry.ShareCount,
		Thirdparty:     entry.Thirdparty,
		UserInfo:       models.UserTemplateInfo{},
	}
	output.Success(c, resp)
}

func (feeds *Feeds) TagQuery(c *app.Context) {
	tagKey, ok := c.GetQuery("tag_key")
	if !ok {
		output.Error(c, output.ParamErrorCode, errors.New("generate_type is required"))
		return
	}
	var tag model.FeedTag
	err := rdb.Rdb.Model(&model.FeedTag{}).Where("tag_key = ?", tagKey).Select("tag_key, tag_name, tag_type").First(&tag).Error
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	var resp TagQueryResp
	resp.Tag = tag
	output.Success(c, resp)
}
