package feeds

import (
	"context"
	"io"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/jwtx"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"go.uber.org/zap"
)

type Home struct {
}

type (
	CategoryData struct {
		Title       string `json:"title"`
		CategoryTag string `json:"category_tag"`
	}
	HomepageTemplateShelf struct {
		Title    string               `json:"title"`
		ShelfTag string               `json:"shelf_tag"`
		Total    int64                `json:"total"`
		Entries  []*models.VideoEntry `json:"entries"`
	}

	HomeConfigReq struct {
		// ColumnTitleList []string `json:"column_title_list"`
	}

	HomeConfigRsp struct {
		ColumnTitleList    []CategoryData          `json:"category_tag_list"`
		TemplatefShelfList []HomepageTemplateShelf `json:"template_shelf_list,omitempty"`
	}
)

func GetUidFromAuth(ctx *app.Context) int64 {
	auth := ctx.GetGinContext().Request.Header.Get(app.Authorization)
	claim, err := jwtx.Parse(auth, jwtx.Access)
	if err != nil {
		return 0
	}
	return claim.AccountId
}

func (u *Home) HomeConfig(c *app.Context) {
	ctx := context.WithValue(context.Background(), logger.TraceIDKey, c.GetTraceID())
	var req HomeConfigReq
	err := c.ShouldBind(&req)
	if err != nil && err != io.EOF {
		logger.Error("HomeConfig", zap.Error(err))
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	uid := GetUidFromAuth(c)
	resp := &HomeConfigRsp{
		ColumnTitleList: []CategoryData{
			{
				Title:       bizconf.LangConf.Text(c.Lang, common.ShelfTrendingKey),
				CategoryTag: common.ShelfTrendingKey,
			},
			{
				Title:       bizconf.LangConf.Text(c.Lang, common.RecommendForYouKey),
				CategoryTag: common.RecommendForYouKey,
			},
		},
	}
	// 兼容非登录状态
	user := &model.User{
		UserLevel:  1,
		CreateTime: time.Now(),
	}
	if uid != 0 {
		user, err = common.GetUserInfo(uid)
		if err != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
	}
	categoryList, err := GetCategoryList(ctx, int(user.UserLevel))
	if err == nil {
		for _, category := range categoryList {
			resp.ColumnTitleList = append(resp.ColumnTitleList, CategoryData{
				Title:       category.TagName, // Todo 文案
				CategoryTag: category.TagKey,
			})
		}
	} else {
		logger.Error("HomeConfig", zap.Error(err))
	}
	// 货架推荐
	shelfTagList, err := GetUserShelfTagList(ctx, uid, int(user.UserLevel))
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	for _, shelfTag := range shelfTagList {
		generator := NewTemplateListGenerator(c, shelfTag.TagKey, int(user.UserLevel), user.CreateTime.After(time.Now().AddDate(0, 0, -1)))
		generateTypes, err := generator.GetTemplateNameList(ctx, &GetTemplateNameListReq{
			Start: 0,
			Size:  6,
		})
		if err != nil {
			output.Error(c, output.InternalErrorCode, err)
			return
		}
		entries, err := common.GetTemplateListByGenerateType(generateTypes.GenerateTypes, uid, c.Lang)
		if err != nil {
			logger.ErrorWithCtx(ctx, "TemplateList GetTemplateListByGenerateType", zap.Error(err))
			continue
		}
		total, _ := generator.GetTotal(ctx)
		entries = FilterTemplateByUserLevel(ctx, int(user.UserLevel), entries)
		if len(entries) > 0 {
			title := bizconf.LangConf.Text(c.Lang, shelfTag.TagKey)
			if title == "" {
				title = shelfTag.TagName
			}
			resp.TemplatefShelfList = append(resp.TemplatefShelfList, HomepageTemplateShelf{
				Title:    title,
				ShelfTag: shelfTag.TagKey,
				Entries:  entries,
				Total:    total,
			})
		}
	}
	output.Success(c, resp)
}
