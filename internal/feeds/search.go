package feeds

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"sort"
	"strconv"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	meilisearch "git.hoxigames.xyz/movely/movely-server/pkg/search"
	"go.uber.org/zap"
)

type (
	TemplateSearchReq struct {
		Content string `json:"content" binding:"required"`
	}

	TemplateSearchResp struct {
		Entries []*models.VideoEntry `json:"entries"`
	}
)

func (feeds *Feeds) TemplateSearch(c *app.Context) {
	req := &TemplateSearchReq{}
	if err := c.ShouldBind(req); err != nil {
		output.Error(c, 20001, err)
		return
	}
	datas, err := meilisearch.Meilisearch.SearchDocuments(req.Content, 20)
	if err != nil {
		output.Error(c, 20002, err)
		return
	}
	resp := &TemplateSearchResp{}
	if len(datas) == 0 {
		output.Success(c, resp)
		return
	}
	generateTypes := make([]string, 0)
	for _, data := range datas {
		generateTypes = append(generateTypes, data.GenerateType)
	}
	uid := middleware.GetAccountId(c)
	entries, err := common.GetTemplateListByGenerateType(generateTypes, uid, c.Lang)
	if err != nil {
		output.Error(c, 20002, err)
		return
	}
	resp.Entries = entries
	output.Success(c, resp)
}

func UpdateMeiliSearch() {
	ctx := context.Background()
	lock, err := rdb.RedisLock.Obtain(ctx, "movely:meilisearch_add_documents", time.Second*180, nil)
	if err != nil {
		logger.Warn("UpdateMeiliSearch Obtain lock err", zap.Error(err))
		return
	}
	defer lock.Release(ctx)

	startTime := time.Now()
	// 获取模板和 tag 数据
	var videos []model.ConfigsVideo
	rdb.Rdb.Model(&model.ConfigsVideo{}).Where("status = 1").Find(&videos)

	// Todo 标签

	// 获取 hash 数据
	hashKey := "movely:meilisearch_documents_hash"
	hashMap, _ := rdb.Redis.HGetAll(ctx, hashKey).Result()
	if len(hashMap) == 0 {
		hashMap = make(map[string]string)
	}

	// 构建差异文档)
	addDocuments := make([]map[string]any, 0)
	for _, video := range videos {
		langMap := bizconf.LangConf.TextAll(video.Title)
		title_i18n := make([]string, 0)
		for _, value := range langMap {
			if value != "" {
				title_i18n = append(title_i18n, value)
			}
		}
		sort.Strings(title_i18n)
		// sort.Strings(videoTagsMap[video.GenerateType])
		hashBytes := md5.Sum([]byte(video.GenerateType + video.Prompt + utils.ToJsonString(title_i18n)))
		hashValue := hex.EncodeToString(hashBytes[:])
		if hashMap[video.GenerateType] == hashValue {
			continue
		}
		doc := map[string]any{
			"id":            video.ID,
			"generate_type": video.GenerateType,
			"prompt":        video.Prompt,
			"title_i18n":    title_i18n,
			// "tags":          videoTagsMap[video.GenerateType],
			"cover_image": video.Image,
			"_vectors": map[string]any{
				"default": map[string]any{
					"regenerate": true,
				},
			},
		}
		addDocuments = append(addDocuments, doc)
		hashMap[video.GenerateType] = hashValue
	}
	if len(addDocuments) == 0 {
		logger.Info("Meilisearch add documents no change", zap.String("cost_time", time.Since(startTime).String()), zap.Int("videos_count", len(videos)))
		return
	}

	// 更新 hash 数据
	rdb.Redis.HMSet(ctx, hashKey, hashMap).Result()
	// 添加文档
	task, err := meilisearch.Meilisearch.AddDocuments(addDocuments)
	if err != nil {
		logger.Error("Meilisearch add documents error", zap.Error(err))
		return
	}
	result := map[string]any{
		"task_uid":            task.TaskUID,
		"cost_time":           time.Since(startTime).String(),
		"videos_count":        len(videos),
		"add_documents_count": len(addDocuments),
		"hash_map_count":      len(hashMap),
		// "add_video_type":      addTypes,
	}

	logger.Info("Meilisearch add documents success", zap.Any("result", result))

	videos = []model.ConfigsVideo{}
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("status <> 1").Find(&videos).Error
	if err != nil {
		logger.Warn("Meilisearch add documents db error", zap.Error(err))
		return
	}
	if len(videos) == 0 {
		return
	}
	generates := []string{}
	ids := make([]string, 0)
	for _, video := range videos {
		generates = append(generates, video.GenerateType)
		ids = append(ids, strconv.FormatInt(video.ID, 10))
	}
	rdb.Redis.HDel(ctx, hashKey, generates...).Result()
	task, err = meilisearch.Meilisearch.DeleteDocuments(ids)
	if err != nil {
		logger.Warn("Meilisearch del invalid documents error", zap.Error(err))
		return
	} else {
		logger.Info("Meilisearch del invalid documents success", zap.Int("documents_count", len(ids)), zap.Int64("task_uid", task.TaskUID))
	}

}
