package feeds

import (
	"context"

	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/internal/middleware"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
)

type HotMovesRecommend struct {
	c                  *app.Context
	userLevel          int
	isNewUser          bool
	isGeneratedHotMove bool
}

func NewHotMovesRecommend(c *app.Context, userLevel int, isNewUser, isGeneratedHotMove bool) *HotMovesRecommend {
	return &HotMovesRecommend{
		c:                  c,
		userLevel:          userLevel,
		isNewUser:          isNewUser,
		isGeneratedHotMove: isGeneratedHotMove,
	}
}

func (r *HotMovesRecommend) GetMergeList(ctx context.Context, start int64, size int64) ([]string, int64, error) {
	var res []string
	newAllTemps, err := rc.GetNewTemplatesByTagAneLevel(common.ShelfHotMovesKey, GetVisLevelByUserLevel(r.userLevel))
	if err != nil {
		return nil, 0, err
	}
	newTempsNum := int64(len(newAllTemps))
	var newTemps []string
	var shelfTemps []string
	// index位置超过新模版的数量 全部取基础列表
	if start >= int64(newTempsNum) {
		shelfTemps, err = GetTemplateListByShelfTag(ctx, r.userLevel, common.ShelfHotMovesKey, start, start+size)
	} else {
		shelfTemps, err = GetTemplateListByShelfTag(ctx, r.userLevel, common.ShelfHotMovesKey, start, start+size/2)
		newTemps = GetTemplateNameList(newAllTemps, start, start+size/2)
	}
	if err != nil {
		return nil, 0, err
	}
	var index int = 0
	for _, temp := range shelfTemps {
		res = append(res, temp)
		// 间隔插入新模版
		if index < len(newTemps) {
			res = append(res, newTemps[index])
			index++
		}
	}

	return res, int64(len(shelfTemps)), nil
}

func (r *HotMovesRecommend) GetHotModveMergeList(ctx context.Context, start int64, size int64) ([]string, int64, error) {
	var res []string
	newAllTemps, err := rc.GetNewTemplatesByTagAneLevel(common.ShelfHotMovesKey, GetVisLevelByUserLevel(r.userLevel))
	if err != nil {
		return nil, 0, err
	}
	newTempsNum := int64(len(newAllTemps))
	var newTemps []string
	var shelfTemps []string
	uid := middleware.GetAccountId(r.c)
	// index位置超过新模版的数量 全部取基础列表
	if start >= int64(newTempsNum) {
		shelfTemps, err = GetHotMovesTemplateList(ctx, UserMoveInterestScorePre, uid, start, start+size)
	} else {
		shelfTemps, err = GetHotMovesTemplateList(ctx, UserMoveInterestScorePre, uid, start, start+size/2)
		newTemps = GetTemplateNameList(newAllTemps, start, start+size/2)
	}
	if err != nil {
		return nil, 0, err
	}
	var index int = 0
	for _, temp := range shelfTemps {
		res = append(res, temp)
		// 间隔插入新模版
		if index < len(newTemps) {
			res = append(res, newTemps[index])
			index++
		}
	}

	return res, int64(len(shelfTemps)), nil
}

func (r *HotMovesRecommend) GetTemplateNameList(ctx context.Context, req *GetTemplateNameListReq) (*GetTemplateNameListRsp, error) {
	var err error
	var generateTypes []string
	generateTypes, err = rc.GetTopTemplatesByTagAneLevel(common.ShelfHotMovesKey, GetVisLevelByUserLevel(r.userLevel))
	if err != nil {
		return nil, err
	}
	var res []string
	res = GetTemplateNameList(generateTypes, req.Start, req.Start+req.Size)
	// 置顶足够
	if len(res) >= int(req.Size) {
		return &GetTemplateNameListRsp{
			GenerateTypes: res,
			NextIndex:     req.Start + req.Size,
		}, nil
	}
	topNum := int64(len(generateTypes))
	start := req.Start
	size := req.Size - int64(len(res))
	if start >= int64(topNum) {
		// 超过置顶
		start -= int64(topNum)
	} else {
		// 置顶剩余不够
		start = 0
	}
	nextIndex := req.Start
	var temps []string
	var num int64
	if r.isNewUser {
		temps, err = GetTemplateListByShelfTag(ctx, r.userLevel, common.ShelfHotMovesKey, start, start+size-1)
		nextIndex += int64(len(temps))
	} else if !r.isGeneratedHotMove {
		temps, num, err = r.GetMergeList(ctx, start, size)
		nextIndex += num
	} else {
		// 有生成过hotmove模版的老用户
		temps, num, err = r.GetHotModveMergeList(ctx, start, size)
		nextIndex += num
	}
	if err != nil {
		return nil, err
	}
	res = append(res, temps...)
	return &GetTemplateNameListRsp{
		GenerateTypes: res,
		NextIndex:     nextIndex,
	}, nil
}

func (trending *HotMovesRecommend) GetTotal(ctx context.Context) (int64, error) {
	return GetTemplateNumByShelfTag(ctx, trending.userLevel, common.ShelfHotMovesKey)
}
