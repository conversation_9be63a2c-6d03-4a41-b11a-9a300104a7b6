package feeds

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"sync/atomic"
	"time"
	"unsafe"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/global"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"github.com/bsm/redislock"
	"github.com/google/uuid"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/allegro/bigcache/v3"
)

const (
	// 基础数据
	L1PopScoreL12 = "movely:l1_pop_score_l12"
	L2PopScoreL12 = "movely:l2_pop_score_l12"
	L1PopScoreL34 = "movely:l1_pop_score_l34"
	L2PopScoreL34 = "movely:l2_pop_score_l34"
	L3PopScoreL34 = "movely:l3_pop_score_l34"
	L4PopScoreL34 = "movely:l4_pop_score_l34"

	// 由基础数据再生成具体分类
	TagScoreL12Pre = "movely:recommend:l12:" // 从PopScoreL12区分tag
	TagScoreL3Pre  = "movely:recommend:l3:"  // 从PopScoreL34区分tag并过滤掉L4模版
	TagScoreL4Pre  = "movely:recommend:l4:"  // 从PopScoreL34区分tag

	UserMoveInterestL3ScorePre = "movely:recommend:l3:{move_interest_score}:" // 每个用户move_interest_score分 只有Hot Moves货架
	UserMoveInterestL4ScorePre = "movely:recommend:l4:{move_interest_score}:" // 每个用户move_interest_score分 只有Hot Moves货架
	UserMoveInterestScorePre   = "movely:recommend:{move_interest_score}:"    // 每个用户move_interest_score分 只有Hot Moves货架

	// 首页货架排序
	UserShelfTagScorePre = "movely:recommend:{user_shelf_tag_score}:"
)

const (
	ScoreGroupL12 = "L12_"
	ScoreGroupL34 = "L34_"
)

func GetTemplateListByShelfTag(ctx context.Context, userLevel int, shelf string, start, stop int64) ([]string, error) {
	var key string
	switch userLevel {
	case 1:
		fallthrough
	case 2:
		key = TagScoreL12Pre
	case 3:
		key = TagScoreL3Pre
	case 4:
		key = TagScoreL4Pre
	}
	key = key + shelf
	rdb.Redis.Expire(ctx, key, 7*24*time.Hour)
	return rdb.Redis.ZRevRange(ctx, key, start, stop-1).Result()
}

func GetTemplateNumByShelfTag(ctx context.Context, userLevel int, shelf string) (int64, error) {
	var key string
	switch userLevel {
	case 1:
		fallthrough
	case 2:
		key = TagScoreL12Pre
	case 3:
		key = TagScoreL3Pre
	case 4:
		key = TagScoreL4Pre
	}
	key = key + shelf
	rdb.Redis.Expire(ctx, key, 7*24*time.Hour)
	return rdb.Redis.ZCard(ctx, key).Result()
}

func DelTemplateListByShelfTag(ctx context.Context, userLevel int, shelf, generateType string) error {
	var key string
	switch userLevel {
	case 1:
		fallthrough
	case 2:
		key = TagScoreL12Pre
	case 3:
		key = TagScoreL3Pre
	case 4:
		key = TagScoreL4Pre
	}
	key = key + shelf
	_, err := rdb.Redis.ZRem(ctx, key, generateType).Result()
	return err
}

func GetHotMovesTemplateList(ctx context.Context, keypre string, uid int64, start, stop int64) ([]string, error) {
	key := keypre + strconv.FormatInt(uid, 10)
	isExist, err := rdb.Redis.Exists(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	if isExist == 0 {
		var templateTags []*model.TemplateTag = make([]*model.TemplateTag, 0, stop-start+1)
		err = rdb.Rdb.Model(&model.TemplateTag{}).Where("tag_key = ?", common.ShelfHotMovesKey).Offset(int(start)).Limit(int(stop - start + 1)).Find(&templateTags).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		var templateList []string
		for _, templateTag := range templateTags {
			templateList = append(templateList, templateTag.GenerateType)
		}
		return templateList, nil
	} else {
		return rdb.Redis.ZRevRange(ctx, key, start, stop-1).Result()
	}
}

func FilterTemplateByUserLevel(ctx context.Context, userLevel int, templates []*models.VideoEntry) []*models.VideoEntry {
	if userLevel == 4 {
		return templates
	}
	newtemplate := make([]*models.VideoEntry, 0)
	for _, template := range templates {
		switch userLevel {
		case 1:
			fallthrough
		case 2:
			if template.TemplateLevel == 1 || template.TemplateLevel == 2 {
				newtemplate = append(newtemplate, template)
			}
		case 3:
			if template.TemplateLevel <= 3 {
				newtemplate = append(newtemplate, template)
			}
		}
	}
	return newtemplate
}

func GetVisLevelByUserLevel(userLevel int) []int32 {
	switch userLevel {
	case 1:
		fallthrough
	case 2:
		return []int32{1, 2}
	case 3:
		return []int32{1, 2, 3}
	case 4:
		return []int32{1, 2, 3, 4}
	}
	return nil
}

func GetTemplateNameList(templateList []string, start, stop int64) []string {
	if start >= stop {
		return nil
	}
	num := int64(len(templateList))
	if start >= num {
		return nil
	}
	if stop > num {
		stop = num
	}
	return templateList[start:stop]
}

func GetLoopTemplateNameList(templateList []string, start, stop int64) []string {
	if start >= stop {
		return nil
	}
	num := int64(len(templateList))
	start = start % num
	stop = stop % num
	if start >= num {
		return nil
	}
	if stop > num {
		stop = num
	}
	return templateList[start:stop]
}

func GetForYouTemplateList(ctx context.Context, uid int64, userLevel int, start, stop int64) ([]string, error) {
	key := UserMoveInterestScorePre + strconv.FormatInt(uid, 10)
	return rdb.Redis.ZRevRange(ctx, key, start, stop-1).Result()
}

func GetL1PopScoreL12List(ctx context.Context, start, stop int64) ([]string, error) {
	key := L1PopScoreL12
	return rdb.Redis.ZRevRange(ctx, key, start, stop-1).Result()
}

func GetL2PopScoreL12List(ctx context.Context, start, stop int64) ([]string, error) {
	key := L2PopScoreL12
	return rdb.Redis.ZRevRange(ctx, key, start, stop-1).Result()
}

// 用户货架推荐排序
func GetUserShelfTagList(ctx context.Context, uid int64, userLevel int) ([]*model.FeedTag, error) {
	var res []*model.FeedTag = make([]*model.FeedTag, 0)
	tagList := make([]*model.FeedTag, 0)
	err := rdb.Rdb.Model(&model.FeedTag{}).Where("tag_type = ? and status = 1 and tag_level <= ?", common.TagTypeShelf, userLevel).Find(&tagList).Error
	if err != nil {
		return nil, err
	}
	unique := make(map[string]struct{})
	tagMap := make(map[string]*model.FeedTag)
	for _, tag := range tagList {
		tagMap[tag.TagKey] = tag
		if tag.Top > 0 {
			// 置顶数据
			unique[tag.TagKey] = struct{}{}
			res = append(res, tag)
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].Top < res[j].Top
	})

	key := UserShelfTagScorePre + strconv.FormatInt(uid, 10)
	isExist, err := rdb.Redis.Exists(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	if isExist > 0 {
		tagKeys, err := rdb.Redis.ZRevRange(ctx, key, 0, -1).Result()
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		for _, tagKey := range tagKeys {
			if _, ok := unique[tagKey]; ok {
				continue
			}
			// 其他数据
			if tag, ok := tagMap[tagKey]; ok {
				res = append(res, tag)
			}
		}
	} else {
		for _, tag := range tagList {
			// 其他数据
			if _, ok := unique[tag.TagKey]; !ok {
				res = append(res, tag)
			}
		}
	}

	return res, nil
}

func GetCategoryList(ctx context.Context, userLevel int) ([]*model.FeedTag, error) {
	var res []*model.FeedTag = make([]*model.FeedTag, 0)
	tagList := make([]*model.FeedTag, 0)
	err := rdb.Rdb.Model(&model.FeedTag{}).Where("tag_type = ? and status = 1 and tag_level <= ?", common.TagTypeCategory, userLevel).Find(&tagList).Error
	if err != nil {
		return nil, err
	}
	unique := make(map[string]struct{})
	tagMap := make(map[string]*model.FeedTag)
	for _, tag := range tagList {
		tagMap[tag.TagKey] = tag
		if tag.Top > 0 {
			// 置顶数据
			unique[tag.TagKey] = struct{}{}
			res = append(res, tag)
		}
	}
	for _, tag := range tagList {
		// 其他数据
		if _, ok := unique[tag.TagKey]; !ok {
			res = append(res, tag)
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].Top < res[j].Top
	})
	return res, nil
}

// 生成
func cleanTemplateScore(ctx context.Context, generateType string) {
	keys := []string{
		L1PopScoreL12,
		L2PopScoreL12,
		L1PopScoreL34,
		L2PopScoreL34,
		L3PopScoreL34,
		L4PopScoreL34,
	}
	for _, key := range keys {
		_, err := rdb.Redis.ZRem(ctx, key, generateType).Result()
		if err != nil && err != redis.Nil {
			logger.Error("cleanTemplateScore ZRem failed", zap.Error(err))
		}
	}
}

type TemplateTotalData struct {
	MaxGenNum, MinGenNum         atomic.Int64
	MaxGenUserNum, MinGenUserNum atomic.Int64
}

type RecommendCalc struct {
	ctx               context.Context
	pool              *ants.Pool
	cache             *bigcache.BigCache
	l12               TemplateTotalData
	l34               TemplateTotalData
	tagNewTemplateMap atomic.Pointer[map[string][]*model.ConfigsVideo]
	tagTopTemplateMap atomic.Pointer[map[string][]*model.ConfigsVideo]
}

type TemplateAnalysisData struct {
	GenNum       int     `json:"gen_num"`
	GenUserNum   int     `json:"gen_user_num"`
	CTR          float64 `json:"ctr"`
	CVR          float64 `json:"cvr"`
	StoreUserNum int     `json:"store_user_num"`
	ShareUserNum int     `json:"share_user_num"`
	LikeUserNum  int     `json:"like_user_num"`
}

func NewRecommendCalc(ctx context.Context) *RecommendCalc {
	pool, err := ants.NewPool(100)
	if err != nil {
		logger.Error("NewRecommendCalc NewPool failed", zap.Error(err))
	}
	var info TemplateAnalysisData
	cache, err := bigcache.New(ctx, bigcache.Config{
		Shards:             128,
		LifeWindow:         24 * time.Hour,
		CleanWindow:        time.Hour,
		MaxEntriesInWindow: 1000,
		MaxEntrySize:       int(unsafe.Sizeof(info)),
		StatsEnabled:       false,
		Verbose:            false,
		Hasher:             nil,
		HardMaxCacheSize:   1024,
		Logger:             nil,
	})
	if err != nil {
		logger.Error("NewRecommendCalc NewBigCache failed", zap.Error(err))
	}
	return &RecommendCalc{
		ctx:   ctx,
		pool:  pool,
		cache: cache,
	}
}

func (rc *RecommendCalc) generateTemplateUserScore(template *model.ConfigsVideo) error {
	caculFunc := func(key string, total *TemplateTotalData) float64 {
		data, err := rc.GetTemplateAnalysisData(key)
		if err != nil {
			logger.ErrorWithCtx(rc.ctx, "generateTemplateUserScore Get failed", zap.Error(err), zap.String("key", key))
			return 0
		}
		minGenNum := total.MinGenNum.Load()
		genNumScore := float64(data.GenNum-int(minGenNum)) / float64(total.MaxGenNum.Load()-minGenNum)
		minGenUserNum := total.MinGenUserNum.Load()
		genUserNumScore := float64(data.GenNum-int(minGenUserNum)) / float64(total.MaxGenUserNum.Load()-minGenUserNum)
		if genNumScore < 0 || genUserNumScore < 0 {
			logger.ErrorWithCtx(rc.ctx, "generateTemplateUserScore score err", zap.String("key", key),
				zap.Float64("genNumScore", genNumScore), zap.Float64("genUserNumScore", genUserNumScore))
			return 0
		}
		var score float64
		score = 0.2*genNumScore + 0.2*genUserNumScore + 0.1*data.CTR + 0.1*data.CVR
		score += 0.15 * float64(data.StoreUserNum) / float64(data.GenUserNum)
		score += 0.1 * float64(data.LikeUserNum) / float64(data.GenUserNum)
		score += 0.15 * float64(data.ShareUserNum) / float64(data.GenUserNum)
		// logger.InfoWithCtx(rc.ctx, "generateTemplateUserScore", zap.String("key", key), zap.Float64("score", score))
		return score
	}
	var score, scoreL34 float64
	score = caculFunc(ScoreGroupL12+template.GenerateType, &rc.l12)
	if template.TemplateLevel >= 3 {
		scoreL34 = caculFunc(ScoreGroupL34+template.GenerateType, &rc.l34)
	}
	switch template.TemplateLevel {
	case 1:
		InsertScore(rc.ctx, L1PopScoreL12, template.GenerateType, score)
		InsertScore(rc.ctx, L1PopScoreL34, template.GenerateType, scoreL34)
	case 2:
		InsertScore(rc.ctx, L2PopScoreL12, template.GenerateType, score)
		InsertScore(rc.ctx, L2PopScoreL34, template.GenerateType, scoreL34)
	case 3:
		InsertScore(rc.ctx, L1PopScoreL34, template.GenerateType, scoreL34)
	case 4:
		InsertScore(rc.ctx, L1PopScoreL34, template.GenerateType, scoreL34)
	}
	switch template.TemplateLevel {
	case 1:
		fallthrough
	case 2:
		fallthrough
	case 3:
		InsertScore(rc.ctx, TagScoreL3Pre+common.RecommendForYouKey, template.GenerateType, scoreL34)
		fallthrough
	case 4:
		InsertScore(rc.ctx, TagScoreL4Pre+common.RecommendForYouKey, template.GenerateType, scoreL34)
	}
	var templateTags []*model.TemplateTag
	err := rdb.Rdb.Model(&model.TemplateTag{}).Where("generate_type = ?", template.GenerateType).Select("tag_key").Find(&templateTags).Error
	if err != nil {
		logger.ErrorWithCtx(rc.ctx, "generateTemplateUserScore Get failed", zap.Error(err))
	} else {
		tagKeys := make([]string, 0)
		for _, tag := range templateTags {
			tagKeys = append(tagKeys, tag.TagKey)
		}
		if len(tagKeys) == 0 {
			return nil
		}
		tags := make([]*model.FeedTag, 0)
		err = rdb.Rdb.Model(&model.FeedTag{}).Where("tag_key in ?", tagKeys).Find(&tags).Error
		if err != nil {
			return err
		}
		for _, tag := range templateTags {
			// 过滤不生效和不展示的tag
			for _, tag := range tags {
				if tag.TagType == common.TagTypeFetish || tag.Status != 1 {
					continue
				}
			}
			switch template.TemplateLevel {
			case 1:
				fallthrough
			case 2:
				InsertScore(rc.ctx, TagScoreL12Pre+tag.TagKey, template.GenerateType, score)
				fallthrough
			case 3:
				InsertScore(rc.ctx, TagScoreL3Pre+tag.TagKey, template.GenerateType, scoreL34)
				fallthrough
			case 4:
				InsertScore(rc.ctx, TagScoreL4Pre+tag.TagKey, template.GenerateType, scoreL34)
			}
		}
	}
	return nil
}

func moveInterestScoreReName(ctx context.Context, key string) {
	_, err := rdb.Redis.Rename(ctx, key+"tmp", key).Result()
	if err != nil {
		logger.ErrorWithCtx(ctx, "moveInterestScoreReName Rename failed", zap.Error(err), zap.String("key", key))
	}
	rdb.Redis.Expire(ctx, key, 7*24*time.Hour).Result()
}

func (rc *RecommendCalc) generateUserInterestScore(templates []*model.ConfigsVideo, tags []*model.FeedTag, user *model.User) error {
	// Todo 数数数据分析 用户等级区分
	key := UserMoveInterestScorePre + strconv.Itoa(int(user.UID))
	tmpkey := key + "tmp"
	minGenNum := 1000000
	maxGenNum := 0
	mapTagData := map[string]TemplateAnalysisData{}
	for _, tag := range tags {
		genNum := 100 + rand.Intn(10000)
		mapTagData[tag.TagKey] = TemplateAnalysisData{
			GenNum:       genNum,
			CTR:          rand.Float64(),
			CVR:          rand.Float64(),
			StoreUserNum: rand.Intn(genNum),
			ShareUserNum: rand.Intn(genNum),
			LikeUserNum:  rand.Intn(genNum),
		}
		if genNum < minGenNum {
			minGenNum = genNum
		}
		if genNum > maxGenNum {
			maxGenNum = genNum
		}

	}
	for _, template := range templates {
		tags := make([]*model.FeedTag, 0)
		err := rdb.Rdb.Raw("select * from feed_tag where tag_type = ? and status = 1 and tag_key in (select tag_key from template_tags where generate_type = ?)", common.TagTypeFetish, "video_gen_sexy_lady").Find(&tags).Error
		if err != nil || len(tags) == 0 {
			continue
		}
		data, ok := mapTagData[tags[0].TagKey]
		if !ok {
			continue
		}
		genNumScore := float64(data.GenNum-int(minGenNum)) / float64(maxGenNum-minGenNum)
		var fetishScore float64
		fetishScore = 0.4*genNumScore + 0.1*data.CTR + 0.1*data.CVR
		fetishScore += 0.15 * float64(data.StoreUserNum) / float64(data.GenNum)
		fetishScore += 0.15 * float64(data.LikeUserNum) / float64(data.GenNum)
		fetishScore += 0.1 * float64(data.ShareUserNum) / float64(data.GenNum)
		// 用户等级区分
		popscore, err := GetScoreByLevel(rc.ctx, template.GenerateType, int(user.UserLevel), template.TemplateLevel)
		if err != nil {
			logger.ErrorWithCtx(rc.ctx, "generateUserInterestScore GetScoreByLevel failed", zap.Error(err))
			continue
		}
		score := 0.7*fetishScore + 0.3*popscore
		InsertScore(rc.ctx, tmpkey, template.GenerateType, score)
		logger.InfoWithCtx(rc.ctx, "generateUserInterestScore", zap.String("key", tmpkey), zap.String("fetish_tag", tags[0].TagKey),
			zap.Float64("score", score), zap.Float64("fetishScore", fetishScore), zap.Float64("popscore", popscore))
		switch template.TemplateLevel {
		case 3:
			InsertScore(rc.ctx, UserMoveInterestL3ScorePre+strconv.Itoa(int(user.UID))+"tmp", template.GenerateType, score)
		case 4:
			InsertScore(rc.ctx, UserMoveInterestL4ScorePre+strconv.Itoa(int(user.UID))+"tmp", template.GenerateType, score)
		}
	}
	moveInterestScoreReName(rc.ctx, key)
	moveInterestScoreReName(rc.ctx, UserMoveInterestL3ScorePre+strconv.Itoa(int(user.UID)))
	moveInterestScoreReName(rc.ctx, UserMoveInterestL4ScorePre+strconv.Itoa(int(user.UID)))
	return nil
}

func (rc *RecommendCalc) generateUserShelfScore(tags []*model.FeedTag, user *model.User) error {
	// Todo 数数数据分析 用户等级区分
	type UserShelfAnalysisData struct {
		GenNums         []float64
		GenTemplateNums []float64
		ClickNums       []float64
		StoreNums       []float64
		ShareNums       []float64
		LikeNums        []float64
	}
	var data UserShelfAnalysisData
	key := UserShelfTagScorePre + strconv.Itoa(int(user.UID))
	tmpkey := UserShelfTagScorePre + strconv.Itoa(int(user.UID)) + "tmp"
	for _, tag := range tags {
		fmt.Println(tag.TagKey)
		genNum := 100 + rand.Intn(10000)
		data.GenNums = append(data.GenNums, float64(genNum))
		data.GenTemplateNums = append(data.GenTemplateNums, float64(rand.Intn(genNum)))
		data.ClickNums = append(data.ClickNums, float64(rand.Intn(genNum)))
		data.StoreNums = append(data.StoreNums, float64(rand.Intn(genNum)/genNum))
		data.ShareNums = append(data.ShareNums, float64(rand.Intn(genNum)/genNum))
		data.LikeNums = append(data.LikeNums, float64(rand.Intn(genNum)/genNum))
	}
	data.GenNums = common.Normalization(data.GenNums)
	data.GenTemplateNums = common.Normalization(data.GenTemplateNums)
	data.ClickNums = common.Normalization(data.ClickNums)
	for i, tag := range tags {
		var score float64
		score = 0.2*data.GenNums[i] + 0.2*data.GenTemplateNums[i] + 0.15*data.ClickNums[i]
		score += 0.15*data.StoreNums[i] + 0.15*data.ShareNums[i] + 0.15*data.LikeNums[i]
		InsertScore(rc.ctx, tmpkey, tag.TagKey, score)
		rdb.Redis.Expire(rc.ctx, tmpkey, 7*24*time.Hour)
		logger.InfoWithCtx(rc.ctx, "generateUserShelfScore", zap.String("key", tmpkey),
			zap.Float64("score", score), zap.Float64("score", score))
	}
	_, err := rdb.Redis.Rename(rc.ctx, tmpkey, key).Result()
	rdb.Redis.Expire(rc.ctx, key, 7*24*time.Hour).Result()
	if err != nil {
		logger.ErrorWithCtx(rc.ctx, "generateUserShelfScore Rename failed", zap.Error(err))
	}
	return nil
}

func (rc *RecommendCalc) generateTemplateData(template *model.ConfigsVideo) error {
	if template.Status != 1 {
		rc.cache.Delete(ScoreGroupL12 + template.GenerateType)
		if template.TemplateLevel >= 3 {
			rc.cache.Delete(ScoreGroupL34 + template.GenerateType)
		}
		return nil
	}
	// Todo 从数数分析数据 先使用随机
	genNum := 100 + rand.Intn(10000)
	genUserNum := 1 + rand.Intn(genNum)
	l12data := TemplateAnalysisData{
		GenNum:       genNum,
		GenUserNum:   rand.Intn(genNum),
		CTR:          rand.Float64(),
		CVR:          rand.Float64(),
		StoreUserNum: rand.Intn(genUserNum),
		ShareUserNum: rand.Intn(genUserNum),
		LikeUserNum:  rand.Intn(genUserNum),
	}
	compareFunc := func(num *atomic.Int64, target int64, ismin bool) {
		cur := num.Load()
		succ := false
		for ((ismin && target < cur) || (!ismin && target > cur)) && !succ {
			succ = num.CompareAndSwap(cur, target)
			cur = num.Load()
		}
	}
	compareFunc(&rc.l12.MaxGenNum, int64(genNum), false)
	compareFunc(&rc.l12.MaxGenUserNum, int64(genUserNum), false)
	compareFunc(&rc.l12.MinGenNum, int64(genNum), true)
	compareFunc(&rc.l12.MinGenUserNum, int64(genUserNum), true)
	rc.SetTemplateAnalysisData(ScoreGroupL12+template.GenerateType, &l12data)
	if template.TemplateLevel >= 3 {
		genNum = 100 + rand.Intn(10000)
		genUserNum = 1 + rand.Intn(genNum)
		l34data := TemplateAnalysisData{
			GenNum:       genNum,
			GenUserNum:   rand.Intn(genNum),
			CTR:          rand.Float64(),
			CVR:          rand.Float64(),
			StoreUserNum: rand.Intn(genUserNum),
			ShareUserNum: rand.Intn(genUserNum),
			LikeUserNum:  rand.Intn(genUserNum),
		}
		compareFunc(&rc.l34.MaxGenNum, int64(genNum), false)
		compareFunc(&rc.l34.MaxGenUserNum, int64(genUserNum), false)
		compareFunc(&rc.l34.MinGenNum, int64(genNum), true)
		compareFunc(&rc.l34.MinGenUserNum, int64(genUserNum), true)
		rc.SetTemplateAnalysisData(ScoreGroupL34+template.GenerateType, &l34data)
	}
	return nil
}

func (rc *RecommendCalc) GetTemplateAnalysisData(key string) (TemplateAnalysisData, error) {
	var info TemplateAnalysisData
	data, err := rc.cache.Get(key)
	if err != nil {
		return info, err
	}

	err = json.Unmarshal(data, &info)
	if err != nil {
		return info, err
	}
	return info, nil
}

func (rc *RecommendCalc) SetTemplateAnalysisData(key string, info *TemplateAnalysisData) error {
	data, err := json.Marshal(info)
	if err != nil {
		return err
	}
	return rc.cache.Set(key, data)
}

func (rc *RecommendCalc) DelTemplateAnalysisData(key string, info *TemplateAnalysisData) error {
	return rc.cache.Delete(key)
}

func (rc *RecommendCalc) GenerateRecommendTemplateScore(ctx context.Context) error {
	var videoEntries []*model.ConfigsVideo
	err := rdb.Rdb.Model(&model.ConfigsVideo{}).Find(&videoEntries).Error
	if err != nil {
		return err
	}
	// Todo 获取最近7天活跃用户
	users := make([]*model.User, 0)
	err = rdb.Rdb.Model(&model.User{}).Find(&users).Error
	if err != nil {
		return err
	}
	// 重置数据
	const MinGenNum = 1000000
	rc.l12 = TemplateTotalData{}
	rc.l12.MinGenNum.Store(MinGenNum)
	rc.l12.MinGenUserNum.Store(MinGenNum)
	rc.l34 = TemplateTotalData{}
	rc.l34.MinGenNum.Store(MinGenNum)
	rc.l34.MinGenUserNum.Store(MinGenNum)
	// 分析基础数据
	for _, videoEntry := range videoEntries {
		if videoEntry.Status == -1 {
			cleanTemplateScore(ctx, videoEntry.GenerateType)
		} else {
			rc.pool.Submit(func() {
				err := rc.generateTemplateData(videoEntry)
				if err != nil {
					logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore generateTemplateData failed", zap.String("generateType", videoEntry.GenerateType), zap.Error(err))
				}
			})
		}

	}
	for rc.pool.Running() > 0 {
		time.Sleep(5 * time.Second)
	}
	// 计算pop_score_l12 pop_score_l34
	for _, videoEntry := range videoEntries {
		if videoEntry.Status != 1 {
			continue
		}
		rc.pool.Submit(func() {
			err := rc.generateTemplateUserScore(videoEntry)
			if err != nil {
				logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore generateTemplateUserScore failed", zap.String("generateType", videoEntry.GenerateType), zap.Error(err))
			}
		})
	}
	for rc.pool.Running() > 0 {
		time.Sleep(5 * time.Second)
	}
	// 计算move_interest_score 只有hot move标签才计算
	var templateTags []*model.TemplateTag
	hotmoveTemplateMap := make(map[string]struct{})
	err = rdb.Rdb.Model(&model.TemplateTag{}).Where("tag_key = ?", common.ShelfHotMovesKey).Select("generate_type").Find(&templateTags).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore get tags failed", zap.Error(err))
	} else {
		for _, tag := range templateTags {
			hotmoveTemplateMap[tag.GenerateType] = struct{}{}
		}
	}
	targetTemplates := []*model.ConfigsVideo{}
	for _, template := range videoEntries {
		if _, ok := hotmoveTemplateMap[template.GenerateType]; !ok {
			continue
		}
		targetTemplates = append(targetTemplates, template)
	}
	var fetishTags []*model.FeedTag
	err = rdb.Rdb.Model(&model.FeedTag{}).Where("tag_type = ?", common.TagTypeFetish).Find(&fetishTags).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore get tags failed", zap.Error(err))
	} else {
		for _, user := range users {
			rc.pool.Submit(func() {
				err := rc.generateUserInterestScore(targetTemplates, fetishTags, user)
				if err != nil {
					logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore generateUserInterestScore failed", zap.Error(err))
				}
			})
		}
	}
	var feedTags []*model.FeedTag
	err = rdb.Rdb.Model(&model.FeedTag{}).Where("tag_type = ?", common.TagTypeShelf).Find(&feedTags).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore get tags failed", zap.Error(err))
	} else {
		for _, user := range users {
			rc.pool.Submit(func() {
				err := rc.generateUserShelfScore(feedTags, user)
				if err != nil {
					logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore generateUserShelfScore failed", zap.Error(err))
				}
			})
		}
	}
	for rc.pool.Running() > 0 {
		time.Sleep(time.Second)
	}

	return nil
}

func (rc *RecommendCalc) generateNewTemplateList(ctx context.Context) {
	videoEntries := make([]*model.ConfigsVideo, 0)
	err := rdb.Rdb.Model(&model.ConfigsVideo{}).Order("id desc").Limit(100).Where("create_time > ?", time.Now().AddDate(0, 0, -1)).Find(&videoEntries).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "generateNewTemplateList get videoEntries failed", zap.Error(err))
		return
	}
	if len(videoEntries) == 0 {
		return
	}
	templateMaps := make(map[string]*model.ConfigsVideo, 0)
	generateTypes := make([]string, 0)
	for _, videoEntry := range videoEntries {
		generateTypes = append(generateTypes, videoEntry.GenerateType)
		templateMaps[videoEntry.GenerateType] = videoEntry
	}
	var templateTags []*model.TemplateTag
	err = rdb.Rdb.Model(&model.TemplateTag{}).Where("generate_type in ?", generateTypes).Find(&templateTags).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "generateNewTemplateList get templateTags failed", zap.Error(err))
		return
	}
	tagNewTemplateMap := make(map[string][]*model.ConfigsVideo)
	for _, templateTag := range templateTags {
		tagNewTemplateMap[templateTag.TagKey] = append(tagNewTemplateMap[templateTag.TagKey], templateMaps[templateTag.GenerateType])
	}
	rc.tagNewTemplateMap.Store(&tagNewTemplateMap)
}

func (rc *RecommendCalc) generateTopTemplateList(ctx context.Context) {
	var templateTags []*model.TemplateTag
	err := rdb.Rdb.Model(&model.TemplateTag{}).Where("top > 0").Find(&templateTags).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "generateTopTemplateList get templateTags failed", zap.Error(err))
		return
	}
	if len(templateTags) == 0 {
		return
	}
	tagMaps := make(map[string]*model.TemplateTag, 0)
	generateTypes := make([]string, 0)
	for _, tag := range templateTags {
		generateTypes = append(generateTypes, tag.GenerateType)
		tagMaps[tag.GenerateType] = tag
	}
	var templates []*model.ConfigsVideo
	err = rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type in ?", generateTypes).Find(&templates).Error
	if err != nil {
		logger.ErrorWithCtx(ctx, "generateTopTemplateList get templates failed", zap.Error(err))
		return
	}
	tagTopTemplateMap := make(map[string][]*model.ConfigsVideo)
	for _, template := range templates {
		tag, ok := tagMaps[template.GenerateType]
		if !ok {
			continue
		}
		tagTopTemplateMap[tag.TagKey] = append(tagTopTemplateMap[tag.TagKey], template)
	}
	rc.tagTopTemplateMap.Store(&tagTopTemplateMap)
}

func (rc *RecommendCalc) generateSpecTemplateList(ctx context.Context) {
	for {
		rc.generateNewTemplateList(ctx)
		rc.generateTopTemplateList(ctx)
		time.Sleep(5 * time.Minute)
	}
}

func (rc *RecommendCalc) GetNewTemplatesByTagAneLevel(tagKey string, level []int32) ([]string, error) {
	return GetTemplatesByTagAneLevel(&rc.tagNewTemplateMap, tagKey, level)
}

func (rc *RecommendCalc) GetTopTemplatesByTagAneLevel(tagKey string, level []int32) ([]string, error) {
	return GetTemplatesByTagAneLevel(&rc.tagTopTemplateMap, tagKey, level)
}

func GetTemplatesByTagAneLevel(pTagMap *atomic.Pointer[map[string][]*model.ConfigsVideo], tagKey string, level []int32) ([]string, error) {
	tagMap := pTagMap.Load()
	if tagMap == nil {
		return nil, nil
	}
	templates, ok := (*tagMap)[tagKey]
	if !ok {
		return nil, nil
	}
	templateNames := make([]string, 0)
	for _, template := range templates {
		for _, l := range level {
			if template.TemplateLevel == l {
				templateNames = append(templateNames, template.GenerateType)
			}
		}
	}
	return templateNames, nil
}

var rc *RecommendCalc

func InitGenerate() {
	ctx := context.Background()
	ctx = context.WithValue(ctx, logger.TraceIDKey, app.NewTraceID())
	rc = NewRecommendCalc(ctx)
	uuid := uuid.New().String()
	logger.Info("InitGenerate", zap.String("uuid", uuid))
	// 同时只能有一个推荐计算运行
	go rc.generateSpecTemplateList(ctx)
	getLock := func() *redislock.Lock {
		for {
			lock, err := rdb.RedisLock.Obtain(ctx, "movely:lock:recommend_generate", 5*time.Hour, &redislock.Options{
				Token: uuid,
			})
			if err != nil {
				time.Sleep(time.Minute)
				continue
			}
			global.SetCleanup(global.RecommendRedisLockCleanup, func() error {
				return lock.Release(ctx)
			})
			logger.Info("InitGenerate getLock success", zap.String("uuid", uuid))
			return lock
		}
	}
	var lock *redislock.Lock
	for {
		if lock == nil || lock.Refresh(ctx, time.Minute, nil) != nil {
			lock = getLock()
		}
		start := time.Now()
		err := rc.GenerateRecommendTemplateScore(ctx)
		if err != nil {
			logger.Error("InitGenerate GenerateRecommendTemplateScore failed", zap.Error(err))
		}
		cost := time.Since(start)
		logger.Info("InitGenerate GenerateRecommendTemplateScore success", zap.Duration("cost", cost))
		if cost < 4*time.Hour {
			if config.Configuration.Debug {
				time.Sleep(5 * time.Minute)
			} else {
				time.Sleep(4*time.Hour - cost)
			}
		}
		lock.Release(ctx)
		global.SetCleanup(global.RecommendRedisLockCleanup, func() error {
			return nil
		})
	}
}

func InsertScore(ctx context.Context, key, generateType string, score float64) {
	err := rdb.Redis.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: generateType,
	}).Err()
	if err != nil {
		logger.ErrorWithCtx(ctx, "GenerateRecommendTemplateScore ZAdd failed", zap.String("key", key), zap.String("generateType", generateType), zap.Error(err))
	}
	rdb.Redis.Expire(ctx, key, 7*24*time.Hour).Result()
}

func GetScoreByLevel(ctx context.Context, generateType string, userLevel int, templateLevel int32) (float64, error) {
	var key string
	switch templateLevel {
	case 1:
		key = L1PopScoreL12
		if userLevel >= 3 {
			key = L1PopScoreL34
		}
	case 2:
		key = L2PopScoreL12
		if userLevel >= 3 {
			key = L2PopScoreL34
		}
	case 3:
		key = L3PopScoreL34
	case 4:
		key = L4PopScoreL34
	}
	score, err := rdb.Redis.ZScore(ctx, key, generateType).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}
	return score, nil
}

func CleanInvalidTemplate(generateTypes []string, templates []*models.VideoEntry, delFunc func(generateType string)) {
	tmp := map[string]struct{}{}
	for _, template := range templates {
		tmp[template.GenerateType] = struct{}{}
	}
	for _, generateType := range generateTypes {
		if _, ok := tmp[generateType]; !ok {
			delFunc(generateType)
		}
	}
}
