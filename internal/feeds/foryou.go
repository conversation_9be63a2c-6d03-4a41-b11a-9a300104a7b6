package feeds

import (
	"context"
	"fmt"

	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
)

type ForyouRecommend struct {
	uid       int64
	lang      string
	userLevel int
}

func NewForyouRecommend(uid int64, lang string, userLevel int) *ForyouRecommend {
	return &ForyouRecommend{
		uid:       uid,
		lang:      lang,
		userLevel: userLevel,
	}
}

const (
	AllNum      = 4
	L1UserL1Num = 2
	L1UserL2Num = 2
	L2UserL1Num = 1
	L2UserL2Num = 3

	L3UserPopScoreNum     = 2
	L3UserMoveInterestNum = 2

	L4UserPopScoreNum     = 2
	L4UserMoveInterestNum = 2
)

func (r *ForyouRecommend) getL1UserTemplateList(ctx context.Context, start, size int64) ([]string, error) {
	var generateTypes []string
	l1start := start * L1UserL1Num / AllNum
	l1stop := l1start + size*L1UserL1Num/AllNum - 1
	fmt.Println(l1start, l1stop)
	tmp, err := GetL1PopScoreL12List(ctx, l1start, l1stop)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	l2start := start * L1UserL2Num / AllNum
	l2stop := l2start + size*L1UserL2Num/AllNum - 1
	fmt.Println(l2start, l2stop)
	tmp, err = GetL2PopScoreL12List(ctx, l2start, l2stop)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	return generateTypes, nil
}

func (r *ForyouRecommend) getL2UserTemplateList(ctx context.Context, start, size int64) ([]string, error) {
	var generateTypes []string
	l1start := start * L2UserL1Num / AllNum
	tmp, err := GetL1PopScoreL12List(ctx, l1start, l1start+size*L2UserL1Num/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	l2start := start * L2UserL2Num / AllNum
	tmp, err = GetL2PopScoreL12List(ctx, l2start, l2start+size*L2UserL2Num/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	return generateTypes, nil
}

func (r *ForyouRecommend) getL3UserTemplateList(ctx context.Context, uid int64, start, size int64) ([]string, error) {
	// Todo
	var generateTypes []string
	l1start := start * L3UserPopScoreNum / AllNum
	tmp, err := GetTemplateListByShelfTag(ctx, 3, common.RecommendForYouKey, l1start, l1start+size*L3UserPopScoreNum/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	l2start := start * L3UserMoveInterestNum / AllNum
	tmp, err = GetHotMovesTemplateList(ctx, UserMoveInterestL3ScorePre, uid, l2start, l1start+size*L3UserMoveInterestNum/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	return generateTypes, nil
}

func (r *ForyouRecommend) getL4UserTemplateList(ctx context.Context, uid int64, start, size int64) ([]string, error) {
	// Todo
	var generateTypes []string
	l1start := start * L4UserPopScoreNum / AllNum
	tmp, err := GetTemplateListByShelfTag(ctx, 4, common.RecommendForYouKey, l1start, l1start+size*L4UserPopScoreNum/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	l2start := start * L4UserMoveInterestNum / AllNum
	tmp, err = GetHotMovesTemplateList(ctx, UserMoveInterestL4ScorePre, uid, l2start, l2start+size*L4UserMoveInterestNum/AllNum-1)
	if err != nil {
		return nil, err
	}
	generateTypes = append(generateTypes, tmp...)
	return generateTypes, nil
}

func (r *ForyouRecommend) GetTemplateNameList(ctx context.Context, req *GetTemplateNameListReq) (*GetTemplateNameListRsp, error) {
	var err error
	uid := r.uid
	start := req.Start
	size := req.Size
	var generateTypes []string
	switch r.userLevel {
	case 1:
		generateTypes, err = r.getL1UserTemplateList(ctx, start, size)
	case 2:
		generateTypes, err = r.getL2UserTemplateList(ctx, start, size)
	case 3:
		generateTypes, err = r.getL3UserTemplateList(ctx, uid, start, size)
	case 4:
		generateTypes, err = r.getL4UserTemplateList(ctx, uid, start, size)
	}
	if err != nil {
		return nil, err
	}
	return &GetTemplateNameListRsp{
		GenerateTypes: generateTypes,
		NextIndex:     start + size,
	}, nil
}

func (trending *ForyouRecommend) GetTotal(ctx context.Context) (int64, error) {
	num1, err := rdb.Redis.ZCard(ctx, L1PopScoreL12).Result()
	if err != nil {
		return 0, err
	}
	num2, err := rdb.Redis.ZCard(ctx, L2PopScoreL12).Result()
	if err != nil {
		return 0, err
	}
	return num1 + num2, nil
}
