package feeds

import (
	"context"
)

type OtherTagRecommend struct {
	uid       int64
	userLevel int
	tag       string
	isNew<PERSON>ser bool
}

func NewOtherTagRecommend(uid int64, isNewUser bool, userLevel int, tag string) *OtherTagRecommend {
	return &OtherTagRecommend{
		uid:       uid,
		userLevel: userLevel,
		tag:       tag,
		isNewUser: isNewUser,
	}
}

func (r *OtherTagRecommend) GetMergeList(ctx context.Context, start int64, size int64) ([]string, int64, error) {
	var res []string
	newAllTemps, err := rc.GetNewTemplatesByTagAneLevel(r.tag, GetVisLevelByUserLevel(r.userLevel))
	if err != nil {
		return nil, 0, err
	}
	newTempsNum := int64(len(newAllTemps))
	var newTemps []string
	var shelfTemps []string
	// index位置超过新模版的数量 全部取基础列表
	if start >= int64(newTempsNum) {
		shelfTemps, err = GetTemplateListByShelfTag(ctx, r.userLevel, r.tag, start, start+size)
	} else {
		shelfTemps, err = GetTemplateListByShelfTag(ctx, r.userLevel, r.tag, start, start+size/2)
		newTemps = GetTemplateNameList(newAllTemps, start, start+size/2)
	}
	if err != nil {
		return nil, 0, err
	}
	var index int = 0
	for _, temp := range shelfTemps {
		res = append(res, temp)
		// 间隔插入新模版
		if index < len(newTemps) {
			res = append(res, newTemps[index])
			index++
		}
	}

	return res, int64(len(shelfTemps)), nil
}

func (r *OtherTagRecommend) GetTemplateNameList(ctx context.Context, req *GetTemplateNameListReq) (*GetTemplateNameListRsp, error) {
	var err error
	var generateTypes []string
	generateTypes, err = rc.GetTopTemplatesByTagAneLevel(r.tag, GetVisLevelByUserLevel(r.userLevel))
	if err != nil {
		return nil, err
	}
	var res []string
	res = GetTemplateNameList(generateTypes, req.Start, req.Start+req.Size)
	// 置顶足够
	if len(res) >= int(req.Size) {
		return &GetTemplateNameListRsp{
			GenerateTypes: res,
			NextIndex:     req.Start + req.Size,
		}, nil
	}
	topNum := int64(len(generateTypes))
	start := req.Start
	size := req.Size - int64(len(res))
	if start >= int64(topNum) {
		// 超过置顶
		start -= int64(topNum)
	} else {
		// 置顶剩余不够
		start = 0
	}
	nextIndex := req.Start
	var temps []string
	if size > 0 {
		if r.isNewUser {
			temps, err = GetTemplateListByShelfTag(ctx, r.userLevel, r.tag, start, start+size)
			if err != nil {
				return nil, err
			} else {
				res = append(res, temps...)
			}
			nextIndex += int64(len(temps))
		} else {
			var num int64
			temps, num, err = r.GetMergeList(ctx, start, size)
			// 使用基础列表的index 插入的新模版不计算
			nextIndex += num
		}
	}
	if err != nil {
		return nil, err
	} else {
		res = append(res, temps...)
	}

	return &GetTemplateNameListRsp{
		GenerateTypes: res,
		NextIndex:     nextIndex,
	}, nil
}

func (trending *OtherTagRecommend) GetTotal(ctx context.Context) (int64, error) {
	return GetTemplateNumByShelfTag(ctx, trending.userLevel, trending.tag)
}
