package feeds

import (
	"context"
)

type TrendingRecommend struct {
	uid       int64
	userLevel int
	tag       string
}

func NewTrendingRecommend(uid int64, userLevel int, tag string) *TrendingRecommend {
	return &TrendingRecommend{
		uid:       uid,
		userLevel: userLevel,
		tag:       tag,
	}
}

func (trending *TrendingRecommend) GetTemplateNameList(ctx context.Context, req *GetTemplateNameListReq) (*GetTemplateNameListRsp, error) {
	var err error
	var generateTypes []string
	generateTypes, err = rc.GetTopTemplatesByTagAneLevel(trending.tag, GetVisLevelByUserLevel(trending.userLevel))
	if err != nil {
		return nil, err
	}
	var res []string
	res = GetTemplateNameList(generateTypes, req.Start, req.Start+req.Size)
	// 置顶足够
	if len(res) >= int(req.Size) {
		return &GetTemplateNameListRsp{
			GenerateTypes: res,
			NextIndex:     req.Start + req.Size,
		}, nil
	}
	topNum := int64(len(generateTypes))
	start := req.Start
	size := req.Size - int64(len(res))
	if start >= int64(topNum) {
		// 超过置顶
		start -= int64(topNum)
	} else {
		// 置顶剩余不够
		start = 0
	}
	if size > 0 {
		temps, err := GetTemplateListByShelfTag(ctx, trending.userLevel, trending.tag, start, start+size)
		if err != nil {
			return nil, err
		} else {
			res = append(res, temps...)
		}
	}

	return &GetTemplateNameListRsp{
		GenerateTypes: res,
		NextIndex:     req.Start + int64(len(res)),
	}, nil
}

func (trending *TrendingRecommend) GetTotal(ctx context.Context) (int64, error) {
	return GetTemplateNumByShelfTag(ctx, trending.userLevel, trending.tag)
}
