package config_handler

import (
	"context"
	"fmt"
	"math/rand/v2"

	"git.hoxigames.xyz/movely/movely-server/internal/output"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
)

type GetLongConnAddrReq struct {
	Cluster string `json:"cluster"`
	AppID   uint64 `json:"app"`
}

type GetLongConnAddrRsp struct {
	AddrList []string `json:"addr_list"`
}

func Longconn(c *app.Context) {
	req := &GetLongConnAddrReq{}
	if err := c.ShouldBind(req); err != nil {
		output.Error(c, output.ParamErrorCode, err)
		return
	}
	ctx := context.Background()
	addrs, err := rdb.Redis.SMembers(ctx, fmt.Sprintf("movely:gateway_addr_%d_%s", req.AppID, req.Cluster)).Result()
	if err != nil {
		output.Error(c, output.InternalErrorCode, err)
		return
	}
	rand.Shuffle(len(addrs), func(i, j int) {
		addrs[i], addrs[j] = addrs[j], addrs[i]
	})
	resp := GetLongConnAddrRsp{
		AddrList: addrs,
	}
	output.Success(c, resp)
}
