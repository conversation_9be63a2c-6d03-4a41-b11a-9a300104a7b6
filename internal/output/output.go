package output

import (
	"errors"
	"fmt"
	"net/http"
	"runtime"

	"git.hoxigames.xyz/movely/movely-server/pkg/app"
)

type output struct {
	StatusCode       int
	Code             int
	Msg              string
	ErrorMessageChin string
}

// TODO: 应该定义出error出来，然后通过error判断
const (
	SuccessCode = 0

	ReLoginErrorCode      = 10001
	UserInfoErrorCode     = 10002
	InternalErrorCode     = 10003
	ErrCodeVCodeError     = 10004
	ErrCodeReqTooFrequent = 10005

	ParamErrorCode           = 20001
	LikeNumLimitErrorCode    = 20002
	NotEnoughCreditErrorCode = 20003
	FileTooLargeErrorCode    = 20004
	UnsupportedFileErrorCode = 20005

	// 任务相关
	ErrCodeTaskCanNotCancel = 21001

	// 支付相关
	ErrCodeTransactionAlreadyExist = 22001
)

var (
	errorMap = map[int]*output{
		ReLoginErrorCode:               {http.StatusUnauthorized, 10001, "Re-login", "登录信息已失效"},
		UserInfoErrorCode:              {http.StatusOK, 10002, "User information is abnormal", "用户信息获取失败"},
		InternalErrorCode:              {http.StatusOK, 10003, "Internal error", "内部错误"},
		ErrCodeVCodeError:              {http.StatusOK, 10004, "Verification code error", "验证码错误"},
		ErrCodeReqTooFrequent:          {http.StatusOK, 10005, "Req too frequent", "请求过于频繁"},
		ParamErrorCode:                 {http.StatusOK, 20001, "Parameter error", "参数错误"},
		LikeNumLimitErrorCode:          {http.StatusOK, 20002, "Like number limit error", "点赞数量限制"},
		NotEnoughCreditErrorCode:       {http.StatusOK, 20003, "Not enough credit", "余额不足"},
		FileTooLargeErrorCode:          {http.StatusOK, 20004, "Uploaded file is too large", "上传文件过大"},
		UnsupportedFileErrorCode:       {http.StatusOK, 20005, "Unsupported file types", "不支持的文件类型"},
		ErrCodeTaskCanNotCancel:        {http.StatusOK, 21001, "Task can not cancel", "任务不能取消"},
		ErrCodeTransactionAlreadyExist: {http.StatusOK, 22001, "Transaction already exist", "交易已存在"},
	}
	defaultOut = &output{
		StatusCode: http.StatusOK,
		Code:       99999,
		Msg:        "unknown",
	}
)

func Success(ctx *app.Context, data interface{}) {
	ctx.Success(data)
	return
}

func SuccessNil(ctx *app.Context) {
	ctx.SuccessNil()
	return
}

func Error(ctx *app.Context, code int, err error) {
	out, ok := errorMap[code]
	if !ok {
		out = defaultOut
	}
	if code == InternalErrorCode { // 先放开
		if err != nil {
			out.Msg = err.Error()
		}
	}
	err = SetCaller(2, err)
	ctx.Fail(out.StatusCode, out.Code, out.Msg, err)
	return
}

func SetCaller(skip int, error error) (err error) {
	var errMsg string
	pc, file, line, ok := runtime.Caller(skip)
	if ok {
		name := runtime.FuncForPC(pc).Name()
		if error != nil {
			errMsg = error.Error()
		}
		err = fmt.Errorf("func is %s, file is %s, line is %d, err is %s", name, file, line, errMsg)
	} else {
		err = errors.New("runtime no such pc")
	}
	return
}
