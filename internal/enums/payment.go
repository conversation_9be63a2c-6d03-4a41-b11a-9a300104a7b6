package enums

type Vendor = string

const (
	AppleStore Vendor = "apple"
	Stripe     Vendor = "stripe"
	Paypal     Vendor = "paypal"
	GooglePlay Vendor = "google_play"
)

type SubscribeStatus = int32

const (
	SubActive  SubscribeStatus = 1 // 订阅有效
	SubExpired SubscribeStatus = 2 // 订阅过期
	SubRefund  SubscribeStatus = 3 // 订阅退款
	SubWipe    SubscribeStatus = 4 // 订阅清理
)

type PaymentFromScene string

const (
	PaymentFromScenePurchase  PaymentFromScene = "purchase"  // 客户端购买
	PaymentFromSceneRestore   PaymentFromScene = "restore"   // 客户端恢复
	PaymentFromSceneSubscribe PaymentFromScene = "subscribe" // 订阅事件回调
	PaymentFromSceneRenew     PaymentFromScene = "renew"     // 续费事件回调
	PaymentFromSceneSchedule  PaymentFromScene = "schedule"  // 定时任务处理
)
