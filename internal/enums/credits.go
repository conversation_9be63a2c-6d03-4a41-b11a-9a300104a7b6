package enums

import "time"

type DeductCredits struct {
	BusinessType int32 `json:"business_type"`
	Balance      int64 `json:"balance"`
}

const (
	FreeAutoBusinessType     = 10001 // free
	FreeGiveawayBusinessType = 10002

	RechargeSubsBusinessType = 20001 // recharge
	RechargePurBusinessType  = 20002
)

// 增加和减少 credit 的场景
const (
	PurScene          = "purchase"
	SubScene          = "subscribe"
	FreeAutoScene     = "free_auto"
	FreeGiveawayScene = "free_giveaway"
	RefundScene       = "refund"
	RewardScene       = "reward"

	ClearScene       = "clear"
	Image2VideoScene = "image2video" // 生成视频
)

const (
	SupplyRateDefault int64 = 1800
	MaxDefault        int64 = 90
	FactorDefault     int64 = 1
)

const (
	RechargeRewardInterval = time.Hour * 24 * 30 // 视频订阅奖励间隔时间
)

const (
	PurchaseEntry  = "purchase"
	SubscribeEntry = "subscribe"
)
