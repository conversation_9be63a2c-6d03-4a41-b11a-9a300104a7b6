package enums

import (
	"regexp"
	"strings"
	"time"
)

// 视频模板类型
const (
	VideoTypeImage2Video = "image2video"
	VideoTypeID2Video    = "id2video"
	VideoTypeVideo2Video = "video2video"
	VideoTypeBody2Video  = "body2video" // 身体视频, 目前是丰胸
)

// 视频生成特殊类型
const (
	GenerateCustomize = "video_gen_customize"
	GenerateExtend    = "extend"
)

type TaskStatus_Enum int32

const (
	TaskStatus_queuing   TaskStatus_Enum = -1 // 排队
	TaskStatus_invalid   TaskStatus_Enum = 0  // 默认
	TaskStatus_created   TaskStatus_Enum = 1  // 创建
	TaskStatus_finished  TaskStatus_Enum = 2  // 成功
	TaskStatus_failed    TaskStatus_Enum = 3  // 失败
	TaskStatus_resaving  TaskStatus_Enum = 4  // 转存结果进行中
	TaskStatus_cancelled TaskStatus_Enum = 5  // 取消
)

type VideoCoinStatus string

const (
	VideoCoinStatusDeducted VideoCoinStatus = "deducted"
	VideoCoinStatusRefunded VideoCoinStatus = "refunded"
)

type VideoCoinCostVersion uint8

const (
	VideoCoinCostVersion1 VideoCoinCostVersion = 1
	VideoCoinCostVersion2 VideoCoinCostVersion = 2
)

type VideoCoinInfo struct {
	Status    VideoCoinStatus `json:"status"`
	CostCoins []DeductCredits `json:"cost_coins"`
}

const (
	TenDuration           = "10"
	FiveDuration          = "5"
	VideoExtendExpireTime = 30 * 24 * time.Hour
)

// https://hoxigames.feishu.cn/wiki/LyJbwiEewizcv3kac4OczbIln3e
var ClearPromptVideoMap = map[string]bool{
	"video_temp_20250424_5rSoBDJ6Kr3": true,
	"video_temp_20250506_5sscmAkfVg7": true,
	"video_temp_20250423_5rP7bGomyHW": true,
	"video_temp_20250423_5rP794ejfXj": true,
	"video_temp_20250423_5rP1ZG92UhG": true,
	"video_temp_20250423_5rP1N6f2SH7": true,
	"video_temp_20250422_5rLFEdZvY3m": true,
	"video_temp_20250403_5qQnwNHNoHw": true,
	"video_temp_20250513_5sNapd3hqjL": true,
	"video_temp_20250516_5sW8FZLKE6N": true,
	"video_temp_20250603_5tPspctaDab": true,
	"video_temp_20250529_5tz3g4aYBeS": true,
	"video_temp_20250605_5tVxusGyUSu": true,
	"video_temp_20250614_5umaw4LEbod": true,
	"video_temp_20250616_5urP8SoiGdy": true,
	"video_temp_20250616_5urPUXyePz3": true,
	"video_temp_20250625_5uTjaMosFPf": true,
}

func FilterSensitiveWords(prompt string) string {
	// 敏感词列表
	sensitiveWords := []string{
		"nipples", "nipple", "naked", "nake", "remove bra",
		"round areola", "open bra", "undress", "topless", "areola", "nude breasts",
		"takes off", "taking off", "falling out", "ripping off", "no clothing", "unbuttoning", "lighting off",
		"no clothes", "topless bodynude", "breasts fully visible", "tears off",
		"boobs exposed", "breast exposed", "nude boobs", "shaved pubes",
	}
	result := prompt
	for _, word := range sensitiveWords {
		re := regexp.MustCompile("(?i)" + regexp.QuoteMeta(word))
		result = re.ReplaceAllString(result, "")
	}
	return result
}

func GetVideoCreateDuration(thirdparty string) []string {
	durations := []string{"5"}
	if thirdparty == "kling" {
		durations = []string{"5", "10"}
	}
	return durations
}

func GetVideoExpectedCostSeconds(thirdparty string) int32 {
	if strings.Contains(thirdparty, "wan") {
		return 3 * 60
	}
	return 5 * 60
}

// 视频金币增加
const (
	VIDEO_TYPE_PURCHASE        = "purchase"        // 2001 一次性购买，永远不过期
	VIDEO_TYPE_REWARD          = "reward"          // 2002 订阅会员给的奖励，会清理
	VIDEO_TYPE_FREE            = "free"            // 2003 免费1次的，终生一次机会
	VIDEO_TYPE_BODY_FREE       = "body_free"       // 2005 身体编辑免费1次的，终生一次机会
	VIDEO_TYPE_SUBSCRIBE       = "subscribe"       // 2004 视频订阅奖励，会清理
	VIDEO_TYPE_REFUND          = "refund"          // 退款
	VIDEO_TYPE_CLEAR           = "clear"           // 清币
	VIDEO_TYPE_REGENERATE_FREE = "regenerate_free" // 2006 会员每周免费重试两次

	// 视频金币使用
	VIDEO_TYPE_GENERATE = "generate" // 生成视频
)

func VideoTypeToTrack(videoType string) string {
	switch videoType {
	case VIDEO_TYPE_PURCHASE:
		return "token_onetime_purchase"
	case VIDEO_TYPE_REWARD:
		return "pro_subscription"
	case VIDEO_TYPE_FREE:
		return "free_1time"
	case VIDEO_TYPE_SUBSCRIBE:
		return "token_subscription"
	case VIDEO_TYPE_BODY_FREE:
		return "free_1time_bodyedit"
	case VIDEO_TYPE_REGENERATE_FREE:
		return "free_retry"
	default:
		return ""
	}
}

var (
	// 机器组
	WanGroupRemoteAccelerateLora = "wan_remote_accelerate_lora"
	WanGroupRemoteFusionix       = "wan_remote_fusionix"
	WanGroupRemote               = "wan_remote"
	WanGroupDefault              = "wan"

	// 机房
	WanRoomH100Us     = "wan_h100_us"
	WanRoomH100Zj     = "wan_h100_zj"
	WanRoom4090Qy     = "wan_4090_qy"
	WanRoom4090Autodl = "wan_4090_autodl"

	// 对应关系
	WanRoomGroupMap = map[string]string{
		WanRoomH100Us: WanGroupRemoteAccelerateLora, // 美国 加速lora
		// WanRoomH100Zj:     WanGroupRemoteFusionix,       // 浙江 fusionix
		WanRoomH100Zj:     WanGroupRemoteAccelerateLora, // 浙江 加速lora
		WanRoom4090Qy:     WanGroupRemote,               // 4090 庆阳
		WanRoom4090Autodl: WanGroupDefault,              // autodl 2卡
	}
)

// TODO: 要加属性解决
func IsFusionix(generateType string) bool {
	mapFusionix := map[string]bool{
		"video_temp_20250714_5vPHCZqP6tW": true,
		"video_temp_20250714_5vPR14ANCfm": true,
		"video_temp_20250714_5vPTsUd8N1Q": true,
		"video_temp_20250717_5vX4z1Rdqio": true,
		"video_temp_20250717_5vXaSPQFJ3L": true,
		"video_temp_20250717_5vXhF1cdsV7": true,
		"video_temp_20250718_5w1t7cnDcNh": true,
		"video_temp_20250718_5w1HnPUnJq1": true,
		"video_temp_20250718_5w1MruRay8S": true,
		"video_temp_20250711_5vF1bghFbh1": true,
		"video_temp_20250715_5vSquKovgj9": true,
		"video_temp_20250714_5vNHxku8119": true,
		"video_temp_20250714_5vNJaY2Hiwq": true,
		"video_temp_20250714_5vNUJiZ1ekN": true,
		"video_temp_20250714_5vNV6vD4xBf": true,
		"video_temp_20250714_5vNVC4dbQDN": true,
	}
	return mapFusionix[generateType]
}
