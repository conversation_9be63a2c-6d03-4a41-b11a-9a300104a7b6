package enums

import (
	"strings"
	"sync"

	"git.hoxigames.xyz/movely/movely-server/internal/utils"
)

type ProductType string

const (
	ProductTypeSubscribe ProductType = "subscribe"
	ProductTypeOneTime   ProductType = "one_time"
)

type ProductCredit struct {
	BaseCount              int64 // 基础credit
	RewardIntervalSeconds  int64 // 奖励间隔秒
	RewardIntervalCount    int64 // 奖励间隔次数
	RewardMaxLimit         int64 // 最大奖励credit
	OnetimeAdditionalRatio int64 // 一次性商品额外奖励百分制
}
type Product struct {
	Id      string
	Type    ProductType
	Price   string
	Level   int64
	Credits ProductCredit
}

var (
	AllProductDev    = make(map[string]*Product)
	AllProductOnline = make(map[string]*Product)
	once             sync.Once
)

func init() {
	// 商品文档： https://hoxigames.feishu.cn/wiki/MErgw9yfKi3oq9k8KefcxXl9nEe
	once.Do(func() {
		initSubscribeProducts()
		initOneTimeProducts()
	})
}

const (
	// 周 + 年
	ProductIdMovelyBasicWeekly7 = "movely_basic_weekly_7"
	ProductIdMovelyProWeekly14  = "movely_pro_weekly_14"
	ProductIdMovelyMegaWeekly39 = "movely_mega_weekly_39"

	ProductIdMovelyBasicAnnual94 = "movely_basic_annual_94"
	ProductIdMovelyProAnnual289  = "movely_pro_annual_289"
	ProductIdMovelyMegaAnnual529 = "movely_mega_annual_529"

	// 月 + 年
	ProductIdMovelyBasicMonthly9 = "movely_basic_monthly_9"
	ProductIdMovelyProMonthly29  = "movely_pro_monthly_29"
	ProductIdMovelyMegaMonthly54 = "movely_mega_monthly_54"

	ProductIdMovelyBasicAnnual99 = "movely_basic_annual_99"
	ProductIdMovelyProAnnual399  = "movely_pro_annual_399"
	ProductIdMovelyMegaAnnual699 = "movely_mega_annual_699"

	// 一次性商品
	ProductIdMovelyVideoCredits499 = "movely_video_credits_499"
	ProductIdMovelyVideoCredits199 = "movely_video_credits_199"
	ProductIdMovelyVideoCredits99  = "movely_video_credits_99"
	ProductIdMovelyVideoCredits49  = "movely_video_credits_49"
	ProductIdMovelyVideoCredits19  = "movely_video_credits_19"
	ProductIdMovelyVideoCredits9   = "movely_video_credits_9"
)

// 订阅商品在这里加
func initSubscribeProducts() {
	subscribeProducts := map[string]struct {
		Price                  string
		Level                  int64
		BaseCount              int64
		RewardIntervalSeconds  int64 // 奖励间隔秒
		RewardIntervalCount    int64 // 奖励间隔次数
		RewardMaxLimit         int64 // 最大奖励上限
		OnetimeAdditionalRatio int64 // 一次性商品额外奖励百分制
	}{
		ProductIdMovelyBasicWeekly7: {Price: "7.99", Level: 9, BaseCount: 500, RewardIntervalCount: 1, RewardMaxLimit: 90, RewardIntervalSeconds: 1800, OnetimeAdditionalRatio: 0},
		ProductIdMovelyProWeekly14:  {Price: "14.99", Level: 8, BaseCount: 1500, RewardIntervalCount: 1, RewardMaxLimit: 150, RewardIntervalSeconds: 900, OnetimeAdditionalRatio: 10},
		ProductIdMovelyMegaWeekly39: {Price: "39.99", Level: 7, BaseCount: 4500, RewardIntervalCount: 1, RewardMaxLimit: 300, RewardIntervalSeconds: 600, OnetimeAdditionalRatio: 20},

		ProductIdMovelyBasicMonthly9: {Price: "9.99", Level: 6, BaseCount: 1000, RewardIntervalCount: 1, RewardMaxLimit: 90, RewardIntervalSeconds: 1800, OnetimeAdditionalRatio: 0},
		ProductIdMovelyProMonthly29:  {Price: "29.99", Level: 5, BaseCount: 4000, RewardIntervalCount: 1, RewardMaxLimit: 150, RewardIntervalSeconds: 900, OnetimeAdditionalRatio: 10},
		ProductIdMovelyMegaMonthly54: {Price: "54.99", Level: 4, BaseCount: 10000, RewardIntervalCount: 1, RewardMaxLimit: 300, RewardIntervalSeconds: 600, OnetimeAdditionalRatio: 20},

		ProductIdMovelyBasicAnnual94: {Price: "94.99", Level: 3, BaseCount: 1000, RewardIntervalCount: 1, RewardMaxLimit: 90, RewardIntervalSeconds: 1800, OnetimeAdditionalRatio: 0},
		ProductIdMovelyProAnnual289:  {Price: "289.99", Level: 2, BaseCount: 4000, RewardIntervalCount: 1, RewardMaxLimit: 150, RewardIntervalSeconds: 900, OnetimeAdditionalRatio: 10},
		ProductIdMovelyMegaAnnual529: {Price: "529.99", Level: 1, BaseCount: 10000, RewardIntervalCount: 1, RewardMaxLimit: 300, RewardIntervalSeconds: 600, OnetimeAdditionalRatio: 20},

		ProductIdMovelyBasicAnnual99: {Price: "99.99", Level: 3, BaseCount: 2000, RewardIntervalCount: 1, RewardMaxLimit: 90, RewardIntervalSeconds: 1800, OnetimeAdditionalRatio: 0},
		ProductIdMovelyProAnnual399:  {Price: "399.99", Level: 2, BaseCount: 6000, RewardIntervalCount: 1, RewardMaxLimit: 150, RewardIntervalSeconds: 900, OnetimeAdditionalRatio: 10},
		ProductIdMovelyMegaAnnual699: {Price: "699.99", Level: 1, BaseCount: 18000, RewardIntervalCount: 1, RewardMaxLimit: 300, RewardIntervalSeconds: 600, OnetimeAdditionalRatio: 20},
	}
	for id, p := range subscribeProducts {
		addSubscribeProduct(id, p.Price, p.Level, p.BaseCount, 3600, p.RewardIntervalCount, p.RewardMaxLimit, p.OnetimeAdditionalRatio)
	}
}

// 一次性商品在这里加
func initOneTimeProducts() {
	oneTimeProducts := map[string]struct {
		Price   string
		Credits int64
	}{
		ProductIdMovelyVideoCredits499: {Price: "499.99", Credits: 60000},
		ProductIdMovelyVideoCredits199: {Price: "199.99", Credits: 16000},
		ProductIdMovelyVideoCredits99:  {Price: "99.99", Credits: 8000},
		ProductIdMovelyVideoCredits49:  {Price: "49.99", Credits: 3500},
		ProductIdMovelyVideoCredits19:  {Price: "19.99", Credits: 1250},
		ProductIdMovelyVideoCredits9:   {Price: "9.99", Credits: 500},
	}
	for id, p := range oneTimeProducts {
		addOneTimeProduct(id, p.Price, p.Credits)
	}
}

func addOneTimeProduct(id string, price string, credits int64) {
	product := &Product{
		Id:      id,
		Price:   price,
		Type:    ProductTypeOneTime,
		Credits: ProductCredit{BaseCount: credits},
	}
	AllProductOnline[id] = product

	devProduct := *product
	devProduct.Id = "dev_" + product.Id
	AllProductDev[devProduct.Id] = &devProduct
}

func addSubscribeProduct(id string, price string, level int64, baseCount int64, rewardIntervalSeconds int64, rewardIntervalCount int64, rewardMaxLimit int64, onetimeAdditionalRatio int64) {
	product := &Product{
		Id:    id,
		Price: price,
		Level: level,
		Type:  ProductTypeSubscribe,
		Credits: ProductCredit{
			BaseCount:              baseCount,
			RewardIntervalSeconds:  rewardIntervalSeconds,
			RewardIntervalCount:    rewardIntervalCount,
			RewardMaxLimit:         rewardMaxLimit,
			OnetimeAdditionalRatio: onetimeAdditionalRatio,
		},
	}
	if utils.ExtractMemberShipFromProduct(id) == "" {
		panic("invalid subscribe product id: " + id)
	}
	AllProductOnline[id] = product

	devProduct := *product
	devProduct.Id = "dev_" + product.Id
	AllProductDev[devProduct.Id] = &devProduct
}

func GetProduct(name string, envTag string) *Product {
	name = strings.ToLower(name)
	if envTag == "dev" || envTag == "local" {
		if strings.HasPrefix(name, "dev_") {
			return AllProductDev[name]
		}
		return AllProductDev["dev_"+name]
	}
	return AllProductOnline[name]
}

// 月 + 年
func GetMonthlySubscribeProducts(env string) []*Product {
	productIds := []string{
		ProductIdMovelyBasicMonthly9,
		ProductIdMovelyProMonthly29,
		ProductIdMovelyMegaMonthly54,
		ProductIdMovelyBasicAnnual94,
		ProductIdMovelyProAnnual289,
		ProductIdMovelyMegaAnnual529,
	}
	products := make([]*Product, 0)
	for _, productId := range productIds {
		if product := GetProduct(productId, env); product != nil {
			products = append(products, product)
		}
	}
	return products
}

// 周 + 年
func GetWeeklySubscribeProducts(env string) []*Product {
	productIds := []string{
		ProductIdMovelyBasicWeekly7,
		ProductIdMovelyProWeekly14,
		ProductIdMovelyMegaWeekly39,
		ProductIdMovelyBasicAnnual99,
		ProductIdMovelyProAnnual399,
		ProductIdMovelyMegaAnnual699,
	}
	products := make([]*Product, 0)
	for _, productId := range productIds {
		if product := GetProduct(productId, env); product != nil {
			products = append(products, product)
		}
	}
	return products
}

// 一次性商品
func GetOnetimeProducts(env string) []*Product {
	productIds := []string{
		ProductIdMovelyVideoCredits499,
		ProductIdMovelyVideoCredits199,
		ProductIdMovelyVideoCredits99,
		ProductIdMovelyVideoCredits49,
		ProductIdMovelyVideoCredits19,
		ProductIdMovelyVideoCredits9,
	}
	products := make([]*Product, 0)
	for _, productId := range productIds {
		products = append(products, GetProduct(productId, env))
	}
	return products
}

var StripeProductMap = map[string]string{
	// dev
	"price_1RoG4bL39OuSalus3P5G8x35": "dev_" + ProductIdMovelyBasicAnnual99,
	"price_1RoG4bL39OuSalus3yF9sP5t": "dev_" + ProductIdMovelyBasicMonthly9,
	"price_1RnyQRL39OuSalus2msincc4": "dev_" + ProductIdMovelyProAnnual399,
	"price_1RnyQRL39OuSalusPUpf2gM4": "dev_" + ProductIdMovelyProMonthly29,
	"price_1RnyRZL39OuSalusFZN3vAD6": "dev_" + ProductIdMovelyMegaAnnual699,
	"price_1RnyRZL39OuSalusEuFXnciW": "dev_" + ProductIdMovelyMegaMonthly54,

	// online
	"price_1RoGCGL39OuSalusEGxMmY8S": ProductIdMovelyBasicAnnual99,
	"price_1RoGCFL39OuSalusGNGk4VFa": ProductIdMovelyBasicMonthly9,
	"price_1RoGCUL39OuSalusKMeAmrNW": ProductIdMovelyProAnnual399,
	"price_1RoGCUL39OuSalusd0uKJMCN": ProductIdMovelyProMonthly29,
	"price_1RoGCNL39OuSalusXJFy5sTs": ProductIdMovelyMegaAnnual699,
	"price_1RoGCNL39OuSalusCAeZk3b4": ProductIdMovelyMegaMonthly54,
}

func GetProductByStripePriceID(priceID string, env string) *Product {
	return GetProduct(StripeProductMap[priceID], env)
}

func GetStripePriceIDByProductID(productID string) string {
	for k, v := range StripeProductMap {
		if v == productID {
			return k
		}
	}
	return ""
}
