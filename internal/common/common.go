package common

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"
	"os"
	"strconv"
	"time"

	"git.hoxigames.xyz/movely/movely-server/bizconf"
	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/utils"
	"git.hoxigames.xyz/movely/movely-server/models"
	"git.hoxigames.xyz/movely/movely-server/pkg/app"
	"git.hoxigames.xyz/movely/movely-server/pkg/bot"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"git.hoxigames.xyz/movely/movely-server/pkg/rdb"
	"git.hoxigames.xyz/movely/movely-server/pkg/te"
	"github.com/go-lark/lark"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/zap"
)

const (
	ShelfTrendingKey   = "tag_trending"
	ShelfHotMovesKey   = "tag_hotmoves"
	RecommendForYouKey = "tag_foryou"

	TagTypeShelf    = "shelf"
	TagTypeCategory = "category"
	TagTypeFetish   = "fetish"
)

const (
	UserVideoActionTypeLike = "like"

	UserTemplateListLike   = "like"
	UserTemplateListRecent = "recent"
)

func GetTemplateListByGenerateType(generateTypes []string, uid int64, lang string) ([]*models.VideoEntry, error) {
	var res []model.ConfigsVideo = make([]model.ConfigsVideo, 0)
	err := rdb.Rdb.Model(&model.ConfigsVideo{}).Where("generate_type in (?)", generateTypes).
		Where("status = ?", 1).Find(&res).Error
	if err != nil {
		return nil, err
	}
	users := []model.UserTemplateAction{}
	err = rdb.Rdb.Model(&model.UserTemplateAction{}).Where("user_id = ?", uid).Find(&users).Error
	if err != nil {
		return nil, err
	}
	actionmap := make(map[string]model.UserTemplateAction)
	for _, v := range users {
		actionmap[v.GenerateType] = v
	}
	entries := make([]*models.VideoEntry, 0)
	for _, v := range res {
		title := bizconf.LangConf.Text(lang, v.Title)
		titleEn := bizconf.LangConf.Text("en", v.Title)
		if title == "" {
			title = v.Title
		}
		if titleEn == "" {
			titleEn = v.Title
		}
		entries = append(entries, &models.VideoEntry{
			VideoType:      v.VideoType,
			GenerateType:   v.GenerateType,
			MasterTemplate: v.MasterTemplate,
			Title:          title,
			TitleEnName:    titleEn,
			ImageUrl:       v.Image,
			VideoUrl:       v.Video,
			VideoMediumUrl: v.VideoMedium,
			VideoLowUrl:    v.VideoLow,
			Blurhash:       v.Blurhash,
			VideoWidth:     int(v.VideoWidth),
			VideoHeight:    int(v.VideoHeight),
			Autoplay:       v.Autoplay == 1,
			FreeTrial:      v.FreeTrial == 1,
			LikeCount:      v.LikeCount,
			BaseLikeCount:  v.BaseLikeCount,
			ShareCount:     v.ShareCount,
			TemplateLevel:  v.TemplateLevel,
			Thirdparty:     v.Thirdparty,
			UserInfo: models.UserTemplateInfo{
				IsLike: actionmap[v.GenerateType].ActionType == UserVideoActionTypeLike,
			},
		})
	}
	return entries, nil
}

func Normalization(data []float64) []float64 {
	max := 0.0
	min := 1000000.0
	for _, v := range data {
		if v > max {
			max = v
		}
		if v < min {
			min = v
		}
	}
	for i, v := range data {
		data[i] = (v - min) / (max - min)
	}
	return data
}

func GetUserInfo(uid int64) (*model.User, error) {
	user := &model.User{}
	err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", uid).First(user).Error
	if err != nil {
		return nil, err
	}
	return user, nil
}

func UpdateInfoByTask(ctx context.Context, uid int64, template *model.ConfigsVideo, tagKey string) {
	// Todo 更新用户等级
	// - L1：除L3/L4用户外，没有使用过L2模板的用户
	// - L2：除L3/L4用户外，有使用过L2模板的用户
	// - L3：L3模板有过1次生成行为（通过他人分享路径），或L2模板生成结果有过1次保存/收藏/分享行为，可被曝光L3模板
	// - L4：保存/收藏/分享3次L3模板生成结果，或生成大于10条L3模板内容后，可被曝光L4模板
	var err error
	switch template.TemplateLevel {
	case 2:
		err = rdb.Rdb.Model(&model.User{}).Where("uid = ? and user_level = 1", uid).UpdateColumn("user_level", 2).Error
	case 3:
		err = rdb.Rdb.Model(&model.User{}).Where("uid = ? and user_level < 3", uid).UpdateColumn("user_level", 3).Error
	}
	if err != nil {
		logger.ErrorWithCtx(ctx, "UpdateInfoByTask update userlevel err", zap.Error(err))
	}
	// 用户是否使用过hotmove的模版
	if tagKey == ShelfHotMovesKey {
		err := rdb.Rdb.Model(&model.UserAction{}).Where("user_id = ?", uid).Assign(&model.UserAction{
			UseHotmove: true,
		}).FirstOrCreate(&model.UserAction{
			UID:        uid,
			UseHotmove: true,
		}).Error
		if err != nil {
			logger.WarnWithCtx(ctx, "UpdateInfoByTask update user action err", zap.Error(err))
		}
	}
	// 自动给用户增加收藏的分组
	if tagKey != "" && tagKey != RecommendForYouKey {
		// 校验分组是否有效
		var feedTag model.FeedTag
		err := rdb.Rdb.Model(&model.FeedTag{}).Where("tag_key = ?", tagKey).First(&feedTag).Error
		if err != nil {
			logger.WarnWithCtx(ctx, "UpdateInfoByTask query user tag action err", zap.Error(err))
		} else if feedTag.Status == 1 {
			err = rdb.Rdb.Model(&model.UserTagAction{}).Where("user_id = ? AND tag_key = ?", uid, tagKey).FirstOrCreate(&model.UserTagAction{
				UserID:     uid,
				TagKey:     tagKey,
				ActionType: UserVideoActionTypeLike,
			}).Error
			if err != nil {
				logger.WarnWithCtx(ctx, "UpdateInfoByTask update user tag action err", zap.Error(err))
			}
		}
	}
	var count int64
	err = rdb.Rdb.Model(&model.UserTemplateRecent{}).Where("user_id = ?", uid).Count(&count).Error
	if err != nil {
		logger.WarnWithCtx(ctx, "UpdateInfoByTask count user template recent err", zap.Error(err))
	}
	if count <= 50 {
		err = rdb.Rdb.Model(&model.UserTemplateRecent{}).Where("user_id = ? AND generate_type = ?", uid, template.GenerateType).
			Assign("updated_at = ?", time.Now().Unix()).
			FirstOrCreate(&model.UserTemplateRecent{
				UserID:       uid,
				GenerateType: template.GenerateType,
			}).Error
		if err != nil {
			logger.WarnWithCtx(ctx, "UpdateInfoByTask update user template recent err", zap.Error(err))
		}
	}
}

func UniqueSlice[T any](slice []T, keyFunc func(T) string) []T {
	uniqueMap := make(map[string]bool)
	result := []T{}
	for _, v := range slice {
		key := keyFunc(v)
		if !uniqueMap[key] {
			uniqueMap[key] = true
			result = append(result, v)
		}
	}
	return result
}

// 上报事件
func TrackWithBaseAttributes(c *app.Context, userId int64, eventName string, info map[string]any) {
	ctx := context.WithValue(context.Background(), logger.TraceIDKey, c.GetTraceID())
	var gUser model.User
	if err := rdb.Rdb.Model(&model.User{}).Where("uid = ?", userId).First(&gUser).Error; err != nil {
		msg := fmt.Sprintf("TrackWithBaseAttributes get user err: %v, userId: %d, eventName: %s, info: %v", err, userId, eventName, info)
		logger.ErrorWithTraceId(c.GetTraceID(), msg)
		SendServerAlarmMsg(ctx, userId, c.Country, msg)
	}
	environment := "production"
	if config.IsDev() {
		environment = "dev"
	}
	base := map[string]interface{}{
		"account_id":      strconv.Itoa(int(gUser.UID)),
		"device_id":       c.DistinctId,
		"app_version_sdk": c.AppVersionSdk,
		"os_name":         c.Platform,
		"environment":     environment,
		"country":         c.Country,
		"user_pay_status": utils.ExtractMemberShipFromProduct(gUser.SubscribeProductID),
		"user_level":      gUser.UserLevel,
		"trace_id":        c.GetTraceID(),
		"#ip":             c.RealIP,
		"#time":           time.Now(),
	}

	maps.Copy(base, info)

	threading.GoSafe(func() {
		err := te.TeClient.Track(fmt.Sprintf("%v", gUser.UID), gUser.Did, eventName, base)
		if err != nil {
			logger.Errorf("TrackWithBaseAttributes err:%v", err)
			SendServerAlarmMsg(ctx, gUser.UID, c.Country, fmt.Sprintf("TrackWithBaseAttributes err:%v", err))
		}
		logger.InfoWithCtx(ctx, "TrackWithBaseAttributes event finish", zap.String("eventName", eventName),
			zap.String("base", ToJsonString(base)))
	})

}

func ToJsonString(v interface{}) string {
	b, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(b)
}

// 服务端告警群
func SendServerAlarmMsg(ctx context.Context, userId int64, country, text string) {
	text = fmt.Sprintf("%s\r\n\r\ntime: %v", text, time.Now().Format("2006-01-02 15:04:05"))
	if config.Configuration.Env != "" {
		text += fmt.Sprintf("\r\nenv: %s", config.Configuration.Env)
	}
	var traceId = ctx.Value(logger.TraceIDKey)
	if traceId != "" {
		text += fmt.Sprintf("\r\ntraceId: %s", traceId)
	}
	hostName, _ := os.Hostname()
	if hostName != "" {
		text += fmt.Sprintf("\r\nhost: %s", hostName)
	}
	if country != "" {
		text += fmt.Sprintf("\r\ncountry: %s", country)
	}
	msg := lark.NewMsgBuffer(lark.MsgText).Text(text).Build()
	_, err := bot.ServerAlarmClient.PostNotificationV2(msg)
	if err != nil {
		logx.Errorf("bot SendProductAlarmMsg error:%v", err)
	}
}

func SendServerAlarmText(ctx context.Context, text string) {
	SendServerAlarmMsg(ctx, 0, "", text)
}
