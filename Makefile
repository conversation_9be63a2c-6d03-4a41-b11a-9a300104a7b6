.PHONY: clean fmt

GIT_COMMIT=`git show -s --format=%H`
BUILD_TIME=`date '+%Y-%m-%d %H:%M:%S'`

install: cmd/main.go
	@make fmt
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -tags=jsoniter -ldflags \
"-s -w \
-X 'git.hoxigames.xyz/movely/movely-server.BuildTime=$(BUILD_TIME)' \
-X git.hoxigames.xyz/movely/movely-server.Git=$(GIT_COMMIT)" \
-o bin/main cmd/main.go

clean:
	@rm -rf bin/*

fmt:
	@gofmt -s -w .

