package main

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"git.hoxigames.xyz/movely/movely-server/dao/model"
	"git.hoxigames.xyz/movely/movely-server/internal/common"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

const TableNameConfigsVideo = "configs_video"

// ConfigsVideo mapped from table <configs_video>
type ConfigsVideo struct {
	ID                int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	GenerateType      string    `gorm:"column:generate_type;type:varchar(255);not null;comment:视频模版类型" json:"generate_type"`                                   // 视频模版类型
	Thirdparty        string    `gorm:"column:thirdparty;type:varchar(255);not null;comment:调用的第三方平台" json:"thirdparty"`                                       // 调用的第三方平台
	InferParams       string    `gorm:"column:infer_params;type:mediumtext;comment:生成相关推理参数" json:"infer_params"`                                              // 生成相关推理参数
	Prompt            string    `gorm:"column:prompt;type:text;comment:视频生成提示词" json:"prompt"`                                                                 // 视频生成提示词
	NegativePrompt    string    `gorm:"column:negative_prompt;type:text;comment:视频生成反向提示词" json:"negative_prompt"`                                             // 视频生成反向提示词
	CfgScale          float32   `gorm:"column:cfg_scale;type:float;not null;default:0.5;comment:视频生成自由度，取值范围闭区间[0,1]" json:"cfg_scale"`                        // 视频生成自由度，取值范围闭区间[0,1]
	Status            int32     `gorm:"column:status;type:tinyint;not null;comment:-1:隐藏 1:展示" json:"status"`                                                  // -1:隐藏 1:展示
	EditStatus        int32     `gorm:"column:edit_status;type:tinyint;not null;comment:1:已同步 2:新增 3:有修改" json:"edit_status"`                                  // 1:已同步 2:新增 3:有修改
	Title             string    `gorm:"column:title;type:varchar(255);not null;comment:视频模版标题，也是多语言key" json:"title"`                                          // 视频模版标题，也是多语言key
	Image             string    `gorm:"column:image;type:varchar(255);not null;comment:视频模板封面图URL" json:"image"`                                               // 视频模板封面图URL
	ImageMedium       string    `gorm:"column:image_medium;type:varchar(255);not null;comment:视频模板封面图URL，中等分辨率" json:"image_medium"`                           // 视频模板封面图URL，中等分辨率
	ImageLow          string    `gorm:"column:image_low;type:varchar(255);not null;comment:视频模板封面图URL，低分辨率" json:"image_low"`                                  // 视频模板封面图URL，低分辨率
	ImageBak          string    `gorm:"column:image_bak;type:varchar(255);not null;comment:视频模板封面图URL备份" json:"image_bak"`                                     // 视频模板封面图URL备份
	Video             string    `gorm:"column:video;type:varchar(255);not null;comment:视频模板封面视频URL" json:"video"`                                              // 视频模板封面视频URL
	VideoBak          string    `gorm:"column:video_bak;type:varchar(255);not null;comment:视频模板封面视频URL备份" json:"video_bak"`                                    // 视频模板封面视频URL备份
	VideoMedium       string    `gorm:"column:video_medium;type:varchar(255);not null;comment:视频模板封面视频URL，中等分辨率" json:"video_medium"`                          // 视频模板封面视频URL，中等分辨率
	VideoLow          string    `gorm:"column:video_low;type:varchar(255);not null;comment:视频模板封面视频URL，低分辨率" json:"video_low"`                                 // 视频模板封面视频URL，低分辨率
	VideoWidth        int32     `gorm:"column:video_width;type:int;not null;comment:视频宽度" json:"video_width"`                                                  // 视频宽度
	VideoHeight       int32     `gorm:"column:video_height;type:int;not null;comment:视频高度" json:"video_height"`                                                // 视频高度
	Blurhash          string    `gorm:"column:blurhash;type:varchar(255);not null;comment:视频加载占位图" json:"blurhash"`                                            // 视频加载占位图
	Autoplay          int32     `gorm:"column:autoplay;type:tinyint;not null;comment:是否自动播放 -1:否 1:是" json:"autoplay"`                                         // 是否自动播放 -1:否 1:是
	HideInReviewing   int32     `gorm:"column:hide_in_reviewing;type:tinyint;not null;comment:是否在审核时隐藏 -1:否 1:是" json:"hide_in_reviewing"`                     // 是否在审核时隐藏 -1:否 1:是
	FreeTrial         int32     `gorm:"column:free_trial;type:tinyint;not null;comment:是否允许免费试用 -1:否 1:是" json:"free_trial"`                                   // 是否允许免费试用 -1:否 1:是
	Tags              string    `gorm:"column:tags;type:text;comment:视频模板标签" json:"tags"`                                                                      // 视频模板标签
	DevPublishTime    time.Time `gorm:"column:dev_publish_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:模板dev环境发布时间" json:"dev_publish_time"` // 模板dev环境发布时间
	PublishTime       time.Time `gorm:"column:publish_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:模板发布时间" json:"publish_time"`              // 模板发布时间
	CreateTime        time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime        time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`
	VideoType         string    `gorm:"column:video_type;type:varchar(255);not null;comment:视频类型" json:"video_type"`                         // 视频类型
	LikeCount         int64     `gorm:"column:like_count;type:bigint;not null;comment:点赞数量" json:"like_count"`                               // 点赞数量
	BaseLikeCount     int64     `gorm:"column:base_like_count;type:bigint;not null;comment:基础点赞数量" json:"base_like_count"`                   // 基础点赞数量
	AspectRatio       string    `gorm:"column:aspect_ratio;type:varchar(32);not null;comment:长宽比" json:"aspect_ratio"`                       // 长宽比
	Id2videoImageList string    `gorm:"column:id2video_image_list;type:text;comment:id生视频的默认图片" json:"id2video_image_list"`                  // id生视频的默认图片
	PretreatType      string    `gorm:"column:pretreat_type;type:varchar(120);comment:预处理类型" json:"pretreat_type"`                           // 预处理类型
	PretreatParams    string    `gorm:"column:pretreat_params;type:mediumtext;comment:预处理参数" json:"pretreat_params"`                         // 预处理参数
	ClipType          string    `gorm:"column:clip_type;type:varchar(120);not null;comment:裁剪类型" json:"clip_type"`                           // 裁剪类型
	ImageExp          string    `gorm:"column:image_exp;type:varchar(255);not null;comment:视频模板封面图老用户URL" json:"image_exp"`                  // 视频模板封面图老用户URL
	VideoExp          string    `gorm:"column:video_exp;type:varchar(255);not null;comment:视频模板老用户URL" json:"video_exp"`                     // 视频模板老用户URL
	VideoExpMedium    string    `gorm:"column:video_exp_medium;type:varchar(255);not null;comment:视频模板老用户URL，中等分辨率" json:"video_exp_medium"` // 视频模板老用户URL，中等分辨率
	VideoExpLow       string    `gorm:"column:video_exp_low;type:varchar(255);not null;comment:视频模板老用户URL，低分辨率" json:"video_exp_low"`        // 视频模板老用户URL，低分辨率
	MasterTemplate    string    `gorm:"column:master_template;type:varchar(255);not null;comment:用于变种模板的源头" json:"master_template"`          // 用于变种模板的源头
	IDImage           string    `gorm:"column:id_image;type:varchar(255);not null;comment:id2image 模板 ID 图" json:"id_image"`                 // id2image 模板 ID 图
	AbParams          string    `gorm:"column:ab_params;type:text;comment:AB测试参数" json:"ab_params"`                                          // AB测试参数
}

// TableName ConfigsVideo's table name
func (*ConfigsVideo) TableName() string {
	return TableNameConfigsVideo
}

func initMysql(dsn string, maxLifeTime, maxIdleConn, maxOpenConn int) (*gorm.DB, error) {
	client, err := gorm.Open(mysql.New(mysql.Config{DSN: dsn}), &gorm.Config{})
	if err != nil {
		log.Fatal("mysql open", zap.String("dsn", dsn), zap.Error(err))
		return nil, err
	}
	db, err := client.DB()
	if err != nil {
		log.Fatal("mysql db", zap.String("dsn", dsn), zap.Error(err))
		return nil, err
	}
	db.SetMaxIdleConns(maxIdleConn)
	db.SetMaxOpenConns(maxOpenConn)
	db.SetConnMaxLifetime(time.Second * time.Duration(maxLifeTime))
	return client, nil
}

var (
	movelyDev = "root:root@tcp(10.228.1.232:3306)/movely_dev?charset=utf8mb4&parseTime=True&loc=Local&interpolateParams=True&loc=Local"
	ifonlydev = "vis_live:6LT4xHs7cMMyKKyAZt1H@tcp(10.228.11.142:3306)/if_only_dev_db?charset=utf8mb4&parseTime=True&loc=Local&interpolateParams=True&loc=Local"
)

func Tag(movelyclient *gorm.DB, tag string) {
	configsVideo := []model.ConfigsVideo{}
	movelyclient.Model(&model.ConfigsVideo{}).Offset(rand.Intn(20) + 10).Limit(rand.Intn(20)).Find(&configsVideo)
	for _, config := range configsVideo {
		movelyclient.Model(&model.TemplateTag{}).Create(&model.TemplateTag{
			GenerateType: config.GenerateType,
			TagKey:       tag,
		})
	}
}

func Move(movelyclient *gorm.DB) {
	ifonlyclient, err := initMysql(ifonlydev, 60, 10, 10)
	if err != nil {
		log.Fatal("mysql init", zap.Error(err))
	}
	configsVideo := []ConfigsVideo{}
	err = ifonlyclient.Model(&ConfigsVideo{}).Find(&configsVideo).Error
	if err != nil {
		log.Fatal("mysql find failed", zap.Error(err))
	}
	for _, config := range configsVideo {
		if config.VideoType == "id2video" {
			continue
		}
		err = movelyclient.Model(&model.ConfigsVideo{}).Create(&model.ConfigsVideo{
			ID:                config.ID,
			GenerateType:      config.GenerateType,
			Thirdparty:        config.Thirdparty,
			InferParams:       config.InferParams,
			Prompt:            config.Prompt,
			NegativePrompt:    config.NegativePrompt,
			CfgScale:          config.CfgScale,
			Status:            config.Status,
			EditStatus:        config.EditStatus,
			Title:             config.Title,
			Image:             config.Image,
			ImageMedium:       config.ImageMedium,
			ImageLow:          config.ImageLow,
			Video:             config.Video,
			VideoMedium:       config.VideoMedium,
			VideoLow:          config.VideoLow,
			VideoWidth:        config.VideoWidth,
			VideoHeight:       config.VideoHeight,
			Blurhash:          config.Blurhash,
			Autoplay:          config.Autoplay,
			HideInReviewing:   config.HideInReviewing,
			FreeTrial:         config.FreeTrial,
			DevPublishTime:    config.DevPublishTime,
			PublishTime:       config.PublishTime,
			CreateTime:        config.CreateTime,
			UpdateTime:        config.UpdateTime,
			VideoType:         config.VideoType,
			LikeCount:         config.LikeCount,
			BaseLikeCount:     config.BaseLikeCount,
			AspectRatio:       config.AspectRatio,
			PretreatType:      config.PretreatType,
			PretreatParams:    config.PretreatParams,
			ClipType:          config.ClipType,
			MasterTemplate:    config.MasterTemplate,
			AbParams:          config.AbParams,
			TemplateLevel:     int32(rand.Intn(4) + 1),
			ManuallyPushToTop: int32(rand.Intn(2)),
		}).Error
		if err != nil {
			fmt.Println(err)
		}
		// err = movelyclient.Model(&model.TemplateImage{}).Create(&model.TemplateImage{
		// 	ID:           config.ID,
		// 	GenerateType: config.GenerateType,
		// 	Image:        config.Image,
		// 	ImageMedium:  config.ImageMedium,
		// 	ImageLow:     config.ImageLow,
		// }).Error
		// if err != nil {
		// 	fmt.Println(err)
		// }
	}
}

func main() {
	movelyclient, err := initMysql(movelyDev, 60, 10, 10)
	if err != nil {
		log.Fatal("mysql init", zap.Error(err))
	}
	// Move(movelyclient)
	Tag(movelyclient, common.ShelfTrendingKey)
	Tag(movelyclient, common.ShelfHotMovesKey)
	Tag(movelyclient, "tag_shelf1")
	Tag(movelyclient, "tag_shelf2")
	Tag(movelyclient, "tag_shelf3")
}
