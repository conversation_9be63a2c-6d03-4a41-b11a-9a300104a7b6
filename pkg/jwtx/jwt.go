package jwtx

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type (
	Option = func(claims *Claims)
)

type Secret string

const (
	Refresh Secret = "MoMBfUw7?.t#(DYZ{!waZd=wJ)M)z"
	Access  Secret = "W:e5jygG8|,0GR7:.xXv"
)

func WithTTL(ttl time.Time) Option {
	return func(c *Claims) {
		c.ExpiresAt = jwt.NewNumericDate(ttl)
	}
}

func Generate(accountId int64, secret Secret, opts ...Option) (jwtToken string, err error) {
	exp := time.Hour * 24 * 3
	now := time.Now()
	claims := Claims{
		AccountId: accountId,
		UserId:    strconv.FormatInt(accountId, 10),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(exp)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}
	for _, opt := range opts {
		opt(&claims)
	}

	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(secret))
}

func Parse(token string, secret Secret) (claims Claims, err error) {
	t, err := jwt.ParseWithClaims(token, &claims, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	if err == nil && !t.Valid {
		err = errors.New("invalid Token")
	}
	return
}

type Claims struct {
	AccountId int64           `json:"account_id"`
	UserId    string          `json:"user_id"`
	Data      json.RawMessage `json:"data"`
	jwt.RegisteredClaims
}
