package app

import (
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
)

// ServerType 服务类型
type (
	ServerType string

	HandlerFunc func(c *Context)
	Context     struct {
		g               *gin.Context
		StartTime       time.Time
		SpanID          int
		App             string
		IDFA            string
		IMEI            string
		DownloadChannel string
		Version         string
		Lang            string
		Country         string
		Platform        string
		TraceID         string
		RemoteIP        string
		RealIP          string
		XForwardFor     string
		UserAgent       string
		Manufacturer    string
		Caller          string
		Authorization   string
		Device          string
		DistinctId      string
		AppVersionSdk   int64
	}
	Response struct {
		Code    int         `json:"code"`
		Msg     string      `json:"msg"`
		TraceID string      `json:"trace_id"`
		Data    interface{} `json:"data,omitempty"`
		LastID  int64       `json:"last_id,omitempty"`
		More    bool        `json:"more,omitempty"`
	}

	Trace struct {
		time int64
		step int64
	}
)

const (
	GinCtx = "gin-ctx"

	ExServer    ServerType = "ex-s"
	InServer    ServerType = "in-s"
	AdminServer ServerType = "ad-s"

	UnknownIP    = "00000000"
	DefaultStep  = 1
	DefaultStart = 1

	App           = "X-app"
	IDFA          = "X-idfa"
	IMEI          = "X-imei"
	Version       = "X-App-Version"
	SDKVersion    = "X-App-Version-Sdk"
	Language      = "X-User-Lang"
	Country       = "X-User-Country"
	Platform      = "X-Channel" // 数数 OS name
	DC            = "X-dc"
	Device        = "X-Device"
	DistinctId    = "X-Distinct-Id"
	Manufacturer  = "X-manufacturer"
	TraceID       = "trace-id"
	XRealIP       = "X-Real-Ip"
	XForwardedFOR = "X-Forwarded-For"
	Caller        = "Caller"
	Authorization = "Authorization"
	UserAgent     = "User-Agent"
	SecChUaMobile = "sec-ch-ua-mobile"
)

var (
	localIP string

	mu    *sync.Mutex
	trace *Trace
)

func ExternalHandler(h HandlerFunc) gin.HandlerFunc {
	return func(g *gin.Context) {
		c, _ := NewExternalContext(g)
		h(c)
	}
}

func InternalHandler(h HandlerFunc) gin.HandlerFunc {
	return func(g *gin.Context) {
		c, _ := NewInternalContext(g)
		h(c)
	}
}

var log *zap.SugaredLogger

func Init(logger *zap.SugaredLogger) {
	log = logger
	mu = &sync.Mutex{}

	address, err := net.InterfaceAddrs()
	if err == nil {
		arr := make([]string, 4)
		for _, addr := range address {
			if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
				if ipNet.IP.To4() != nil {
					arr = strings.Split(ipNet.IP.String(), ".")
				}
			}
		}

		for _, v := range arr {
			n, err := strconv.Atoi(v)
			if err != nil {
				localIP += "--"
				continue
			}
			localIP += fmt.Sprintf("%02x", n)
		}
	}
	if localIP == "" {
		localIP = UnknownIP
	}

	trace = &Trace{step: DefaultStart, time: time.Now().UnixMilli()}
}

func clientIp(r *http.Request) string {
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0])
	if ip != "" {
		return ip
	}

	ip = strings.TrimSpace(r.Header.Get("X-Real-Ip"))
	if ip != "" {
		return ip
	}

	if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
		return ip
	}
	return ""
}

func NewExternalContext(g *gin.Context) (ctx *Context, err error) {
	// 如果存在，直接返回
	if c, exists := g.Get(GinCtx); exists {
		ctx = c.(*Context)
		return
	}

	remoteIP, _, _ := net.SplitHostPort(g.Request.RemoteAddr)
	realIP := clientIp(g.Request)
	xForward := g.Request.Header.Get(XForwardedFOR)
	traceID := genTraceID(g.Request.Header.Get(TraceID), ExServer)

	device := g.Request.Header.Get(Device)
	distinctId := g.Request.Header.Get(DistinctId)

	app := g.Request.Header.Get(App)
	idfa := g.Request.Header.Get(IDFA)
	imei := g.Request.Header.Get(IMEI)
	version := g.Request.Header.Get(Version)
	lang := g.Request.Header.Get(Language)
	platform := g.Request.Header.Get(Platform)
	downloadChannel := g.Request.Header.Get(DC)
	manufacturer := g.Request.Header.Get(Manufacturer)
	authorization := g.Request.Header.Get(Authorization)
	appVersionSdk, _ := strconv.ParseInt(g.Request.Header.Get(SDKVersion), 10, 64)

	var country string = g.Request.Header.Get(Country)
	if country == "" {
		if list := strings.Split(lang, "-"); len(list) > 0 {
			country = list[0]
		}
	}
	logger.Debug("NewExternalContext", zap.String("distinctId", distinctId), zap.Int64("appVersionSdk", appVersionSdk),
		zap.String("osname", platform), zap.String("country", country), zap.String("realIP", realIP), zap.String("lang", Language))
	ctx = &Context{
		g:               g,
		StartTime:       time.Now(),
		TraceID:         traceID,
		App:             app,
		DownloadChannel: downloadChannel,
		IDFA:            idfa,
		IMEI:            imei,
		Version:         version,
		Lang:            lang,
		Country:         country,
		Platform:        platform,
		RemoteIP:        remoteIP,
		RealIP:          realIP,
		XForwardFor:     xForward,
		UserAgent:       g.Request.UserAgent(),
		Manufacturer:    manufacturer,
		Authorization:   authorization,
		Device:          device,
		DistinctId:      distinctId,
		AppVersionSdk:   appVersionSdk,
	}
	return
}

func NewInternalContext(g *gin.Context) (ctx *Context, err error) {
	// 如果存在，直接返回
	if c, exists := g.Get(GinCtx); exists {
		ctx = c.(*Context)
		return
	}

	remoteIP, _, _ := net.SplitHostPort(g.Request.RemoteAddr)
	realIP := g.Request.Header.Get(XRealIP)
	xForward := g.Request.Header.Get(XForwardedFOR)
	traceID := genTraceID(g.Request.Header.Get(TraceID), InServer)
	caller := g.Request.Header.Get(Caller)

	ctx = &Context{
		g:           g,
		StartTime:   time.Now(),
		TraceID:     traceID,
		RemoteIP:    remoteIP,
		RealIP:      realIP,
		XForwardFor: xForward,
		UserAgent:   g.Request.UserAgent(),
		Country:     "zh",
		Caller:      caller,
	}
	return
}

// Reset 重置
func (c *Context) Reset() {
	c.g = nil
}

func NewTraceID() string {
	return genTraceID("", "")
}

/*
* traceID生成规范

* 8位服务器IP
* 13位毫秒时间戳
* 不定长自增ID
* 服务类型
 */
func genTraceID(traceID string, serverType ServerType) string {
	if len(traceID) > 0 {
		return traceID
	}
	var step int64
	now := time.Now().UnixMilli()
	mu.Lock()
	if now == trace.time {
		trace.step = trace.step + DefaultStep
	} else {
		trace.step = DefaultStart
	}
	trace.time = now
	step = trace.step
	mu.Unlock()
	return fmt.Sprintf("%02x%d%d%s", localIP, now, step, serverType)
}

func (c *Context) GetTraceID() string {
	return c.TraceID
}

func (c *Context) GetSpanID() string {
	c.SpanID++
	return strconv.Itoa(c.SpanID)
}

func (c *Context) Set(key string, value interface{}) {
	c.g.Set(key, value)
}

func (c *Context) Get(key string) (interface{}, bool) {
	return c.g.Get(key)
}

func (c *Context) GetGinContext() *gin.Context {
	return c.g
}

func (c *Context) ShouldBind(obj any) error {
	return c.g.ShouldBind(obj)
}

func (c *Context) GetQuery(key string) (string, bool) {
	return c.g.GetQuery(key)
}

func (c *Context) ShouldBindBodyWith(obj any, bb binding.BindingBody) error {
	return c.g.ShouldBindBodyWith(obj, bb)
}

func (c *Context) Success(data interface{}) {
	resp := &Response{
		Code:    0,
		Msg:     "ok",
		TraceID: c.TraceID,
		Data:    data,
	}
	if log != nil {
		_ = c.g.Request.ParseForm()
		log.Info("http success", zap.String("traceId", c.GetTraceID()), zap.Any("data", c.logFields("success")))
	}
	c.g.JSON(http.StatusOK, resp)
}

func (c *Context) RespJSON(httpCode int, resp interface{}) {
	if log != nil {
		_ = c.g.Request.ParseForm()
		log.Info("http success json", zap.String("traceId", c.GetTraceID()), zap.Any("data", c.logFields("success json")))
	}
	c.g.JSON(httpCode, resp)
}

func (c *Context) SuccessNil() {
	resp := &Response{
		Code:    0,
		Msg:     "ok",
		TraceID: c.TraceID,
	}
	if log != nil {
		_ = c.g.Request.ParseForm()
		log.Info("http successNil", zap.String("traceId", c.GetTraceID()), zap.Any("data", c.logFields("successNil")))
	}
	c.g.JSON(http.StatusOK, resp)
}

func (c *Context) Fail(httpCode, code int, msg string, err error) {
	resp := Response{
		Code:    code,
		Msg:     msg,
		TraceID: c.TraceID,
	}
	if log != nil {
		_ = c.g.Request.ParseForm()
		log.Error("http fail", zap.Error(err), zap.String("traceId", c.GetTraceID()), zap.Any("data", c.logFields("Failed")))
	}
	c.g.JSON(httpCode, resp)
}

func (c *Context) Failed(code int, msg string, err error) {
	resp := &Response{
		Code:    code,
		Msg:     msg,
		TraceID: c.TraceID,
	}
	if log != nil {
		_ = c.g.Request.ParseForm()
		log.Error("http failed", zap.String("traceId", c.GetTraceID()), zap.Any("data", c.logFields("Failed")))
	}
	c.g.JSON(http.StatusInternalServerError, resp)
}

func (c *Context) logFields(mark string) map[string]interface{} {
	return map[string]interface{}{
		"method":       c.g.Request.Method,
		"real_ip":      c.RealIP,
		"remote_ip":    c.RemoteIP,
		"path":         c.g.Request.URL.Path,
		"get":          c.g.Request.URL.RawQuery,
		"post":         c.g.Request.PostForm,
		"timestamp":    c.StartTime.UnixMilli(),
		"duration(ms)": int64(time.Since(c.StartTime) / time.Millisecond),
		"result":       mark,
		"caller":       c.Caller,
	}
}

func BuildExternalCtx() gin.HandlerFunc {
	return func(c *gin.Context) {
		context, err := NewExternalContext(c)
		if err != nil || context == nil {
			c.JSON(http.StatusInternalServerError, map[string]interface{}{"code": 1, "msg": "internal server error"})
			c.Abort()
			return
		}
		c.Set(GinCtx, context)
		c.Next()
		// 打请求结束日志

		// End 最后收尾，后续不允许有任何操作
		context.Reset()
	}
}
