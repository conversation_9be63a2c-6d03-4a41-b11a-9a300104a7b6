package bot

import (
	"github.com/go-lark/lark"
)

// 机器人飞书文档：https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot
var (
	// ProductAlarmClient *lark.Bot
	ServerAlarmClient *lark.Bot
	// ConfigNoticeClient *lark.Bot
)

func InitProductAlarmBot() {
	// movely 后端告警
	// ServerAlarmClient = lark.NewNotificationBot("https://open.feishu.cn/open-apis/bot/v2/hook/7d710218-9a3a-40db-9ab9-bc89b8f84035")

	// test
	ServerAlarmClient = lark.NewNotificationBot("https://open.feishu.cn/open-apis/bot/v2/hook/50cd7818-15b2-441a-9f5d-67686ab7c200")
}
