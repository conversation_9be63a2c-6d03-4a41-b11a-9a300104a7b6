package te

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"

	"github.com/ThinkingDataAnalytics/go-sdk/v2/src/thinkingdata"
	"github.com/google/uuid"
)

const (
	ServerUrl = "http://te-internal.receive.ifonlyapp.com:8991" // https://te.receive.ifonlyapp.com
	AppId     = "5b75973ee0a94e14ab5a7b2baf15ebd9"
)

type TE struct {
	thinkingdata.TDAnalytics
	token      string
	httpClient *http.Client
}

var TeClient *TE

func Init(cfg config.TEConfig) {
	logger.Info("NewTE config", zap.Any("cfg", cfg))
	var consumer thinkingdata.TDConsumer
	var err error
	if cfg.UseLogBus {
		consumer, err = thinkingdata.NewLogConsumerWithConfig(thinkingdata.TDLogConsumerConfig{
			Directory:      cfg.Directory,
			FileNamePrefix: fmt.Sprintf("%s_%s", cfg.Prefix, uuid.New()),
		})
	} else if cfg.UseBatch {
		consumer, err = thinkingdata.NewBatchConsumerWithConfig(thinkingdata.TDBatchConfig{
			ServerUrl:     ServerUrl,
			AppId:         AppId,
			Compress:      true,
			AutoFlush:     true, // Default false
			Interval:      5,    // Default 30
			CacheCapacity: 1000, // Default 50
			Timeout:       5000,
		})
	} else {
		consumer, err = thinkingdata.NewDebugConsumerWithWriter(ServerUrl, AppId, false)
	}
	if err != nil {
		panic(err)
	}
	TeClient = &TE{
		TDAnalytics: thinkingdata.New(consumer),
		token:       cfg.Token,
		httpClient: &http.Client{
			Transport: &http.Transport{
				MaxIdleConns:        30,               // 最大空闲连接数
				MaxIdleConnsPerHost: 10,               // 每个host的最大空闲连接数
				IdleConnTimeout:     90 * time.Second, // 空闲连接超时时间
				MaxConnsPerHost:     100,
			},
			Timeout: 60 * time.Second,
		},
	}
}

func (l *TE) Start() {
}

func (l *TE) Stop() {
	logger.Info("TE Starting graceful shutdown")
	time.Sleep(1 * time.Second)
	err := l.Close()
	if err != nil {
		logx.Error(err)
	}
	logx.Info("TE Exited")
}

func (l *TE) QuerySql(rawString string) ([]byte, error) {
	encodedString := url.QueryEscape(rawString)
	url := fmt.Sprintf(ServerUrl+"/querySql?token=%s&format=json&timeoutSeconds=10&sql=%s", l.token, encodedString)
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	rsp, err := l.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()

	bytes, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	return bytes, nil
}
