package meilisearch

import (
	"fmt"
	"os"
	"testing"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"github.com/meilisearch/meilisearch-go"
)

func TestSearch(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	Init()
	// rdb.Init()
	addDocuments := make([]map[string]any, 0)
	doc := map[string]any{
		"id":            201,
		"generate_type": "video_gen_shake_booty",
		"prompt":        "In a lively dance setting, a woman confidently sways her hips to the rhythm, her movements both rhythmic and enticing. Her playful sensuality captivates the room, creating an atmosphere charged with excitement and allure. Her expression of pure enjoyment adds to the scene's magnetic appeal.",
		"tags":          "booty",
		"cover_image":   "https://cdn.aipicplus.com/assets/video_gen/video_gen_shake_booty_20250317164230.jpeg",
		"_vectors": map[string]any{
			"default": map[string]any{
				"regenerate": true,
			},
		},
	}
	addDocuments = append(addDocuments, doc)
	doc = map[string]any{
		"id":            202,
		"generate_type": "video_gen_showing_off",
		"prompt":        "A model turning around to show the outfit, First pause when showing the side, then show the back，Model standing pose",
		"tags":          "showing off",
		"cover_image":   "https://cdn.aipicplus.com/assets/video_gen/video_gen_showing_off_20250224151844.jpeg",
		"_vectors": map[string]any{
			"default": map[string]any{
				"regenerate": true,
			},
		},
	}
	fmt.Println("add doc")
	addDocuments = append(addDocuments, doc)
	taskinfo, err := Meilisearch.index.AddDocuments(&addDocuments, "id")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(taskinfo)
	task, err := Meilisearch.index.GetTask(taskinfo.TaskUID)
	if err != nil {
		t.Fatal(err)
	}
	id := taskinfo.TaskUID
	t.Log(task)
	for task.Status == "enqueued" {
		time.Sleep(time.Second)
		task, err = Meilisearch.index.GetTask(id)
		if err != nil {
			t.Fatal(err)
		}
		t.Log(task.Status)
		t.Log(task.Details.CanceledTasks)
		t.Log(task.Type)
		t.Log(task)
	}
	// doc = make(map[string]any)
	// fmt.Println("get doc")
	// err = Meilisearch.index.GetDocument("201", &meilisearch.DocumentQuery{
	// 	Fields: []string{"id", "generate_type", "prompt", "tags", "cover_image"},
	// }, &doc)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// t.Log(doc)
}

func TestGetIndexes(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	meilisearchClient := meilisearch.New(config.Configuration.MeilisearchConfig.Host,
		meilisearch.WithAPIKey(config.Configuration.MeilisearchConfig.MasterKey))
	// task, err := meilisearchClient.DeleteIndex("movely_template")
	// fmt.Println(task, err)
	indexes, err := meilisearchClient.GetRawIndexes(&meilisearch.IndexesQuery{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(indexes)
}

func TestGettask(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	Init()
	task, err := Meilisearch.index.GetTask(295)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(task)
}

func TestGetDoc(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	meilisearchClient := meilisearch.New(config.Configuration.MeilisearchConfig.Host,
		meilisearch.WithAPIKey(config.Configuration.MeilisearchConfig.MasterKey))

	docs, err := meilisearchClient.Index("movely_template").Search("臀部", &meilisearch.SearchRequest{
		ShowRankingScore: true,
		Hybrid: &meilisearch.SearchRequestHybrid{
			Embedder: "default",
		},
		// RetrieveVectors: true,
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, doc := range docs.Hits {
		fmt.Println(doc)
	}
}

func TestCreatIndex(t *testing.T) {
	os.Setenv("ENVIRON", "dev")
	config.Init("../../etc/app/dev.json")
	meilisearchClient := meilisearch.New(config.Configuration.MeilisearchConfig.Host,
		meilisearch.WithAPIKey(config.Configuration.MeilisearchConfig.MasterKey))
	meilisearchClient.CreateIndex(&meilisearch.IndexConfig{
		Uid:        "movely_template",
		PrimaryKey: "id",
	})
}
