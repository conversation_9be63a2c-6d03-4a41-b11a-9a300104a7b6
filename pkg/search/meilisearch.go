package meilisearch

import (
	"git.hoxigames.xyz/movely/movely-server/config"
	"github.com/meilisearch/meilisearch-go"
	"github.com/mitchellh/mapstructure"
)

type MeiliSearch struct {
	meilisearchClient meilisearch.ServiceManager
	index             meilisearch.IndexManager
}

var Meilisearch *MeiliSearch

func Init() {
	meilisearchClient := meilisearch.New(config.Configuration.MeilisearchConfig.Host,
		meilisearch.WithAPIKey(config.Configuration.MeilisearchConfig.MasterKey))
	settings := meilisearch.Settings{
		Embedders: map[string]meilisearch.Embedder{
			"default": {
				Source: "rest",
				APIKey: "sk-atlfvwjzwkwhvuadierehembgdkvjnakqnafzpfcnblncugg",
				URL:    "https://api.siliconflow.cn/v1/embeddings",
				Request: map[string]interface{}{
					"input":           "{{text}}",
					"model":           "BAAI/bge-m3",
					"encoding_format": "float",
				},
				Response: map[string]interface{}{
					"data": []map[string]interface{}{
						{
							"embedding": "{{embedding}}",
						},
					},
				},
				Headers: map[string]string{
					"Authorization": "Bearer sk-atlfvwjzwkwhvuadierehembgdkvjnakqnafzpfcnblncugg",
					"Content-Type":  "application/json",
				},
			},
		},
	}
	index := meilisearchClient.Index(config.Configuration.MeilisearchConfig.Index)
	_, err := index.UpdateSettings(&settings)
	if err != nil {
		panic(err)
	}
	Meilisearch = &MeiliSearch{
		meilisearchClient: meilisearchClient,
		index:             index,
	}
}

func (m *MeiliSearch) AddDocuments(docs []map[string]any) (*meilisearch.TaskInfo, error) {
	return m.index.AddDocuments(docs, "id")
}

func (m *MeiliSearch) DeleteDocuments(docs []string) (*meilisearch.TaskInfo, error) {
	return m.index.DeleteDocuments(docs)
}

func (m *MeiliSearch) SearchDocuments(query string, limit int64) ([]Hits, error) {
	searchRes, err := m.index.Search(query,
		&meilisearch.SearchRequest{
			Hybrid: &meilisearch.SearchRequestHybrid{
				Embedder: "default",
			},
			ShowRankingScore:      true,
			Limit:                 limit,
			RankingScoreThreshold: 0.75,
		})
	if err != nil {
		return nil, err
	}
	var hits []Hits
	err = mapstructure.Decode(&searchRes.Hits, &hits)
	if err != nil {
		return nil, err
	}
	return hits, nil
}

type Hits struct {
	GenerateType string  `mapstructure:"generate_type"`
	RankingScore float64 `mapstructure:"_rankingScore"`
	Id           int64   `mapstructure:"id"`
}
