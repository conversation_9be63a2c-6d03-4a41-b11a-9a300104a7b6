package s3

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

const (
	Bucket = "movelyai"

	Image = "images"
)

func GetImageDir(evnTag string, userId int64) string {
	return fmt.Sprintf("%s/%s/%v/%v", evnTag, Image, userId%1000, userId)
}

func GetImage<PERSON>ey(userId int64) string {
	return fmt.Sprintf("%v_%v_%v", userId, time.Now().UnixMilli(), rand.Intn(100000))
}

func FormatURL(bucket, key string) string {
	return "s3://" + bucket + "/" + key
}

type Path struct {
	Bucket string
	Key    string
	URL    string
}

func ParseURL(url string) (*Path, error) {
	if !strings.HasPrefix(url, "s3://") {
		return nil, fmt.Errorf("无效的 s3 url: %s", url)
	}
	path := strings.TrimPrefix(url, "s3://")
	parts := strings.SplitN(path, "/", 2)
	if len(parts) < 2 {
		return nil, fmt.Errorf("无效的 s3 路径: %s", url)
	}

	return &Path{
		Bucket: parts[0],
		Key:    parts[1],
		URL:    url,
	}, nil
}
