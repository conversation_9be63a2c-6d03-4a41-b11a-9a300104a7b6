package s3

import (
	"io"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"go.uber.org/zap"
)

var (
	Cli *client
)

type client struct {
	region     string
	session    *session.Session
	srv        *s3.S3
	downloader *s3manager.Downloader
	uploader   *s3manager.Uploader
}

func Init() (err error) {
	Cli, err = newClient(
		config.Configuration.Aws.S3.Srv.Region,
		config.Configuration.Aws.S3.Srv.AccessKey,
		config.Configuration.Aws.S3.Srv.SecretAccessKey)
	if err != nil {
		logger.Error("aws client error", zap.Error(err))
		return err
	}
	return nil
}

func newClient(region, accessKey, secretAccessKey string) (*client, error) {
	cfg := &aws.Config{
		Credentials: credentials.NewStaticCredentials(accessKey, secretAccessKey, ""),
		Region:      aws.String(region),
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return nil, err
	}

	return &client{
		region:     region,
		session:    sess,
		srv:        s3.New(sess),
		downloader: s3manager.NewDownloader(sess),
		uploader:   s3manager.NewUploader(sess),
	}, nil
}

func (c *client) Upload(bucket, key string, data io.Reader, contentType string) (*s3manager.UploadOutput, error) {

	return c.uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(bucket),
		Key:         aws.String(key),
		Body:        data,
		ContentType: aws.String(contentType),
	})
}

func (c *client) GetObject(bucket, key string) (*s3.GetObjectOutput, error) {
	return c.srv.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
}
