package sf

import (
	"fmt"
	"net"
	"strconv"
	"strings"

	"git.hoxigames.xyz/movely/movely-server/pkg/logger"
	"github.com/bwmarrin/snowflake"
	"go.uber.org/zap"
)

var Node *snowflake.Node

func getHostIp() string {
	addrList, err := net.InterfaceAddrs()
	if err != nil {
		fmt.Println("get current host ip err: ", err)
		return ""
	}
	var ip string
	for _, address := range addrList {
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ip = ipNet.IP.String()
				break
			}
		}
	}
	return ip
}

func Init(isDev bool) {
	ip := getHostIp()
	logger.Info("host ip", zap.String("ip", ip))
	vec := strings.Split(ip, ".")
	if len(vec) < 4 {
		panic("invalid ip")
	}
	id, _ := strconv.Atoi(vec[3])
	if id == 0 {
		panic("invalid node id")
	}
	if isDev {
		id = 357
	}
	node, err := snowflake.NewNode(int64(id))
	if err != nil {
		panic(err)
	}
	Node = node
}
