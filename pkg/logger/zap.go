package logger

import (
	"context"
	"fmt"
	"os"

	"git.hoxigames.xyz/movely/movely-server/config"
	"git.hoxigames.xyz/movely/movely-server/global"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	Logger        *zap.Logger
	SugaredLogger *zap.SugaredLogger
)

func Init() error {
	var logLevel logLevelStr
	debug := config.Configuration.Debug
	if config.Configuration.Debug {
		logLevel = LevelDebugStr
	} else {
		logLevel = logLevelStr(config.Configuration.Log.Level)
	}

	cfg := zap.NewProductionEncoderConfig()
	cfg.EncodeTime = zapcore.ISO8601TimeEncoder
	cfg.EncodeDuration = zapcore.StringDurationEncoder
	level, err := str2ZapLevel(logLevel)
	if err != nil {
		_, _ = fmt.Fprintf(os.Stdout, "zap init error, is %s", err.Error())
		return err
	}
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(cfg),
		zapcore.Lock(zapcore.AddSync(os.Stdout)),
		zap.NewAtomicLevelAt(level),
	)
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	SugaredLogger = Logger.Sugar()
	if !debug {
		global.SetCleanup(global.ZapLoggerCleanup, Logger.Sync)
	}

	return nil
}

type logLevelStr string

const (
	LevelDebugStr logLevelStr = "debug"
	LevelFatalStr logLevelStr = "fatal"
	LevelErrorStr logLevelStr = "error"
	LevelWarnStr  logLevelStr = "warn"
	LevelInfoStr  logLevelStr = "info"
)

func str2ZapLevel(level logLevelStr) (zapcore.Level, error) {
	l := zapcore.DebugLevel
	err := l.Set(string(level))
	return l, err
}

func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

func Infof(msg string, args ...interface{}) {
	SugaredLogger.Infof(msg, args...)
}

func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	// todo sentry
	Logger.Error(msg, fields...)
}

func Errorf(msg string, args ...interface{}) {
	// todo sentry
	SugaredLogger.Errorf(msg, args...)
}

func Fatal(msg string, fields ...zap.Field) {
	// todo sentry
	Logger.Fatal(msg, fields...)
}

type TraceIDKeyType string

var TraceIDKey TraceIDKeyType = "trace-id"

func InfoWithCtx(ctx context.Context, msg string, fields ...zap.Field) {
	traceId, ok := ctx.Value(TraceIDKey).(string)
	if !ok {
		traceId = ""
	}
	Logger.With(zap.String("traceId", traceId)).Info(msg, fields...)
}

func WarnWithCtx(ctx context.Context, msg string, fields ...zap.Field) {
	traceId, ok := ctx.Value(TraceIDKey).(string)
	if !ok {
		traceId = ""
	}
	Logger.With(zap.String("traceId", traceId)).Warn(msg, fields...)
}

func ErrorWithCtx(ctx context.Context, msg string, fields ...zap.Field) {
	traceId, ok := ctx.Value(TraceIDKey).(string)
	if !ok {
		traceId = ""
	}
	Logger.With(zap.String("traceId", traceId)).Error(msg, fields...)
}

func InfoWithTraceId(traceId string, msg string, fields ...zap.Field) {
	Logger.With(zap.String("traceId", traceId)).Info(msg, fields...)
}

func ErrorWithTraceId(traceId string, msg string, fields ...zap.Field) {
	Logger.With(zap.String("traceId", traceId)).Error(msg, fields...)
}

type AppCtx interface {
	GetTraceID() string
}

func ErrorWithAppCtx(ctx AppCtx, msg string, fields ...zap.Field) {
	Logger.With(zap.String("traceId", ctx.GetTraceID())).Error(msg, fields...)
}

func InfoWithAppCtx(ctx AppCtx, msg string, fields ...zap.Field) {
	Logger.With(zap.String("traceId", ctx.GetTraceID())).Info(msg, fields...)
}
