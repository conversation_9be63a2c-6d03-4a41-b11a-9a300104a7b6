package rdb

import (
	"context"
	"crypto/tls"
	"log"
	"os"
	"time"

	"git.hoxigames.xyz/movely/movely-server/config"
	"github.com/bsm/redislock"
	"github.com/hibiken/asynq"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	_ "gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	Rdb            *gorm.DB
	Redis          redis.UniversalClient
	RedisLock      *redislock.Client
	AsynqClient    *asynq.Client
	AsynqInspector *asynq.Inspector
)

func Init() (err error) {
	Rdb, err = initMysql(config.Configuration.Mysql.Dsn, config.Configuration.Mysql.MaxLifeTime,
		config.Configuration.Mysql.MaxIdleConn, config.Configuration.Mysql.MaxOpenConn)
	if err != nil {
		return err
	}
	Redis, err = initRds(config.Configuration.Redis.Addr, config.Configuration.Redis.Pwd,
		config.Configuration.Redis.PoolSize, config.Configuration.Redis.ClusterMode, config.Configuration.Redis.Tls)
	if err != nil {
		return err
	}
	AsynqClient = asynq.NewClientFromRedisClient(Redis)
	AsynqInspector = asynq.NewInspectorFromRedisClient(Redis)
	return
}

func initMysql(dsn string, maxLifeTime, maxIdleConn, maxOpenConn int) (*gorm.DB, error) {
	level := logger.Error
	if config.Configuration.Debug {
		level = logger.Info
	}
	client, err := gorm.Open(mysql.New(mysql.Config{DSN: dsn}), &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold: 10 * time.Millisecond,
				LogLevel:      level,
				Colorful:      true,
			},
		),
	})
	if err != nil {
		log.Fatal("mysql open", zap.String("dsn", dsn), zap.Error(err))
		return nil, err
	}
	db, err := client.DB()
	if err != nil {
		log.Fatal("mysql db", zap.String("dsn", dsn), zap.Error(err))
		return nil, err
	}
	db.SetMaxIdleConns(maxIdleConn)
	db.SetMaxOpenConns(maxOpenConn)
	db.SetConnMaxLifetime(time.Second * time.Duration(maxLifeTime))
	return client, nil
}

func initRds(addr, pwd string, poolSize int, cluster, useTls bool) (redis.UniversalClient, error) {
	cfg := &redis.UniversalOptions{
		Addrs:         []string{addr},
		Password:      pwd,
		PoolSize:      poolSize,
		IsClusterMode: cluster,
	}
	if useTls {
		cfg.TLSConfig = &tls.Config{
			InsecureSkipVerify: true,
		}
	}
	client := redis.NewUniversalClient(cfg)

	err := client.Ping(context.Background()).Err()
	if err != nil {
		return nil, err
	}
	RedisLock = redislock.New(client)
	return client, nil
}
