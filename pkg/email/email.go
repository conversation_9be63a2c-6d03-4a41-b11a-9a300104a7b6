package email

import (
	"bytes"
	"crypto/tls"
	"embed"
	"errors"
	"fmt"
	"html/template"
	"net/mail"

	"git.hoxigames.xyz/movely/movely-server/config"
	"github.com/k3a/html2text"
	"github.com/vanng822/go-premailer/premailer"
	"gopkg.in/gomail.v2"
)

type MailData struct {
	Code     string
	UserName string
	Subject  string
}

var (
	c config.EmailConfig

	//go:embed templates
	admin embed.FS

	verifyTpl *template.Template
)

type VerifyEmailType string

const (
	ChangePassword VerifyEmailType = "ChangePassword"
	UserActive     VerifyEmailType = "UserActive"
)

func SetUp(config config.EmailConfig) {
	c = config
	var err error
	if verifyTpl, err = template.ParseFS(admin, "**/*.html"); err != nil {
		panic(err)
	}
}

func ValidMailAddress(address string) (string, bool) {
	addr, err := mail.ParseAddress(address)
	if err != nil {
		return "", false
	}
	return addr.Address, true
}

func SendVerifyEmail(email string, data *MailData, tpl VerifyEmailType) error {
	from := c.EmailFrom
	smtpPass := c.SmtpPass
	smtpUser := c.SmtpUser
	to := email
	smtpHost := c.SmtpHost
	smtpPort := c.SmtpPort

	var body bytes.Buffer

	verifyTpl.ExecuteTemplate(&body, fmt.Sprintf("%s.html", tpl), &data)
	htmlString := body.String()
	prem, _ := premailer.NewPremailerFromString(htmlString, nil)
	htmlInline, err := prem.Transform()
	if err != nil {
		return err
	}

	m := gomail.NewMessage()
	m.SetHeader("From", from)
	m.SetHeader("To", to)
	m.SetHeader("Subject", data.Subject)
	m.SetBody("text/html", htmlInline)
	m.AddAlternative("text/plain", html2text.HTML2Text(body.String()))

	d := gomail.NewDialer(smtpHost, smtpPort, smtpUser, smtpPass)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	if err := d.DialAndSend(m); err != nil {
		return errors.New("send email error")
	}
	return nil
}
