package config

import (
	"fmt"
	"os"

	"github.com/jinzhu/configor"
)

type (
	Config struct {
		Debug             bool
		Env               string
		Srv               srv               `json:"srv"`
		Log               log               `json:"log"`
		Mysql             mysql             `json:"mysql"`
		Email             EmailConfig       `json:"email"`
		Redis             redis             `json:"redis"`
		Apple             AppleStoreConfig  `json:"apple"`
		Aws               aws               `json:"aws"`
		LanguageConf      LanguageConf      `json:"language"`
		TE                TEConfig          `json:"te"`
		MeilisearchConfig MeilisearchConfig `json:"meilisearch"`
		Stripe            StripeConfig      `json:"stripe"`
		PostProcServHost  string            `json:"post_proc_serv_host"`
		Resource          resource          `json:"resource"`
	}
	srv struct {
		Address string `json:"address" required:"true"`
	}
	log struct {
		Level string `json:"level" required:"true"`
	}
	mysql struct {
		Dsn         string `json:"dsn" required:"true"`
		MaxLifeTime int    `json:"max_life_time" required:"true"`
		MaxIdleConn int    `json:"max_idle_conn" required:"true"`
		MaxOpenConn int    `json:"max_open_conn" required:"true"`
	}
	redis struct {
		Addr        string `json:"addr" required:"true"`
		Pwd         string `json:"pwd"`
		PoolSize    int    `json:"pool_size" required:"true"`
		ClusterMode bool   `json:"cluster_mode"`
		Tls         bool   `json:"tls"`
	}

	AppleStoreConfig struct {
		KeyID      string `json:"key_id"`
		KeyContent string `json:"key_content"`
		BundleID   string `json:"bundle_id"`
		Issuer     string `json:"issuer"`
		Sandbox    bool   `json:"sandbox"`
	}
	aws struct {
		S3 struct {
			Srv s3 `json:"srv" required:"true"`
		} `json:"s3"`
		Cdn struct {
			Srv cdn `json:"srv" required:"true"`
		} `json:"cdn"`
	}
	s3 struct {
		Domain          string `json:"domain" required:"true"`
		Region          string `json:"region" required:"true"`
		AccessKey       string `json:"accessKey" required:"true"`
		SecretAccessKey string `json:"secretAccessKey" required:"true"`
	}
	cdn struct {
		Domain string `json:"domain" required:"true"`
	}

	TEConfig struct {
		Directory string `json:"directory"`
		Prefix    string `json:"prefix"`
		UseLogBus bool   `json:"use_log_bus"`
		UseBatch  bool   `json:"use_batch"`
		Token     string `json:"token"`
	}

	EmailConfig struct {
		EmailFrom string `json:"email_from"`
		SmtpPass  string `json:"smtp_pass"`
		SmtpUser  string `json:"smtp_user"`
		SmtpHost  string `json:"smtp_host"`
		SmtpPort  int    `json:"smtp_port"`
	}

	LanguageConf struct {
		Bucket string `json:"bucket" required:"true"`
		Key    string `json:"key" required:"true"`
	}

	MeilisearchConfig struct {
		Host      string `json:"host" required:"true"`
		MasterKey string `json:"master_key" required:"true"`
		Index     string `json:"index" required:"true"`
	}

	StripeConfig struct {
		SecretKey     string `json:"secret_key" required:"true"`
		WebhookSecret string `json:"webhook_secret" required:"true"` // 如果是本地，要填写自己的 webhook secret
	}
	resource struct {
		MaxSize int64 `json:"max_size" required:"true"`
	}
)

const (
	envKey    = "ENVIRON"
	envDev    = "dev"
	envBeat   = "beta"
	envOnline = "online"

	path   = "./etc/app/"
	suffix = ".json"
)

var (
	Configuration *Config
)

func Init(configFile string) error {
	Env := os.Getenv(envKey)
	if Env == "" {
		panic(fmt.Errorf("environment key `%s` not set", envKey))
	}

	if configFile == "" {
		switch Env {
		case envOnline, envBeat:
			configFile = Env
		default:
			configFile = envDev
		}
		configFile = path + configFile + suffix
	}

	Configuration = &Config{
		Env: Env,
	}
	err := configor.Load(Configuration, configFile)
	if err != nil {
		_, _ = fmt.Fprintf(os.Stdout, "load config error, is %s", err.Error())
		return err
	}
	if Env != envOnline {
		Configuration.Debug = true
	}

	return nil
}

func IsDev() bool {
	return Configuration.Env == envDev
}
