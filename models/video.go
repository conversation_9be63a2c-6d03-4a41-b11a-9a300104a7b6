package models

type (
	VideoTrack struct {
		GenerateApi                 string `json:"generate_api"`
		InputProcessMethod          string `json:"input_process_method"`
		TemplateSexyTag             string `json:"template_sexy_tag"`
		VideoOriPhotoIdAfterprocess string `json:"video_ori_photo_id_afterprocess"`
		AbtestMachine               int    `json:"abtest_machine"`
		AbtestLoraName              string `json:"abtest_lora_name"`
		VideoDuration               string `json:"video_duration"`
		TokenType                   string `json:"token_type"`
		TokenNum                    int64  `json:"token_num"`
		CreditNum                   int64  `json:"credit_num"`
	}
	VideoTask struct {
		Id                  string `json:"id"`
		Status              int32  `json:"status"` // -1:排队 0:默认 1:创建 2:成功 3:失败 4:转存 5:取消
		CanBeCancelled      bool   `json:"can_be_cancelled"`
		Progress            int32  `json:"progress"`
		Msg                 string `json:"msg"`
		IsExtend            bool   `json:"is_extend"`
		ExpectedCostSeconds int32  `json:"expected_cost_seconds"`
	}
	VideoAsset struct {
		Id          string `json:"id,omitempty"`
		VideoUrl    string `json:"video_url,omitempty"`
		VideoWidth  int    `json:"video_width,omitempty"`
		VideoHeight int    `json:"video_height,omitempty"`
		ImageUrl    string `json:"image_url,omitempty"`
	}
)

type VideoCreateReq struct {
	GenerateType        string `json:"generate_type"`
	Duration            string `json:"duration"`        // 5 | 10
	PhotoWithEdit       bool   `json:"photo_with_edit"` // inpaint video
	IsReGenerate        bool   `json:"is_re_generate"`
	IsFree              bool   `json:"is_free"`
	Faceid              string `json:"faceid"`
	OriginPicUrl        string `json:"origin_pic_url"`
	CustomPrompt        string `json:"custom_prompt"`
	TaskId              string `json:"task_id"`
	OriginVideoUrl      string `json:"origin_video_url"`       // 原视频
	BodyFirstFrameImage string `json:"body_first_frame_image"` // 首帧图片
	BodyFirstFrameMask  string `json:"body_first_frame_mask"`  // 首帧mask 图片
}

type VideoCreateResp struct {
	Task *VideoTask `json:"task"`
}

type VideoQueryReq struct {
	TaskId string `json:"task_id"`
}

type VideoQueryResp struct {
	Task    *VideoTask    `json:"task,omitempty"`
	Results []*VideoAsset `json:"results,omitempty"`
	Track   *VideoTrack   `json:"track,omitempty"`
}
