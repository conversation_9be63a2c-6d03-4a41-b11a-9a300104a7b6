package models

type UserTemplateInfo struct {
	IsLike bool `json:"is_like"`
}

type FeedsTag struct {
	Id      string `json:"tag_key"`
	Name    string `json:"name"`
	TagType string `json:"tag_type"`
}

type VideoEntry struct {
	VideoType      string           `json:"video_type"`
	GenerateType   string           `json:"generate_type"`
	Title          string           `json:"title"`
	TitleEnName    string           `json:"title_en_name"`
	ImageUrl       string           `json:"image_url"`
	VideoUrl       string           `json:"video_url"`
	VideoMediumUrl string           `json:"video_medium_url"`
	VideoLowUrl    string           `json:"video_low_url"`
	Blurhash       string           `json:"blurhash"`
	VideoWidth     int              `json:"video_width"`
	VideoHeight    int              `json:"video_height"`
	Autoplay       bool             `json:"autoplay"`
	MasterTemplate string           `json:"master_template"`
	TemplateLevel  int32            `json:"template_level"`
	FreeTrial      bool             `json:"free_trial"`
	LikeCount      int64            `json:"like_count"`
	BaseLikeCount  int64            `json:"base_like_count"`
	ShareCount     int64            `json:"share_count"`
	UserInfo       UserTemplateInfo `json:"user_template_info"`
	Thirdparty     string           `json:"thirdparty"`
}
